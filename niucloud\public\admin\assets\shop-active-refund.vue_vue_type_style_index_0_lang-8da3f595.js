import{d as U,r as m,n as q,h as p,s as z,w as a,a as n,e as d,i as E,t as i,u as r,q as _,Z as Y,c as v,C as F,B as Z,$ as j,a6 as H,b4 as J,a$ as K,M as O,ag as Q,aY as W,ah as X,L as ee,N as oe,E as te,V as se,a3 as ne}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                        *//* empty css               */import"./el-form-item-4ed993c7.js";/* empty css                 */import{w as le}from"./order-caf8547a.js";const ae=n("p",null,"商家主动退款功能仅作为退款售后业务的补充功能，请勿过度依赖和使用。此功能支持多次退款操作。",-1),de=n("p",null,"如果订单项全部退款，运费将在最后一次退款时一并退还，同时使用的优惠券也将返还给用户，退款金额将原路返还至用户的支付账户。",-1),re={class:"text-[red]"},_e={key:0,class:"text-[#999] text-[24rpx]"},ie={class:"flex cursor-pointer"},ue={class:"flex items-center min-w-[50px] mr-[10px]"},ce=["src"],fe={key:1,class:"w-[50px] h-[50px]",src:"",alt:""},me={class:"flex flex-col items-start"},pe={class:"max-w-[250px]"},ye={class:"multi-hidden text-[14px]"},xe={class:"text-[12px] text-[#999] truncate"},ve={key:0,class:"px-[4px] text-[12px] text-[#fff] rounded-[4px] bg-primary leading-[18px]"},he={class:"flex flex-col"},ge={class:"text-[13px]"},be={class:"flex flex-col"},ke={class:"text-[13px]"},we={class:"flex flex-col"},Ee={class:"text-[13px] mt-[5px]"},Fe={class:"dialog-footer"},Ye=U({__name:"shop-active-refund",emits:["complete"],setup(Ve,{expose:V,emit:D}){const y=m(!1),c=m(!1),T={order_goods_ids:[],refund_money:0,shop_active_refund_remark:""},g=m([]),b=m(null),e=q({...T}),k=m();let h=0,f=m(!1);const C=(s,o)=>{e.refund_money=0,f.value=!1,e.order_goods_ids=[],s.forEach((l,u)=>{e.refund_money+=l.refund_money,e.order_goods_ids.push(l.order_goods_id)}),s.length==h&&(e.refund_money+=parseFloat(e.delivery_money),f.value=!0),e.refund_money=e.refund_money.toFixed(2)},N=s=>{e.refund_money=0,f.value=!1,e.order_goods_ids=[],s.forEach((o,l)=>{e.refund_money+=o.refund_money,e.order_goods_ids.push(o.order_goods_id)}),s.length==h&&(e.refund_money+=parseFloat(e.delivery_money),f.value=!0),e.refund_money=e.refund_money.toFixed(2)},S=(s,o)=>{let l=!1;return s.status==1&&(l=!0),s.is_gift==1&&(l=!1),l},A=async s=>{if(!(c.value||!s)){if(e.order_goods_ids.length<=0){j({message:_("refundGoodsPlaceholder"),type:"warning"});return}await s.validate(async o=>{if(o){c.value=!0;const l=H(e);l.shop_active_refund_money=e.refund_money,delete l.refund_money,le(l).then(u=>{c.value=!1,y.value=!1,D("complete"),I()}).catch(u=>{})}})}},B=async(s=null)=>{c.value=!0,s&&(h=0,e.refund_money=0,e.order_goods_ids=[],g.value=s.order_goods,g.value.forEach((o,l,u)=>{u[l].refund_money=o.goods_money-o.discount_money,o.status==1&&(h++,e.refund_money+=u[l].refund_money,e.order_goods_ids.push(o.order_goods_id))}),e.delivery_money=s.delivery_money,e.refund_money+=parseFloat(s.delivery_money),f.value=!0,e.refund_money=e.refund_money.toFixed(2),e.shop_active_refund_remark=""),J(()=>{setTimeout(()=>{b.value.toggleAllSelection()},100)}),c.value=!1},I=()=>{e.order_goods_id=0,e.refund_money=0,e.shop_active_refund_status="partial_refund",e.shop_active_money_type="back_refund",e.shop_active_refund_remark=""};return V({showDialog:y,setFormData:B}),(s,o)=>{const l=K,u=O,x=Q,R=W,G=X,M=ee,P=oe,w=te,$=se,L=ne;return p(),z($,{modelValue:y.value,"onUpdate:modelValue":o[3]||(o[3]=t=>y.value=t),title:r(_)("refundTitle"),width:"800px",class:"diy-dialog-wrap","destroy-on-close":!0},{footer:a(()=>[n("span",Fe,[d(w,{onClick:o[1]||(o[1]=t=>y.value=!1)},{default:a(()=>[E(i(r(_)("cancel")),1)]),_:1}),d(w,{type:"primary",loading:c.value,onClick:o[2]||(o[2]=t=>A(k.value))},{default:a(()=>[E(i(r(_)("confirm")),1)]),_:1},8,["loading"])])]),default:a(()=>[Y((p(),v("div",null,[d(l,{type:"warning",closable:!1,class:"!mb-[10px]"},{default:a(()=>[ae,de]),_:1}),d(P,{model:e,"label-width":"100px",ref_key:"formRef",ref:k,class:"page-form mb-[30px]"},{default:a(()=>[d(u,{label:r(_)("refundMoney")},{default:a(()=>[n("div",re,[n("span",null,"￥"+i(e.refund_money),1),r(f)?(p(),v("span",_e,"（运费:￥"+i(e.delivery_money)+"）",1)):F("",!0)])]),_:1},8,["label"]),d(u,{label:r(_)("refundGoodsItem")},{default:a(()=>[d(G,{data:g.value,size:"large",ref_key:"refundTableRef",ref:b,onSelect:C,onSelectAll:N},{default:a(()=>[d(x,{type:"selection",width:"40",selectable:S}),d(x,{align:"left","min-width":"200",label:r(_)("refundGoodsInfo")},{default:a(({row:t})=>[n("div",ie,[n("div",ue,[t.goods_image?(p(),v("img",{key:0,class:"w-[50px] h-[50px]",src:r(Z)(t.goods_image),alt:""},null,8,ce)):(p(),v("img",fe))]),n("div",me,[d(R,{class:"box-item",effect:"light",placement:"top"},{content:a(()=>[n("div",pe,i(t.goods_name),1)]),default:a(()=>[n("p",ye,i(t.goods_name),1)]),_:2},1024),n("span",xe,i(t.sku_name),1),t.is_gift==1?(p(),v("span",ve,"赠品")):F("",!0)])])]),_:1},8,["label"]),d(x,{"min-width":"60",label:r(_)("refundPayPrice")},{default:a(({row:t})=>[n("div",he,[n("span",ge,"￥"+i(t.refund_money),1)])]),_:1},8,["label"]),d(x,{"min-width":"80",label:r(_)("refundGoodsPrice")},{default:a(({row:t})=>[n("div",be,[n("span",ke,"￥"+i(t.price),1)])]),_:1},8,["label"]),d(x,{"min-width":"60",label:r(_)("refundGoodsNum")},{default:a(({row:t})=>[n("div",we,[n("span",Ee,i(t.num)+i(r(_)("piece")),1)])]),_:1},8,["label"])]),_:1},8,["data"])]),_:1},8,["label"]),d(u,{label:r(_)("refundInstructions"),prop:"shop_active_refund_remark"},{default:a(()=>[d(M,{modelValue:e.shop_active_refund_remark,"onUpdate:modelValue":o[0]||(o[0]=t=>e.shop_active_refund_remark=t),modelModifiers:{trim:!0},rows:5,type:"textarea",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])])),[[L,c.value]])]),_:1},8,["modelValue","title"])}}});export{Ye as _};
