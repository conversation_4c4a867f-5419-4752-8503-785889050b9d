import{d as te,r as g,l as F,n as oe,q as m,Z as re,h as b,s as N,w as r,c as C,a as n,e as a,u as d,i as h,C as $,t as y,F as M,W as A,af as se,L as ie,M as ne,E as ue,N as de,au as pe,av as me,a9 as ce,a3 as ge}from"./index-30109030.js";/* empty css                   *//* empty css                */import ve from"./index-e3ceb692.js";/* empty css                       *//* empty css                 *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                 */import{l as fe,c as he,r as be,d as _e,f as D,h as we}from"./notice-9865bf12.js";/* empty css                       *//* empty css                   */const xe={key:0},ye=n("span",{class:""},"还未注册牛云短信?",-1),Ve={key:1},ke={class:"bg-[var(--el-color-primary-light-9)] p-2 text-[14px] rounded-[6px]"},Ce=n("span",{class:""},"已有账号，",-1),Ue={class:"panel-title !text-[14px]"},Ee=n("div",{class:"mb-[10px] text-[12px] ml-[150px] text-[#999] leading-[20px]"},"子账户用户名，仅支持6~50位英文+数字组合",-1),Se={class:"flex items-center"},qe=["src"],Fe={class:"flex items-center"},Ne=n("div",{class:"mb-[10px] text-[12px] ml-[150px] text-[#999] leading-[20px]"},"密码由数字、大小写字母组成，密码长度8-16位",-1),$e=n("div",{class:"ml-[150px] text-[12px] text-[#999] leading-[20px]"},"必须由【】包裹，例如：【test】",-1),Re=n("div",{class:"my-[5px] ml-[150px] text-[12px] text-[#999] leading-[20px]"},"字数要求在2-20个字符，不能使用空格和特殊符号“ - + = * & % # @ ~等;",-1),Be={class:"panel-title !text-[14px"},Ie=n("div",{class:"ml-[150px] text-[12px] text-[#999] leading-[20px]"},"当签名来源为商标、APP、小程序、事业单位简称或企业名称简称时，需必填此字段",-1),Le=n("div",{class:"my-[5px] ml-[150px] text-[12px] text-[#999] leading-[20px]"},"当签名来源为事业单位全称或企业名称全称时，选填此字段。",-1),Te={class:"fixed-footer-wrap"},Pe={class:"fixed-footer"},Me={key:2},Ae=n("div",{class:"bg-[var(--el-color-primary-light-9)] p-2 text-[14px] rounded-[6px]"},[n("span",{class:"text-primary"},"忘记密码，快去修改")],-1),De={class:"flex items-center"},Ze=["src"],ze={class:"flex items-center"},ol=te({__name:"sms_niu_login",props:{info:{type:Object,default:()=>({})},isLogin:{type:Boolean,default:!1}},emits:["complete"],setup(Z,{emit:U}){const E=Z,V=g(!1),I=g(),c=g("login"),w=g({username:"",password:""});F(()=>!!E.info&&Object.keys(E.info).length>0);const z=F(()=>({username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]})),j=async()=>{var s;await((s=I.value)==null?void 0:s.validate(async e=>{e&&fe(w.value).then(o=>{U("complete")})}))},O=()=>{U("complete")},S=oe({signTypeList:[],signSourceList:[]});(()=>{he().then(s=>{S.signTypeList=s.data.sign_type_list,S.signSourceList=s.data.sign_source_list,t.value.signSource=s.data.sign_source_list[0].type,t.value.signType=s.data.sign_type_list[0].type})})();const t=g({code:"",key:"",remark:"",username:"",password:"",company:"",mobile:"",captcha_key:"",captcha_code:"",captcha_img:"",imgUrl:"",contentExample:"",signType:"",signSource:"",principalIdCard:"",principalName:"",principalMobile:"",legalPerson:"",creditCode:"",companyName:"",signature:""}),q=g("login"),G=async()=>{q.value="register",V.value=!0,await _()&&(t.value.username="",t.value.password="",c.value="register"),V.value=!1},R=g(),W=F(()=>({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{pattern:/^[A-Za-z0-9]{6,50}$/,message:"用户名格式不正确",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{pattern:/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[A-Za-z\d]{8,16}$/,message:"密码格式不正确",trigger:"blur"}],mobile:[{required:!0,message:"请输入手机号",trigger:"blur"}],captcha_code:[{required:!0,message:"请输入验证码",trigger:"blur"}],code:[{required:!0,message:"请输入动态码",trigger:"blur"}],company:[{required:!0,message:"请输入公司名称",trigger:"blur"}],signature:[{required:!0,message:"请输入短信签名",trigger:"blur"},{validator:(s,e,o)=>{if(!/^【[^【】]*】$/.test(e))return o(new Error("短信签名必须被【】包裹"));const p=e.slice(1,-1);if(!(p.length>=2&&p.length<=20))return o(new Error("短信签名内容需在 2-20 个字符之间"));if(/[\s\-+=*&%#@~;]/.test(p))return o(new Error("短信签名不能包含空格或特殊字符 - + = * & % # @ ~ ;"));o()},trigger:"blur"}],principalMobile:[{required:!0,message:"请输入经办人手机号",trigger:"blur"},{validator:H,trigger:"blur"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur"}],contentExample:[{required:!0,message:"请输入短信示例内容",trigger:"blur"}],creditCode:[{required:!0,message:"请输入社会统一信用代码",trigger:"blur"}],legalPerson:[{required:!0,message:"请输入法人姓名",trigger:"blur"}],principalName:[{required:!0,message:"请输入经办人姓名",trigger:"blur"}],principalIdCard:[{required:!0,message:"请输入经办人身份证",trigger:"blur"},{validator:X,trigger:"blur"}],imgUrl:[{validator:(s,e,o)=>{([3,4,5].includes(t.value.signSource)||t.value.signType==1)&&(!e||e.length===0)?o(new Error("请上传图片")):o()},trigger:"blur"}]})),X=(s,e,o)=>{e&&!/^[1-9]\d{5}(19|20)\d{2}((0\d)|(1[0-2]))(([0-2]\d)|3[0-1])\d{3}([0-9Xx])$/.test(e)?o(new Error(m("请输入正确的身份证号码"))):o()},H=(s,e,o)=>{e&&!/^1[3-9]\d{9}$/.test(e)?o(new Error(m("请输入正确的手机号码"))):o()},J=async()=>{var s;await((s=R.value)==null?void 0:s.validate(async e=>{if(e){const{captcha_key:o,captcha_code:i,captcha_img:p,...x}=t.value;be(x).then(k=>{c.value="login"}).catch(k=>{_()})}}))},B=g(),u=g({mobile:"",captcha_key:"",captcha_code:"",captcha_img:"",code:"",key:""}),_=async()=>{try{const s=await _e();return q.value==="register"?(t.value.captcha_key=s.data.captcha_key,t.value.captcha_img=s.data.img):q.value==="password"&&(u.value.captcha_key=s.data.captcha_key,u.value.captcha_img=s.data.img),!0}catch(s){return console.error("获取验证码失败",s),!1}},v=g(!1),f=g(0),L=()=>{f.value>0||v.value||(c.value==="register"?R.value.validateField(["mobile","captcha_code"],s=>{if(!s)return;v.value=!0;const e={mobile:t.value.mobile,captcha_key:t.value.captcha_key,captcha_code:t.value.captcha_code};D(e).then(o=>{T(60),t.value.key=o.data.key}).catch(o=>{_(),v.value=!1}).finally(()=>{v.value=!1})}):c.value==="password"&&B.value.validateField(["mobile","captcha_code"],s=>{if(!s)return;v.value=!0;const e={mobile:u.value.mobile,captcha_key:u.value.captcha_key,captcha_code:u.value.captcha_code};D(e).then(o=>{T(60),u.value.key=o.data.key}).catch(o=>{_(),v.value=!1}).finally(()=>{v.value=!1})}))},T=s=>{f.value=s;const e=setInterval(()=>{f.value--,f.value<=0&&(clearInterval(e),v.value=!1)},1e3)},K=F(()=>({mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:["blur","change"]}],captcha_code:[{required:!0,message:"请输入验证码",trigger:"blur"}],code:[{required:!0,message:"请输入动态码",trigger:"blur"}]})),Q=async()=>{V.value=!0,q.value="password",await _()&&(c.value="password"),V.value=!1},Y=async()=>{var s;await((s=B.value)==null?void 0:s.validate(async e=>{if(e){let o={key:u.value.key,code:u.value.code,mobile:u.value.mobile};we(E.info.username,{...o}).then(i=>{let p=i.data.password;se.confirm(`新密码为：${p}`,"请保存好新密码",{confirmButtonText:"确定",showCancelButton:!1}).then(()=>{c.value="login",U("complete")}).catch(()=>{c.value="login",U("complete")})})}}))};return(s,e)=>{const o=ie,i=ne,p=ue,x=de,k=pe,P=me,ee=ve,le=ce,ae=ge;return re((b(),N(le,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[c.value=="login"?(b(),C("div",xe,[n("div",{class:"bg-[var(--el-color-primary-light-9)] p-2 text-[14px] rounded-[6px]"},[ye,n("span",{onClick:G,class:"cursor-pointer text-primary"},"去注册")]),a(x,{model:w.value,"label-width":"150px",ref_key:"formRef",ref:I,rules:d(z),class:"page-form mt-[20px]"},{default:r(()=>[a(i,{label:"用户名",prop:"username"},{default:r(()=>[a(o,{placeholder:"请输入用户名",class:"input-width",modelValue:w.value.username,"onUpdate:modelValue":e[0]||(e[0]=l=>w.value.username=l),clearable:"",maxlength:"50",autocomplete:"off"},null,8,["modelValue"])]),_:1}),a(i,{label:"密码",prop:"password"},{default:r(()=>[a(o,{placeholder:"请输入密码",type:"password","show-password":"","show-word-limit":"",maxlength:"16",class:"input-width",autocomplete:"new-password",modelValue:w.value.password,"onUpdate:modelValue":e[1]||(e[1]=l=>w.value.password=l),clearable:""},null,8,["modelValue"])]),_:1}),a(i,null,{default:r(()=>[a(p,{type:"primary",onClick:j},{default:r(()=>[h("登录")]),_:1}),a(p,{onClick:e[2]||(e[2]=l=>Q())},{default:r(()=>[h("忘记密码")]),_:1}),E.isLogin?(b(),N(p,{key:0,onClick:e[3]||(e[3]=l=>O())},{default:r(()=>[h("返回")]),_:1})):$("",!0)]),_:1})]),_:1},8,["model","rules"])])):$("",!0),c.value=="register"?(b(),C("div",Ve,[n("div",ke,[Ce,n("span",{onClick:e[4]||(e[4]=l=>c.value="login"),class:"cursor-pointer text-primary"},"去登录")]),a(x,{model:t.value,"label-width":"150px",ref_key:"registerFormRef",ref:R,rules:d(W),class:"page-form mt-[20px]"},{default:r(()=>[n("h3",Ue,y(d(m)("基本信息")),1),a(i,{label:"用户名",prop:"username"},{default:r(()=>[a(o,{placeholder:"请输入用户名",class:"input-width",autocomplete:"off",maxlength:"50","show-word-limit":"",modelValue:t.value.username,"onUpdate:modelValue":e[5]||(e[5]=l=>t.value.username=l),clearable:""},null,8,["modelValue"])]),_:1}),Ee,a(i,{label:"公司名称",prop:"company"},{default:r(()=>[a(o,{placeholder:"请输入公司名称",class:"input-width",maxlength:"50","show-word-limit":"",modelValue:t.value.company,"onUpdate:modelValue":e[6]||(e[6]=l=>t.value.company=l),clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"手机号",prop:"mobile"},{default:r(()=>[a(o,{placeholder:"请输入手机号",class:"input-width",maxlength:"11","show-word-limit":"",modelValue:t.value.mobile,"onUpdate:modelValue":e[7]||(e[7]=l=>t.value.mobile=l),clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"验证码",prop:"captcha_code"},{default:r(()=>[n("div",Se,[a(o,{placeholder:"请输入验证码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:t.value.captcha_code,"onUpdate:modelValue":e[8]||(e[8]=l=>t.value.captcha_code=l),clearable:""},null,8,["modelValue"]),n("img",{src:t.value.captcha_img,alt:"验证码",class:"w-[100px] h-[32px] cursor-pointer ml-[10px]",onClick:_},null,8,qe)])]),_:1}),a(i,{label:"动态码",prop:"code"},{default:r(()=>[n("div",Fe,[a(o,{placeholder:"请输入动态码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:t.value.code,"onUpdate:modelValue":e[9]||(e[9]=l=>t.value.code=l),clearable:""},null,8,["modelValue"]),a(p,{class:"ml-[10px]",onClick:L,disabled:f.value>0,loading:v.value},{default:r(()=>[h(y(f.value>0?`${f.value}秒后重新获取`:"获取动态码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),a(i,{label:"初始密码",prop:"password"},{default:r(()=>[a(o,{placeholder:"请输入初始密码",type:"password",autocomplete:"new-password",maxlength:"16","show-password":"",class:"input-width",modelValue:t.value.password,"onUpdate:modelValue":e[10]||(e[10]=l=>t.value.password=l),clearable:""},null,8,["modelValue"])]),_:1}),Ne,a(i,{label:d(m)("默认签名"),prop:"signature"},{default:r(()=>[a(o,{modelValue:t.value.signature,"onUpdate:modelValue":e[11]||(e[11]=l=>t.value.signature=l),placeholder:"请输入默认签名",class:"input-width",maxlength:"20","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),$e,Re,a(i,{label:"备注",prop:"remark"},{default:r(()=>[a(o,{placeholder:"请输入备注",class:"input-width",type:"textarea",maxlength:"50","show-word-limit":"",modelValue:t.value.remark,"onUpdate:modelValue":e[12]||(e[12]=l=>t.value.remark=l),clearable:""},null,8,["modelValue"])]),_:1}),n("h3",Be,y(d(m)("实名信息")),1),a(i,{label:d(m)("短信示例内容"),prop:"contentExample"},{default:r(()=>[a(o,{modelValue:t.value.contentExample,"onUpdate:modelValue":e[13]||(e[13]=l=>t.value.contentExample=l),placeholder:"请输入短信示例内容",clearable:"",maxlength:"50","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("企业名称"),prop:"companyName"},{default:r(()=>[a(o,{modelValue:t.value.companyName,"onUpdate:modelValue":e[14]||(e[14]=l=>t.value.companyName=l),placeholder:"请输入企业名称",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("社会统一信用代码"),prop:"creditCode"},{default:r(()=>[a(o,{modelValue:t.value.creditCode,"onUpdate:modelValue":e[15]||(e[15]=l=>t.value.creditCode=l),placeholder:"请输入社会统一信用代码",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("法人姓名"),prop:"legalPerson"},{default:r(()=>[a(o,{modelValue:t.value.legalPerson,"onUpdate:modelValue":e[16]||(e[16]=l=>t.value.legalPerson=l),placeholder:"请输入法人姓名",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("经办人姓名"),prop:"principalName"},{default:r(()=>[a(o,{modelValue:t.value.principalName,"onUpdate:modelValue":e[17]||(e[17]=l=>t.value.principalName=l),placeholder:"请输入经办人姓名",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("经办人手机号"),prop:"principalMobile"},{default:r(()=>[a(o,{modelValue:t.value.principalMobile,"onUpdate:modelValue":e[18]||(e[18]=l=>t.value.principalMobile=l),placeholder:"请输入经办人手机号",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("经办人身份证"),prop:"principalIdCard"},{default:r(()=>[a(o,{modelValue:t.value.principalIdCard,"onUpdate:modelValue":e[19]||(e[19]=l=>t.value.principalIdCard=l),placeholder:"请输入经办人身份证",clearable:"",maxlength:"18","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("签名来源")},{default:r(()=>[a(P,{modelValue:t.value.signSource,"onUpdate:modelValue":e[20]||(e[20]=l=>t.value.signSource=l)},{default:r(()=>[(b(!0),C(M,null,A(S.signSourceList,l=>(b(),N(k,{key:l.type,label:l.type},{default:r(()=>[h(y(l.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("签名类型")},{default:r(()=>[a(P,{modelValue:t.value.signType,"onUpdate:modelValue":e[21]||(e[21]=l=>t.value.signType=l)},{default:r(()=>[(b(!0),C(M,null,A(S.signTypeList,l=>(b(),N(k,{key:l.type,label:l.type},{default:r(()=>[h(y(l.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),a(i,{label:d(m)("上传图片"),prop:"imgUrl"},{default:r(()=>[a(ee,{modelValue:t.value.imgUrl,"onUpdate:modelValue":e[22]||(e[22]=l=>t.value.imgUrl=l),limit:1},null,8,["modelValue"])]),_:1},8,["label"]),Ie,Le]),_:1},8,["model","rules"]),n("div",Te,[n("div",Pe,[a(p,{type:"primary",onClick:J},{default:r(()=>[h("注册")]),_:1})])])])):$("",!0),c.value=="password"?(b(),C("div",Me,[Ae,a(x,{model:u.value,"label-width":"150px",ref_key:"changeFormRef",ref:B,rules:d(K),class:"page-form mt-[20px]"},{default:r(()=>[a(i,{label:"手机号",prop:"mobile"},{default:r(()=>[a(o,{placeholder:"请输入手机号",class:"input-width",maxlength:"11","show-word-limit":"",modelValue:u.value.mobile,"onUpdate:modelValue":e[23]||(e[23]=l=>u.value.mobile=l),clearable:""},null,8,["modelValue"])]),_:1}),a(i,{label:"验证码",prop:"captcha_code"},{default:r(()=>[n("div",De,[a(o,{placeholder:"请输入验证码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:u.value.captcha_code,"onUpdate:modelValue":e[24]||(e[24]=l=>u.value.captcha_code=l),clearable:""},null,8,["modelValue"]),n("img",{src:u.value.captcha_img,alt:"验证码",class:"w-[100px] h-[32px] cursor-pointer ml-[10px]",onClick:_},null,8,Ze)])]),_:1}),a(i,{label:"动态码",prop:"code"},{default:r(()=>[n("div",ze,[a(o,{placeholder:"请输入动态码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:u.value.code,"onUpdate:modelValue":e[25]||(e[25]=l=>u.value.code=l),clearable:""},null,8,["modelValue"]),a(p,{class:"ml-[10px]",onClick:L,disabled:f.value>0,loading:v.value},{default:r(()=>[h(y(f.value>0?`${f.value}秒后重新获取`:"获取动态码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),a(i,null,{default:r(()=>[a(p,{type:"primary",onClick:e[26]||(e[26]=l=>Y())},{default:r(()=>[h("重置密码")]),_:1}),a(p,{onClick:e[27]||(e[27]=l=>c.value="login")},{default:r(()=>[h("返回")]),_:1})]),_:1})]),_:1},8,["model","rules"])])):$("",!0)]),_:1})),[[ae,V.value]])}}});export{ol as _};
