const e="基础设置",o="字段设置",t="页面设置",a="生成配置",l="关联设置",c="表名称",n="描述",s="所属插件",d="模块名",r="类名",i="编辑方式",m="创建时间",h="更新时间",P="弹出",u="新页面",y="请选择",p="请输入表名",T="请输入描述",f="请选择插件",b="/",N="请输入模块名",V="请输入类名",g="请选择编辑方式",C="请输入字段名",S="请输入字段描述",x="表名不符合规范，请以 字母、_ 开头，不能出现 字母、数字、下划线 以外的字符",w="不符合规范，请以 字母、_ 开头，不能出现 字母、数字、下划线 以外的字符",D="导入数据表",A="编辑代码生成",v="确定要删除吗？",L="表描述",F="创建时间",k="修改时间",K="添加",M="请输入表名或表描述搜索",I="确认导入该数据表吗？",U="下载",B="常用字段",E="公用字段",q="字段列名",R="字段描述",_="字段类型",O="字段属性",Q="添加编辑",W="列表查询",j="主键",z="必填",G="添加",H="编辑",J="列表",X="搜索",Y="查询",Z="搜索方式",$="表单类型",ee="验证类型",oe="文本框",te="文本域",ae="下拉框",le="单选框",ce="复选框",ne="日期",se="图片上传",de="文件上传",re="视频上传",ie="富文本",me="数字框",he="主键",Pe="状态",ue="字符串",ye="图片",pe="单选",Te="复选",fe="下拉选择",be="富文本",Ne="日期",Ve="表单验证",ge="删除类型",Ce="物理删除",Se="软删除",xe="删除字段",we="请选择删除字段",De="排序字段",Ae="请选择排序字段",ve="排序方式",Le="请选择排序方式",Fe="上级菜单",ke="自动构建",Ke="手动添加",Me="控制器目录",Ie="模型目录",Ue="验证器目录",Be="WEB端视图目录",Ee="路由目录",qe="新增关联",Re="关联类型",_e="请选择关联类型",Oe="一对一",Qe="一对多",We="关联方法名称",je="请输入关联方法名称",ze="关联模型",Ge="请选择关联模型",He="关联键",Je="请选择关联键",Xe="外键",Ye="请输入外键",Ze="关联应用",$e="请选择应用",eo="同步代码",oo="下载代码",to="同步的代码与项目产生冲突，是否确认覆盖?",ao="同步的代码会加入到项目代码中，是否确认继续",lo="手机号验证",co="整数验证",no="身份证验证",so="邮箱验证",ro="最大输入120个字符",io="最小输入1个字符",mo="最大输入字符",ho="最小输入字符",Po="最小输入字符不能为空",uo="最小输入字符不可大于最大输入字符",yo="最大字输入字符不能为空",po="最大输入字符不可小于最小输入字符",To="最大输入数",fo="最小输入数",bo="最小输入数不能为空",No="最小输入数不可大于最大输入数",Vo="最大字输入数不能为空",go="最大输入数不可小于最小输入数",Co="输入字符区间",So="设置",xo="数据字典",wo="请选择数据字典",Do="部分字段未选择数据字典",Ao="远程下拉",vo="远程下拉value字段",Lo="请选择远程下拉value字段",Fo="远程下拉标题字段",ko="请选择远程下拉label字段",Ko="下拉类型",Mo={basicSettings:e,fieldSettings:o,pageSettings:t,generationSettings:a,associatedConfiguration:l,tableName:c,tableContent:n,addon:s,moduleName:d,className:r,editType:i,createTime:m,updateTime:h,popup:P,page:u,selectPlaceholder:y,tableNamePlaceholder:p,tableContentPlaceholder:T,addonPlaceholder:f,addonPlaceholder1:b,moduleNamePlaceholder:N,classNamePlaceholder:V,editTypePlaceholder:g,columnNamePlaceholder:C,columnCommentPlaceholder:S,tableNameValidata:x,fieldNameValidata:w,addCode:D,updateCode:A,codeDeleteTips:v,tableComment:L,tableCreateTime:F,tableUpdateTime:k,addBtn:K,searchPlaceholder:M,selectTableTips:I,download:U,commentField:B,baseField:E,columnName:q,columnComment:R,columnType:_,fieldAttribute:O,addAndEdit:Q,listSearch:W,isPk:j,isRequired:z,isInsert:G,isUpdate:H,isLists:J,isSearch:X,isQuery:Y,queryType:Z,formType:$,verifyType:ee,formInput:oe,formTextarea:te,formSelect:ae,formRadio:le,formCheckbox:ce,formDateTime:ne,formImageSelect:se,formFileSelect:de,formVideoSelect:re,formEditor:ie,formNumber:me,pk:he,status:Pe,string:ue,image:ye,radio:pe,checkbox:Te,select:fe,editor:be,dateTime:Ne,formValidation:Ve,deleteType:ge,physicalDeletion:Ce,softDeletion:Se,deleteField:xe,deleteFieldPlaceholder:we,orderColumnName:De,orderColumnNamePlaceholder:Ae,orderType:ve,orderTypePlaceholder:Le,menuType:Fe,autoBuild:ke,manualAddition:Ke,controller:Me,dataModel:Ie,validator:Ue,webView:Be,routerView:Ee,insertAssociated:qe,associatedType:Re,associatedTypePlaceholder:_e,hasOne:Oe,hasMany:Qe,associatedName:We,associatedNamePlaceholder:je,associatedModel:ze,associatedModelPlaceholder:Ge,localKey:He,localKeyPlaceholder:Je,foreignKey:Xe,foreignKeyPlaceholder:Ye,addons:Ze,addonsPlaceholder:$e,saveAndSync:eo,saveAndDownload:oo,saveAndSyncText:to,saveAndSyncText1:ao,mobileVerify:lo,numberVerify:co,idCardVerify:no,emailVerify:so,maxVerify:ro,minVerify:io,maxLabel:mo,minLabel:ho,minPlaceholder:Po,minPlaceholder1:uo,maxPlaceholder:yo,maxPlaceholder1:po,maxLabel1:To,minLabel1:fo,min1Placeholder:bo,min1Placeholder1:No,max1Placeholder:Vo,max1Placeholder1:go,between:Co,setUp:So,dictType:xo,dictTypePlaceholder:wo,dictTypePlaceholder1:Do,remotePullDown:Ao,remotePullDownValue:vo,remotePullDownValuePlaceholder:Lo,remotePullDownLabel:Fo,remotePullDownLabelPlaceholder:ko,selectType:Ko};export{Q as addAndEdit,K as addBtn,D as addCode,s as addon,f as addonPlaceholder,b as addonPlaceholder1,Ze as addons,$e as addonsPlaceholder,l as associatedConfiguration,ze as associatedModel,Ge as associatedModelPlaceholder,We as associatedName,je as associatedNamePlaceholder,Re as associatedType,_e as associatedTypePlaceholder,ke as autoBuild,E as baseField,e as basicSettings,Co as between,Te as checkbox,r as className,V as classNamePlaceholder,v as codeDeleteTips,R as columnComment,S as columnCommentPlaceholder,q as columnName,C as columnNamePlaceholder,_ as columnType,B as commentField,Me as controller,m as createTime,Ie as dataModel,Ne as dateTime,Mo as default,xe as deleteField,we as deleteFieldPlaceholder,ge as deleteType,xo as dictType,wo as dictTypePlaceholder,Do as dictTypePlaceholder1,U as download,i as editType,g as editTypePlaceholder,be as editor,so as emailVerify,O as fieldAttribute,w as fieldNameValidata,o as fieldSettings,Xe as foreignKey,Ye as foreignKeyPlaceholder,ce as formCheckbox,ne as formDateTime,ie as formEditor,de as formFileSelect,se as formImageSelect,oe as formInput,me as formNumber,le as formRadio,ae as formSelect,te as formTextarea,$ as formType,Ve as formValidation,re as formVideoSelect,a as generationSettings,Qe as hasMany,Oe as hasOne,no as idCardVerify,ye as image,qe as insertAssociated,G as isInsert,J as isLists,j as isPk,Y as isQuery,z as isRequired,X as isSearch,H as isUpdate,W as listSearch,He as localKey,Je as localKeyPlaceholder,Ke as manualAddition,Vo as max1Placeholder,go as max1Placeholder1,mo as maxLabel,To as maxLabel1,yo as maxPlaceholder,po as maxPlaceholder1,ro as maxVerify,Fe as menuType,bo as min1Placeholder,No as min1Placeholder1,ho as minLabel,fo as minLabel1,Po as minPlaceholder,uo as minPlaceholder1,io as minVerify,lo as mobileVerify,d as moduleName,N as moduleNamePlaceholder,co as numberVerify,De as orderColumnName,Ae as orderColumnNamePlaceholder,ve as orderType,Le as orderTypePlaceholder,u as page,t as pageSettings,Ce as physicalDeletion,he as pk,P as popup,Z as queryType,pe as radio,Ao as remotePullDown,Fo as remotePullDownLabel,ko as remotePullDownLabelPlaceholder,vo as remotePullDownValue,Lo as remotePullDownValuePlaceholder,Ee as routerView,oo as saveAndDownload,eo as saveAndSync,to as saveAndSyncText,ao as saveAndSyncText1,M as searchPlaceholder,fe as select,y as selectPlaceholder,I as selectTableTips,Ko as selectType,So as setUp,Se as softDeletion,Pe as status,ue as string,L as tableComment,n as tableContent,T as tableContentPlaceholder,F as tableCreateTime,c as tableName,p as tableNamePlaceholder,x as tableNameValidata,k as tableUpdateTime,A as updateCode,h as updateTime,Ue as validator,ee as verifyType,Be as webView};
