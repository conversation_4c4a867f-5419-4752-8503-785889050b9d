import{d as x,r as p,n as B,l as C,h as r,s as y,w as o,a as i,e as n,i as F,t as l,u as t,q as a,Z as N,c as u,C as f,M as R,N as T,E as j,V as I,a3 as O}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-4ed993c7.js";const q={class:"input-width"},K={class:"input-width"},L={key:0},M={key:1},S={key:2},U={class:"input-width"},Z={class:"input-width"},$={class:"input-width"},z={class:"dialog-footer"},ee=x({__name:"notice-records-info",setup(A,{expose:g}){const m=p(!1),d=p(!0),v={create_time:0,message_data:"",message_key:"",message_type:"",name:"",nickname:"",receiver:"",notice_type:""},e=B({...v}),b=p(),w=C(()=>({}));return g({showDialog:m,setFormData:async(_=null)=>{d.value=!0,Object.assign(e,v),_&&Object.keys(e).forEach(s=>{_[s]!=null&&(e[s]=_[s])}),d.value=!1}}),(_,s)=>{const c=R,k=T,D=j,V=I,E=O;return r(),y(V,{modelValue:m.value,"onUpdate:modelValue":s[1]||(s[1]=h=>m.value=h),title:t(a)("messageInfo"),width:"550px","destroy-on-close":!0},{footer:o(()=>[i("span",z,[n(D,{type:"primary",onClick:s[0]||(s[0]=h=>m.value=!1)},{default:o(()=>[F(l(t(a)("confirm")),1)]),_:1})])]),default:o(()=>[N((r(),y(k,{model:e,"label-width":"110px",ref_key:"formRef",ref:b,rules:t(w),class:"page-form"},{default:o(()=>[n(c,{label:t(a)("messageKey")},{default:o(()=>[i("div",q,l(e.name),1)]),_:1},8,["label"]),n(c,{label:t(a)("smsType")},{default:o(()=>[i("div",K,[e.notice_type=="sms"?(r(),u("div",L,l(t(a)("sms")),1)):f("",!0),e.notice_type=="wechat"?(r(),u("div",M,l(t(a)("wechat")),1)):f("",!0),e.notice_type=="weapp"?(r(),u("div",S,l(t(a)("weapp")),1)):f("",!0)])]),_:1},8,["label"]),n(c,{label:t(a)("nickname")},{default:o(()=>[i("div",U,l(e.nickname),1)]),_:1},8,["label"]),n(c,{label:t(a)("receiver")},{default:o(()=>[i("div",Z,l(e.receiver),1)]),_:1},8,["label"]),n(c,{label:t(a)("createTime")},{default:o(()=>[i("div",$,l(e.create_time),1)]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[E,d.value]])]),_:1},8,["modelValue","title"])}}});export{ee as _};
