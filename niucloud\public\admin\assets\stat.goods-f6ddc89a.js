const o="商品概况",s="时间筛选",t="开始时间",e="结束时间",c="搜索",n="商品浏览量",a="统计时间内，所有商品详情页被访问的次数，一个人在统计时间内访问多次记为多次",u="商品访客数",i="统计时间内，访问任何商品详情页的人数，一个人在统计时间范围内访问多次只记为一个",m="加购件数",d="统计时间内，添加商品进入购物车的商品件数",r="下单件数",N="统计时间内，成功下单的商品件数之和（不剔除退款订单）",l="支付件数",p="统计时间内， 成功付款订单的商品件数之和（不剔除退款订单）",T="支付金额",y="统计时间内，成功付款订单的商品金额之和（不剔除退款订单）",g="退款金额",f="统计时间内，成功退款的商品金额之和",C="退款件数",M="统计时间内，成功退款的商品件数之和",h="商品排行",v="商品名称",b="请输入商品名称",A="商品分类",P="全部",V="统计类型",k="请选择统计类型",w="商品信息",F="访问次数",I="访客数",O="加入购物车数量",R="商品销量",x="支付总金额",_="收藏数量",j={goodsOverview:o,timeFilter:s,startTime:t,endTime:e,search:c,goodsAccessNum:n,goodsAccessNumTip:a,goodsVisitCount:u,goodsVisitCountTips:i,cartNum:m,cartNumTips:d,saleNum:r,saleNumTips:N,payNum:l,payNumTips:p,payMoney:T,payMoneyTips:y,refundMoney:g,refundMoneyTips:f,refundNum:C,refundNumTips:M,goodsRank:h,goodsName:v,goodsNamePlaceholder:b,goodsCategory:A,all:P,totalType:V,totalTypePlaceholder:k,goodsInfo:w,accessNum:F,visitCount:I,cartNumber:O,saleNumber:R,payTotal:x,collectNum:_};export{F as accessNum,P as all,m as cartNum,d as cartNumTips,O as cartNumber,_ as collectNum,j as default,e as endTime,n as goodsAccessNum,a as goodsAccessNumTip,A as goodsCategory,w as goodsInfo,v as goodsName,b as goodsNamePlaceholder,o as goodsOverview,h as goodsRank,u as goodsVisitCount,i as goodsVisitCountTips,T as payMoney,y as payMoneyTips,l as payNum,p as payNumTips,x as payTotal,g as refundMoney,f as refundMoneyTips,C as refundNum,M as refundNumTips,r as saleNum,N as saleNumTips,R as saleNumber,c as search,t as startTime,s as timeFilter,V as totalType,k as totalTypePlaceholder,I as visitCount};
