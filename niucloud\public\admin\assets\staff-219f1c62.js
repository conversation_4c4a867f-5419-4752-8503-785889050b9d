import{d as $,y as M,f as S,n as z,r as h,bI as R,h as y,c as U,e as a,w as o,u as r,aT as j,a as u,q as l,i as c,t as d,Z as I,s as q,bJ as L,af as H,aU as J,a9 as Z,L as A,M as G,E as K,N as O,ag as Q,ah as W,a2 as X,a3 as Y}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{_ as ee}from"./delivery-personnel-edit.vue_vue_type_style_index_0_lang-2f141e00.js";import{p as ae,q as te}from"./delivery-ef62b210.js";/* empty css                  *//* empty css                   */const le={class:"main-container"},oe={class:"flex justify-between items-center"},re={class:"mt-[10px]"},ne={class:"mt-[16px] flex justify-end"},Te=$({__name:"staff",setup(ie){const x=M();S();const C=x.meta.title,e=z({page:1,limit:10,total:0,loading:!1,data:[],searchParam:{deliver_name:"",deliver_mobile:""}}),v=h(),s=(i=1)=>{e.loading=!0,e.page=i,ae({page:e.page,limit:e.limit,...e.searchParam}).then(t=>{e.loading=!1,e.data=t.data.data,e.total=t.data.total,L(e.page,e.limit,e.searchParam)}).catch(()=>{e.loading=!1})};s(R(e.searchParam).page);const m=h(null),k=()=>{m.value.setFormData(),m.value.showDialog=!0},P=i=>{m.value.setFormData(i),m.value.showDialog=!0},D=i=>{H.confirm(l("deliverDeleteTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{te(i).then(()=>{s()}).catch(()=>{})})},E=i=>{i&&(i.resetFields(),s())};return(i,t)=>{const w=J,_=Z,b=A,f=G,p=K,B=O,g=Q,F=W,T=X,V=Y;return y(),U("div",le,[a(_,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[a(w,{content:r(C),icon:r(j),onBack:t[0]||(t[0]=n=>i.$router.back())},null,8,["content","icon"])]),_:1}),a(_,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:o(()=>[a(_,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:o(()=>[u("div",oe,[a(B,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:v},{default:o(()=>[a(f,{label:r(l)("deliverName"),prop:"deliver_name"},{default:o(()=>[a(b,{modelValue:e.searchParam.deliver_name,"onUpdate:modelValue":t[1]||(t[1]=n=>e.searchParam.deliver_name=n),modelModifiers:{trim:!0},placeholder:r(l)("deliverNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),a(f,{label:r(l)("deliverMobile"),prop:"deliver_mobile"},{default:o(()=>[a(b,{modelValue:e.searchParam.deliver_mobile,"onUpdate:modelValue":t[2]||(t[2]=n=>e.searchParam.deliver_mobile=n),modelModifiers:{trim:!0},placeholder:r(l)("deliverMobilePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),a(f,null,{default:o(()=>[a(p,{type:"primary",onClick:t[3]||(t[3]=n=>s())},{default:o(()=>[c(d(r(l)("search")),1)]),_:1}),a(p,{onClick:t[4]||(t[4]=n=>E(v.value))},{default:o(()=>[c(d(r(l)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),a(p,{type:"primary",onClick:k},{default:o(()=>[c(d(r(l)("addDeliveryPersonnel")),1)]),_:1})])]),_:1}),u("div",re,[I((y(),q(F,{data:e.data,ref:"tableRef",size:"large"},{empty:o(()=>[u("span",null,d(e.loading?"":r(l)("emptyData")),1)]),default:o(()=>[a(g,{prop:"deliver_name",label:r(l)("deliverName")},null,8,["label"]),a(g,{prop:"deliver_mobile",label:r(l)("deliverMobile")},null,8,["label"]),a(g,{label:r(l)("operation"),fixed:"right",align:"right",width:"120"},{default:o(({row:n})=>[a(p,{type:"primary",link:"",onClick:N=>P(n)},{default:o(()=>[c(d(r(l)("edit")),1)]),_:2},1032,["onClick"]),a(p,{type:"primary",link:"",onClick:N=>D(n.deliver_id)},{default:o(()=>[c(d(r(l)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[V,e.loading]]),u("div",ne,[a(T,{"current-page":e.page,"onUpdate:current-page":t[5]||(t[5]=n=>e.page=n),"page-size":e.limit,"onUpdate:page-size":t[6]||(t[6]=n=>e.limit=n),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:t[7]||(t[7]=n=>s()),onCurrentChange:s},null,8,["current-page","page-size","total"])])])]),_:1}),a(ee,{ref_key:"editCategoryDialog",ref:m,onComplete:s},null,512)])}}});export{Te as default};
