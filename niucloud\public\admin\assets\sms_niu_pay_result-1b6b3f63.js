import{d as B,f as E,y as C,r as N,aN as h,cs as q,h as n,c as I,e as s,w as a,u as r,aT as R,s as _,i as y,t as k,q as b,aU as S,a9 as $,E as H,b9 as P}from"./index-30109030.js";/* empty css                  *//* empty css                  *//* empty css                *//* empty css                       */import{H as T}from"./notice-9865bf12.js";const U={class:"main-container"},F=B({__name:"sms_niu_pay_result",setup(V){const v=E(),c=C(),x=c.meta.title,g=c.query.username||"",l=()=>{v.push("/setting/niusms/setting")},u=N("wait");let o=null;h(()=>{const t=c.query.out_trade_no;t&&(d(t),o=setInterval(()=>{d(t)},2e3))}),q(()=>{o&&clearInterval(o)});const d=async t=>{try{const e=await T(g,{out_trade_no:t});u.value=e.data.order_status,(e.data.order_status==="payment"||e.data.order_status==="close")&&o&&clearInterval(o)}catch(e){console.error("获取支付状态失败",e)}};return(t,e)=>{const w=S,m=$,p=H,i=P;return n(),I("div",U,[s(m,{class:"box-card !border-none mb-[15px]",shadow:"never"},{default:a(()=>[s(w,{content:r(x),icon:r(R),onBack:e[0]||(e[0]=f=>l())},null,8,["content","icon"])]),_:1}),s(m,{class:"box-card !border-none mb-[15px]",shadow:"never"},{default:a(()=>[u.value==="payment"?(n(),_(i,{key:0,icon:"success",title:"支付成功"},{extra:a(()=>[s(p,{type:"primary",onClick:e[1]||(e[1]=f=>l())},{default:a(()=>[y(k(r(b)("back")),1)]),_:1})]),_:1})):u.value==="close"?(n(),_(i,{key:1,icon:"error",title:"订单已关闭"},{extra:a(()=>[s(p,{type:"primary",onClick:e[2]||(e[2]=f=>l())},{default:a(()=>[y(k(r(b)("back")),1)]),_:1})]),_:1})):(n(),_(i,{key:2,icon:"info",title:"支付状态查询中","sub-title":"请稍候，正在确认支付状态..."}))]),_:1})])}}});export{F as default};
