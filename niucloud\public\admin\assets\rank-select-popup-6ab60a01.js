import{d as q,l as V,r as x,n as E,h as R,c as Z,a as i,b as H,e as r,w as p,i as v,t as d,u as l,q as n,Z as S,_ as b,s as J,a6 as K,$ as T,E as Q,L as W,M as X,N as Y,ag as ee,ah as ae,a2 as te,V as le,a3 as ne,b4 as oe}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  */import{c as re}from"./marketing-9899dda3.js";import{_ as se}from"./_plugin-vue_export-helper-c27b6911.js";const ie={class:"inline-block ml-[10px] text-[14px]"},pe={class:"text-primary mx-[2px]"},de={class:"mt-[16px] flex"},me={class:"flex items-center flex-1"},ue={class:"mr-[10px]"},ce={class:"text-primary mx-[2px]"},_e={class:"dialog-footer"},fe=q({__name:"rank-select-popup",props:{modelValue:{type:String,default:""},max:{type:Number,default:0},min:{type:Number,default:0}},emits:["update:modelValue","rankSelect"],setup($,{expose:B,emit:P}){const u=$,c=V({get(){return u.modelValue},set(t){P("update:modelValue",t)}}),k=x(!1),o=E({}),f=V(()=>Object.keys(o).length),a=E({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{name:"",verify_rank_ids:""}}),w=x(),g=x(),D=(t,e)=>{if(u.max===1){g.value.clearSelection(),g.value.toggleRowSelection(e,!0);for(const s in o)delete o[s];o["rank_"+e.rank_id]=e}else{let s=!1;for(let _=0;_<t.length;_++)if(t[_].rank_id===e.rank_id){s=!0;break}s?o["rank_"+e.rank_id]=e:delete o["rank_"+e.rank_id]}},z=t=>{if(u.max==1){g.value.clearSelection();for(const e in o)delete o[e]}else t.length?t.forEach(e=>{o["rank_"+e.rank_id]=e}):a.data.forEach(e=>{delete o["rank_"+e.rank_id]})},C=()=>{oe(()=>{if(g.value)for(let t=0;t<a.data.length;t++)g.value.toggleRowSelection(a.data[t],!1),o["rank_"+a.data[t].rank_id]&&g.value.toggleRowSelection(a.data[t],!0)})},y=(t=1,e=null)=>{a.loading=!0,a.page=t;const s=K(a.searchParam);re({page:a.page,limit:a.limit,...s}).then(_=>{a.loading=!1,a.data=_.data.data,a.total=_.data.total,e&&e(_.data.verify_rank_ids),C()}).catch(()=>{a.loading=!1})},F=t=>{t&&(t.resetFields(),a.searchParam.verify_rank_ids="",a.searchParam.name="",y())},L=()=>{a.searchParam.verify_rank_ids=c.value,y(1,t=>{if(c.value){c.value.splice(0,c.value.length,...t),c.value.forEach(e=>{o["rank_"+e]||(o["rank_"+e]={})});for(let e=0;e<a.data.length;e++)c.value.indexOf(a.data[e].rank_id)!=-1&&(o["rank_"+a.data[e].rank_id]=a.data[e])}}),k.value=!0},I=()=>{for(let t in o)delete o[t];C()},M=()=>{if(u.min&&f.value<u.min){T({type:"warning",message:`${n("rankSelectPopupGoodsMinTip")}${u.min}${n("goodsSelectPopupPiece")}`});return}if(u.max&&u.max>0&&f.value&&f.value>u.max){T({type:"warning",message:`${n("rankSelectPopupGoodsMaxTip")}${u.max}${n("goodsSelectPopupPiece")}`});return}let t=[];for(let e in o)t.push(parseInt(e.replace("rank_","")));c.value.splice(0,c.value.length,...t),P("rankSelect",o),k.value=!1};return B({showDialog:k,selectRank:o,selectRankNum:f}),(t,e)=>{const s=Q,_=W,N=X,G=Y,h=ee,U=ae,A=te,j=le,O=ne;return R(),Z("div",null,[i("div",{onClick:L},[H(t.$slots,"default",{},()=>[r(s,null,{default:p(()=>[v(d(l(n)("rankSelectPopupSelectRankButton")),1)]),_:1}),S(i("div",ie,[i("span",null,d(l(n)("goodsSelectPopupSelect")),1),i("span",pe,d(l(c).length),1),i("span",null,d(l(n)("goodsSelectPopupPiece")),1)],512),[[b,l(c).length]])],!0)]),r(j,{modelValue:k.value,"onUpdate:modelValue":e[7]||(e[7]=m=>k.value=m),name:l(n)("rankSelect"),width:"1000px","destroy-on-close":!0,"close-on-click-modal":!1},{footer:p(()=>[i("span",_e,[r(s,{onClick:e[6]||(e[6]=m=>k.value=!1)},{default:p(()=>[v(d(l(n)("cancel")),1)]),_:1}),r(s,{type:"primary",onClick:M},{default:p(()=>[v(d(l(n)("confirm")),1)]),_:1})])]),default:p(()=>[r(G,{inline:!0,model:a.searchParam,ref_key:"searchFormRef",ref:w},{default:p(()=>[r(N,{label:l(n)("rankName"),prop:"keyword",class:"form-item-wrap"},{default:p(()=>[r(_,{modelValue:a.searchParam.name,"onUpdate:modelValue":e[0]||(e[0]=m=>a.searchParam.name=m),modelModifiers:{trim:!0},placeholder:l(n)("rankNamePlaceholder"),maxlength:"60"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(N,{class:"form-item-wrap"},{default:p(()=>[r(s,{type:"primary",onClick:e[1]||(e[1]=m=>y())},{default:p(()=>[v(d(l(n)("search")),1)]),_:1}),r(s,{onClick:e[2]||(e[2]=m=>F(w.value))},{default:p(()=>[v(d(l(n)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),S((R(),J(U,{data:a.data,size:"large",ref_key:"rankListTableRef",ref:g,"max-height":"400",onSelect:D,onSelectAll:z},{empty:p(()=>[i("span",null,d(a.loading?"":l(n)("emptyData")),1)]),default:p(()=>[r(h,{type:"selection",width:"55"}),r(h,{prop:"name",label:l(n)("rankName"),"min-width":"130"},null,8,["label"]),r(h,{prop:"show_goods_num",label:l(n)("showGoodsNum"),"min-width":"130"},null,8,["label"]),r(h,{prop:"goods_source_name",label:l(n)("goodsSourceName"),"min-width":"130"},null,8,["label"]),r(h,{prop:"rule_type_name",label:l(n)("ruleTypeName"),"min-width":"130"},null,8,["label"]),r(h,{prop:"rank_type_name",label:l(n)("rankTypeName"),"min-width":"130"},null,8,["label"])]),_:1},8,["data"])),[[O,a.loading]]),i("div",de,[i("div",me,[S(i("div",ue,[i("span",null,d(l(n)("goodsSelectPopupBeforeTip")),1),i("span",ce,d(l(f)),1),i("span",null,d(l(n)("rankSelectPopupAfterTip")),1)],512),[[b,l(f)]]),S(r(s,{type:"primary",link:"",onClick:I},{default:p(()=>[v(d(l(n)("goodsSelectPopupClearGoods")),1)]),_:1},512),[[b,l(f)]])]),r(A,{"current-page":a.page,"onUpdate:current-page":e[3]||(e[3]=m=>a.page=m),"page-size":a.limit,"onUpdate:page-size":e[4]||(e[4]=m=>a.limit=m),layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:e[5]||(e[5]=m=>y()),onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","name"])])}}});const Fe=se(fe,[["__scopeId","data-v-a4925b92"]]);export{Fe as default};
