import{d as R,c5 as E,G as S,aN as L,R as B,Z as D,_ as j,u as o,h as _,c as f,e as r,w as n,F as q,W as N,y as V,aq as $,f as F,s as I,i as m,t as b,q as p,a as M,v as z,bk as G,bl as P,bm as W,ap as Z}from"./index-30109030.js";/* empty css                    *//* empty css                  *//* empty css                         *//* empty css                  *//* empty css                     */import{_ as A}from"./_plugin-vue_export-helper-c27b6911.js";const H={class:"tab-wrap w-full px-[16px]"},J=R({__name:"tabs",setup(K){const a=E(),h=S(),d=V(),l=F();L(()=>{a.addTab(d)}),B(d,e=>{a.addTab(e)});const k=e=>{const t=a.tabs[e.props.name];l.push({name:t.name,query:t.query})},v=e=>{if(d.name==e){const t=Object.keys(a.tabs);t.indexOf(e)==0?l.push({name:t[1]}):l.push({name:t[t.indexOf(e)-1]})}a.removeTab(e)},y=e=>{const t=Object.keys(a.tabs);for(let s=t.indexOf(e)-1;s>=0;s--)delete a.tabs[t[s]];l.push({name:e})},w=e=>{const t=Object.keys(a.tabs);for(let s=t.indexOf(e)+1;s<t.length;s++)delete a.tabs[t[s]];l.push({name:e})},g=e=>{Object.keys(a.tabs).forEach(s=>{s!=e&&delete a.tabs[s]}),l.push({name:e})};return(e,t)=>{const s=G,x=P,C=W,T=Z,O=$;return D((_(),f("div",H,[r(O,{closable:o(a).tabLength>1,"model-value":o(d).name,onTabClick:k,onTabRemove:v},{default:n(()=>[(_(!0),f(q,null,N(o(a).tabs,(c,Q,i)=>(_(),I(T,{name:c.name,key:i},{label:n(()=>[r(C,{trigger:"contextmenu",placement:"bottom-start"},{dropdown:n(()=>[r(x,null,{default:n(()=>[r(s,{icon:"Back",disabled:i==0,onClick:u=>y(c.name)},{default:n(()=>[m(b(o(p)("tabs.closeLeft")),1)]),_:2},1032,["disabled","onClick"]),r(s,{icon:"Right",disabled:i==o(a).tabLength-1,onClick:u=>w(c.name)},{default:n(()=>[m(b(o(p)("tabs.closeRight")),1)]),_:2},1032,["disabled","onClick"]),r(s,{icon:"Close",disabled:o(a).tabLength==1,onClick:u=>g(c.name)},{default:n(()=>[m(b(o(p)("tabs.closeOther")),1)]),_:2},1032,["disabled","onClick"])]),_:2},1024)]),default:n(()=>[M("span",{class:z([{"text-primary":o(d).name==c.name},"tab-name"])},b(c.title),3)]),_:2},1024)]),_:2},1032,["name"]))),128))]),_:1},8,["closable","model-value"])],512)),[[j,o(h).tab]])}}});const oe=A(J,[["__scopeId","data-v-1fd131da"]]);export{oe as default};
