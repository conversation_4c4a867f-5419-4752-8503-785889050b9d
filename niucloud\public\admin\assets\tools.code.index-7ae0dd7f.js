const e="表名称",t="描述",o="插件",c="模块名",n="类名",s="编辑方式",a="创建时间",d="更新时间",l="弹出",b="新页面",p="请输入表名",r="请输入描述",i="请选择插件",m="请输入模块名",T="请输入类名",h="请选择编辑方式",N="导入数据表",P="编辑代码生成",C="确定要删除吗？",u="表描述",y="创建时间",v="修改时间",x="添加",A="请输入表名或表描述搜索",S="确认导入该数据表吗？",g="下载代码",w="代码生成",B="生成列表",D="选择数据表生成代码",G="点击添加，选择对应数据表，添加之后会跳转到对应生成代码的配置管理页面",L="添加",U="基础设置",_="代码生成查看基础设置，包括基础的表名、描述、所属插件设置",f="插件列表",j="字段设置",k="代码中字段的管理，包括字段是否进行增加、编辑、列表、查询展示等",q="查看教程",z="页面设置",E="正在开发中，会将字段进行展示管理配置，同时提供预览，真正实现自定义表单配置",F="插件列表",H="生成配置",I="针对代码中展示页面配置，包括单独页面编辑表单还是弹框，生成的代码穿插到系统中还是进行下载等",J="官方市场",K="同步代码",M="同步的代码与项目产生冲突，是否确认覆盖?",O="同步的代码会加入到项目代码中，是否确认继续",Q="所属插件",R={tableName:e,tableContent:t,addon:o,moduleName:c,className:n,editType:s,createTime:a,updateTime:d,popup:l,page:b,tableNamePlaceholder:p,tableContentPlaceholder:r,addonPlaceholder:i,moduleNamePlaceholder:m,classNamePlaceholder:T,editTypePlaceholder:h,addCode:N,updateCode:P,codeDeleteTips:C,tableComment:u,tableCreateTime:y,tableUpdateTime:v,addBtn:x,searchPlaceholder:A,selectTableTips:S,download:g,codeGeneration:w,codeList:B,step1:D,describe1:G,btn1:L,step2:U,describe2:_,btn2:f,step3:j,describe3:k,btn3:q,step4:z,describe4:E,btn4:F,step5:H,describe5:I,btn5:J,saveAndSync:K,saveAndSyncText:M,saveAndSyncText1:O,addonName:Q};export{x as addBtn,N as addCode,o as addon,Q as addonName,i as addonPlaceholder,L as btn1,f as btn2,q as btn3,F as btn4,J as btn5,n as className,T as classNamePlaceholder,C as codeDeleteTips,w as codeGeneration,B as codeList,a as createTime,R as default,G as describe1,_ as describe2,k as describe3,E as describe4,I as describe5,g as download,s as editType,h as editTypePlaceholder,c as moduleName,m as moduleNamePlaceholder,b as page,l as popup,K as saveAndSync,M as saveAndSyncText,O as saveAndSyncText1,A as searchPlaceholder,S as selectTableTips,D as step1,U as step2,j as step3,z as step4,H as step5,u as tableComment,t as tableContent,r as tableContentPlaceholder,y as tableCreateTime,e as tableName,p as tableNamePlaceholder,v as tableUpdateTime,P as updateCode,d as updateTime};
