const e="插件名称",o="请输入插件名称",c="插件图标",l="请上传插件图标",r="建议图片尺寸：200*200像素；图片格式：jpg、png、jpeg",t="插件标识",s="请输入插件标识",n="插件标识格式不正确，只能以字母开头且只能输入字母、数字、下划线",a="插件标识指开发插件的文件夹名称，申请之后不能修改(仅允许使用字母、数字与下划线组合，且必须以字母开头，同时名称中至少包含一个下划线，格式如：a11_34、f11_22)",d="插件标识设置后建议进行插件标识检测，如果当前插件标识已经在niucloud官方市场注册，则只能在本地使用，无法在官方市场发布销售",h="插件描述",p="请输入插件描述",P="作者",i="请输入作者",u="版本号",y="请输入版本号",v="版本号格式不正确",g="请注意版本号输入格式，例如: 1.1.1",k="插件封面",T="请上传插件封面",x="建议图片尺寸：480*240像素；图片格式：jpg、png、jpeg",j="插件类型",A="请选择插件类型",E="应用：指独立开发的系统，比如商城，零售，erp等",S="插件:指不是独立的系统，可以是辅助应用的插件比如商城的拼团，也可以是独立的插件比如万能表单等",_="所属应用",f="支持应用",w="请选择支持应用",G="生成插件",b="检测当前插件标识尚未在应用市场注册，插件开发后可以在niucloud官方市场发布",m="检测到当前插件标识已经在niucloud官方市场注册，开发的插件只能在本地使用，无法在官方市场发布销售",q="插件生成成功",z={title:e,titlePlaceholder:o,icon:c,iconPlaceholder:l,iconPlaceholder1:r,key:t,keyPlaceholder:s,keyPlaceholderErr:n,keyPlaceholder1:a,keyPlaceholder2:d,desc:h,descPlaceholder:p,author:P,authorPlaceholder:i,version:u,versionPlaceholder:y,versionPlaceholderErr:v,versionPlaceholder1:g,cover:k,coverPlaceholder:T,coverPlaceholder1:x,type:j,typePlaceholder:A,typePlaceholder1:E,typePlaceholder2:S,supportType:_,supportApp:f,supportAppPlaceholder:w,GeneratePlugins:G,successText:b,warningText:m,onSaveSuccessText:q};export{G as GeneratePlugins,P as author,i as authorPlaceholder,k as cover,T as coverPlaceholder,x as coverPlaceholder1,z as default,h as desc,p as descPlaceholder,c as icon,l as iconPlaceholder,r as iconPlaceholder1,t as key,s as keyPlaceholder,a as keyPlaceholder1,d as keyPlaceholder2,n as keyPlaceholderErr,q as onSaveSuccessText,b as successText,f as supportApp,w as supportAppPlaceholder,_ as supportType,e as title,o as titlePlaceholder,j as type,A as typePlaceholder,E as typePlaceholder1,S as typePlaceholder2,u as version,y as versionPlaceholder,g as versionPlaceholder1,v as versionPlaceholderErr,m as warningText};
