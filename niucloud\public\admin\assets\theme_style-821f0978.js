import{d as B,y as D,r as d,h as _,c as x,e as a,w as o,a as t,t as m,u as p,Z as I,s as N,q as y,B as S,x as u,C as j,i as L,a6 as T,K as V,ag as R,E as z,ah as $,a9 as q,a3 as K,p as O,g as Z}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                 *//* empty css                        */import{_ as A}from"./icon-addon-339e16d0.js";import{B as F}from"./diy-aea57979.js";import{t as G}from"./theme-list-6bf065b9.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                   */import"./edit-theme.vue_vue_type_script_setup_true_lang-9344cd0b.js";/* empty css                        *//* empty css                 *//* empty css                */import"./el-form-item-4ed993c7.js";import"./add-theme.vue_vue_type_script_setup_true_lang-ad58889c.js";import"./diy-b9271e02.js";const J=l=>(O("data-v-d1588c37"),l=l(),Z(),l),M={class:"main-container"},P={class:"flex justify-between items-center"},Q={class:"text-page-title"},U={class:"flex items-center"},W=J(()=>t("div",{class:"flex items-center w-full h-full"},[t("img",{class:"w-full h-full",src:A,alt:""})],-1)),X={class:"flex-1 ml-2 truncate"},Y={key:0,class:"rounded-[3px] inline-flex items-center justify-center border-[1px] border-solid border-[#f2f2f2] overflow-hidden"},ee=B({__name:"theme_style",setup(l){const b=D().meta.title,i=d(!0),f=d(null),h=d([]),v=()=>{i.value=!0,F({}).then(n=>{let s=T(n.data);for(let r in s)s[r].key=r;h.value=Object.values(s),i.value=!1})};v();const g=n=>{f.value.open(n)};return(n,s)=>{const r=V,c=R,k=z,w=$,C=q,E=K;return _(),x("div",M,[a(C,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[t("div",P,[t("span",Q,m(p(b)),1)]),I((_(),N(w,{data:h.value,size:"large",class:"mt-[20px]"},{empty:o(()=>[t("span",null,m(i.value?"":p(y)("emptyData")),1)]),default:o(()=>[a(c,{label:"应用","min-width":"120"},{default:o(({row:e})=>[t("div",U,[a(r,{class:"w-[40px] h-[40px] rounded-md overflow-hidden",src:p(S)(e.icon),fit:"contain"},{error:o(()=>[W]),_:2},1032,["src"]),t("div",X,m(e.addon_title),1)])]),_:1}),a(c,{label:"配色名称","min-width":"120"},{default:o(({row:e})=>[t("div",null,m(e.title),1)]),_:1}),a(c,{label:"配色方案","min-width":"120"},{default:o(({row:e})=>[e.theme?(_(),x("div",Y,[t("span",{class:"w-[18px] h-[18px]",style:u({backgroundColor:e.theme["--primary-color"]})},null,4),t("span",{class:"w-[18px] h-[18px]",style:u({backgroundColor:e.theme["--primary-help-color2"]})},null,4),t("span",{class:"w-[18px] h-[18px]",style:u({backgroundColor:e.theme["--primary-color-dark"]})},null,4)])):j("",!0)]),_:1}),a(c,{label:p(y)("operation"),align:"right",fixed:"right",width:"100"},{default:o(({row:e})=>[a(k,{type:"primary",link:"",onClick:oe=>g(e)},{default:o(()=>[L("编辑")]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[E,i.value]])]),_:1}),a(G,{ref_key:"themeListRef",ref:f,onConfirm:s[0]||(s[0]=e=>v())},null,512)])}}});const Ne=H(ee,[["__scopeId","data-v-d1588c37"]]);export{Ne as default};
