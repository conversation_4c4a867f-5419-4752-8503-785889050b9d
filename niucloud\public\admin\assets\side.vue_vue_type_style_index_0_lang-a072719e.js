import{d as le,G as oe,j as ce,r as h,l as re,bZ as P,aN as ue,b0 as de,R as ie,h as o,s as _,w as c,e as f,a as u,u as r,c as p,B as pe,W as Q,F as L,t as T,C as X,y as me,K as he,bG as _e,cZ as fe,U as ve,O as xe,A as ye,f as ge,a6 as ke,bv as we,cY as be}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  */import"./el-menu-item-4ed993c7.js";import{_ as Ee}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";/* empty css                 *//* empty css                        */import{_ as Ce}from"./icon-addon-339e16d0.js";import{g as Me,a as Se}from"./site-0a26f9e1.js";import{_ as je}from"./menu-item.vue_vue_type_style_index_0_lang-e86a3a49.js";const Re={class:"one-menu w-[124px] h-screen px-[8px] bg-[#282c34]"},Be={key:0,class:"logo flex items-center m-auto h-[64px]"},Ke=u("div",{class:"flex justify-center items-center w-full h-[40px]"},[u("img",{class:"max-w-[40px]",src:Ce,alt:"","object-fit":"contain"})],-1),Le={key:1,class:"logo flex items-center justify-center h-[64px]"},Ne=u("i",{class:"text-3xl iconfont iconyunkongjian"},null,-1),Ae=[Ne],De={key:0,class:"w-[16px] h-[16px] relative flex justify-center"},Ue={key:1,class:"w-[16px] h-[16px]"},Ve={class:"relative flex-1 w-0"},Fe={class:"ml-[10px] w-full truncate"},Oe=u("div",{class:"h-[48px]"},null,-1),$e={class:"w-[140px] h-[64px] flex items-center justify-center text-[16px] border-b-[1px] border-solid border-[var(--el-border-color-lighter)]"},Ge=u("div",{class:"h-[48px]"},null,-1),aa=le({__name:"side",setup(Ze){const a=me(),I=ge(),k=oe(),v=ce(),t=v.siteInfo,ee=v.routers,ae=v.addonIndexRoute,x=h([]),l=h([]),w={},te=re(()=>v.siteInfo.icon?v.siteInfo.icon:k.website.icon);if(ee.forEach(e=>{e.original_name=e.name,e.meta.addon==""?e.meta.attr==""&&(e.children&&e.children.length&&(e.name=P(e.children)),x.value.push(e)):e.meta.addon!=""&&(t==null?void 0:t.apps.length)<=1&&(t==null?void 0:t.apps[0].key)==e.meta.addon&&e.meta.show?e.children?(e.children.forEach(s=>{s.original_name=s.name,s.children&&s.children.length&&(s.name=P(s.children))}),x.value.unshift(...e.children)):x.value.unshift(e):w[e.meta.addon]=e}),(t==null?void 0:t.apps.length)>1){const e=[];t==null||t.apps.forEach(s=>{w[s.key]&&(w[s.key].name=ae[s.key],e.push(w[s.key]))}),x.value.unshift(...e)}const j=h(null),N=h(null),d=h(a.matched[1].name),se=async()=>{const e=await Me();j.value=e.data},ne=async()=>{const e=await Se();N.value=e.data};ue(async()=>{await se(),await ne()}),de(()=>{var C,y,g,M,S,n,m,K,A,D,U,V,F,O,$,G,Z,q,H,W,Y,z,J;const e=((g=(y=(C=j.value)==null?void 0:C.addon)==null?void 0:y.list)==null?void 0:g.map(i=>i.key))??[],s=((n=(S=(M=j.value)==null?void 0:M.tool)==null?void 0:S.list)==null?void 0:n.map(i=>i.key))??[],b=[...e,...s],B=((A=(K=(m=N.value)==null?void 0:m.marketing)==null?void 0:K.list)==null?void 0:A.map(i=>i.key))??[],E=(D=a.matched[1])==null?void 0:D.name;if(b.includes(E))d.value="addon",l.value=((U=a.matched[1])==null?void 0:U.children)??[];else if(B.includes(E))d.value="active",l.value=((V=a.matched[1])==null?void 0:V.children)??[];else if(a.meta.attr!=="")d.value=(F=a.matched[2])==null?void 0:F.name,l.value=((O=a.matched[1])==null?void 0:O.children)??[];else if((t==null?void 0:t.apps.length)>1)l.value=($=a.matched[1])==null?void 0:$.children,d.value=(G=a.matched[1])==null?void 0:G.name;else{const i=a.matched[1];i.meta.addon===""?(d.value=(Z=a.matched[1])==null?void 0:Z.name,l.value=((q=a.matched[1])==null?void 0:q.children)??[]):i.meta.addon===((H=t==null?void 0:t.apps[0])==null?void 0:H.key)?(d.value=(W=a.matched[2])==null?void 0:W.name,l.value=((Y=a.matched[2])==null?void 0:Y.children)??[]):(d.value=(z=a.matched[1])==null?void 0:z.name,l.value=((J=a.matched[1])==null?void 0:J.children)??[])}});const R=h([]);return ie(l.value,()=>{if(R.value=[],l.value&&Object.values(l.value).length){let e=ke(l.value);for(let s in e)R.value.push(e[s].name)}},{immediate:!0}),(e,s)=>{const b=he,B=_e,E=Ee,C=be,y=fe,g=ve,M=xe,S=ye;return o(),_(S,{class:"w-100 h-screen"},{default:c(()=>[f(M,{class:"flex p-0"},{default:c(()=>[u("div",Re,[f(B,{class:"logo-wrap"},{default:c(()=>[r(k).menuIsCollapse?(o(),p("div",Le,Ae)):(o(),p("div",Be,[f(b,{style:{width:"40px",height:"40px"},src:r(pe)(r(te)),fit:"contain"},{error:c(()=>[Ke]),_:1},8,["src"])]))]),_:1}),f(g,{class:"h-[calc( 100vh - 64px )]"},{default:c(()=>[f(y,{"default-active":d.value,router:!0,class:"aside-menu","unique-opened":!0,collapse:r(k).menuIsCollapse},{default:c(()=>[(o(!0),p(L,null,Q(x.value,(n,m)=>(o(),p(L,{key:m},[n.meta.show?(o(),_(C,{key:0,index:n.original_name,onClick:K=>r(I).push({name:n.name})},{title:c(()=>[u("div",Ve,[u("span",Fe,T(n.meta.short_title||n.meta.title),1)])]),default:c(()=>[n.meta.icon?(o(),p("div",De,[r(we)(n.meta.icon)?(o(),_(b,{key:0,class:"w-[16px] h-[16px] rounded-[50%] overflow-hidden",src:n.meta.icon,fit:"fill"},null,8,["src"])):(o(),_(E,{key:1,name:n.meta.icon,class:"absolute top-[50%] -translate-y-[50%]"},null,8,["name"]))])):(o(),p("div",Ue))]),_:2},1032,["index","onClick"])):X("",!0)],64))),128))]),_:1},8,["default-active","collapse"]),Oe]),_:1})]),l.value.length?(o(),_(g,{key:0,class:"two-menu w-[140px]"},{default:c(()=>[u("div",$e,T(r(a).matched[1].meta.title),1),f(y,{class:"aside-menu","default-active":r(a).name,"default-openeds":R.value,router:!0,collapse:r(k).menuIsCollapse},{default:c(()=>[(o(!0),p(L,null,Q(l.value,(n,m)=>(o(),_(je,{routes:n,key:m},null,8,["routes"]))),128))]),_:1},8,["default-active","default-openeds","collapse"]),Ge]),_:1})):X("",!0)]),_:1})]),_:1})}}});export{aa as _};
