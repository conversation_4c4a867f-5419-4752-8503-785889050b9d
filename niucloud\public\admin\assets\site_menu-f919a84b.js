import{d as R,y as j,r as B,n as O,h as s,c as _,e as l,w as n,a as h,t as i,u as t,i as m,q as e,Z as N,s as u,C as c,ak as P,af as z,al as v,am as S,an as U,E as Z,ag as A,ao as G,ah as H,ap as J,aq as K,a9 as Q,a3 as W}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                    *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css               */import{_ as X}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";/* empty css                        *//* empty css                  */import{_ as Y}from"./edit-menu.vue_vue_type_script_setup_true_lang-3e73be09.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 *//* empty css                        */import"./index-87881158.js";import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                        */import"./el-form-item-4ed993c7.js";/* empty css                  *//* empty css                  *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                *//* empty css                       *//* empty css                       *//* empty css                 */import"./tools-a40a4fbf.js";const ee={class:"main-container"},te={class:"flex justify-between items-center"},ae={class:"text-page-title"},ne={class:"flex items-center"},le={key:0},ie={key:1},oe={key:2},se={key:0},re={key:1},me={key:2},Xe=R({__name:"site_menu",setup(pe){const V=j(),w=B("system"),$=V.meta.title,r=O({loading:!0,system:[],application:[]}),g=()=>{r.loading=!0,P("site").then(({data:d})=>{r.loading=!1;const y=[],p=[];d.forEach(o=>{o.addon==""?y.push(o):p.push(o)}),r.system=y,r.application=p}).catch(()=>{})};g();const q=()=>{z.confirm(v("div",null,[v("p",null,e("initializeMenuTipsOne")),v("p",null,e("initializeMenuTipsTwo"))]),e("warning"),{confirmButtonText:e("confirm"),cancelButtonText:e("cancel")}).then(()=>{S().then(d=>{location.reload()}).catch(()=>{})}).catch(()=>{})},f=B(null),F=()=>{f.value.setFormData({app_type:"site"}),f.value.showDialog=!0},T=d=>{f.value.setFormData(d),f.value.showDialog=!0},x=d=>{z.confirm(e("menuDeleteTips"),e("warning"),{confirmButtonText:e("confirm"),cancelButtonText:e("cancel"),type:"warning"}).then(()=>{U("site",d).then(y=>{g()}).catch(()=>{})})};return(d,y)=>{const p=Z,o=A,C=X,b=G,D=H,E=J,I=K,L=Q,M=W;return s(),_("div",ee,[l(L,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[h("div",te,[h("span",ae,i(t($)),1),h("div",ne,[l(p,{type:"primary",class:"w-[100px]",onClick:F},{default:n(()=>[m(i(t(e)("addMenu")),1)]),_:1}),l(p,{class:"w-[100px]",onClick:q},{default:n(()=>[m(i(t(e)("initializeMenu")),1)]),_:1})])]),l(I,{modelValue:w.value,"onUpdate:modelValue":y[0]||(y[0]=a=>w.value=a)},{default:n(()=>[l(E,{label:t(e)("system"),name:"system"},{default:n(()=>[N((s(),u(D,{data:r.system,"row-key":"menu_key",size:"large"},{empty:n(()=>[h("span",null,i(r.loading?"":t(e)("emptyData")),1)]),default:n(()=>[l(o,{prop:"menu_name","show-overflow-tooltip":!0,label:t(e)("menuName"),"min-width":"150"},null,8,["label"]),l(o,{label:t(e)("icon"),width:"100",align:"center"},{default:n(({row:a})=>[a.icon?(s(),u(C,{key:0,name:a.icon,size:"18px"},null,8,["name"])):c("",!0)]),_:1},8,["label"]),l(o,{label:t(e)("menuType"),width:"80"},{default:n(({row:a})=>[a.menu_type==0?(s(),_("div",le,i(t(e)("menuTypeDir")),1)):a.menu_type==1?(s(),_("div",ie,i(t(e)("menuTypeMenu")),1)):a.menu_type==2?(s(),_("div",oe,i(t(e)("menuTypeButton")),1)):c("",!0)]),_:1},8,["label"]),l(o,{prop:"api_url",label:t(e)("authId"),"min-width":"150",align:"center"},null,8,["label"]),l(o,{label:t(e)("status"),"min-width":"120",align:"center"},{default:n(({row:a})=>[a.status==1?(s(),u(b,{key:0,class:"ml-2",type:"success"},{default:n(()=>[m(i(t(e)("statusNormal")),1)]),_:1})):c("",!0),a.status==0?(s(),u(b,{key:1,class:"ml-2",type:"error"},{default:n(()=>[m(i(t(e)("statusDeactivate")),1)]),_:1})):c("",!0)]),_:1},8,["label"]),l(o,{prop:"sort",label:t(e)("sort"),"min-width":"100"},null,8,["label"]),l(o,{label:t(e)("operation"),align:"right",fixed:"right",width:"130"},{default:n(({row:a})=>[l(p,{type:"primary",link:"",onClick:k=>T(a)},{default:n(()=>[m(i(t(e)("edit")),1)]),_:2},1032,["onClick"]),l(p,{type:"primary",link:"",onClick:k=>x(a.menu_key)},{default:n(()=>[m(i(t(e)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[M,r.loading]])]),_:1},8,["label"]),l(E,{label:t(e)("application"),name:"application"},{default:n(()=>[N((s(),u(D,{data:r.application,"row-key":"menu_key",size:"large"},{empty:n(()=>[h("span",null,i(r.loading?"":t(e)("emptyData")),1)]),default:n(()=>[l(o,{prop:"menu_name","show-overflow-tooltip":!0,label:t(e)("menuName"),"min-width":"150"},null,8,["label"]),l(o,{label:t(e)("icon"),width:"100",align:"center"},{default:n(({row:a})=>[a.icon?(s(),u(C,{key:0,name:a.icon,size:"18px"},null,8,["name"])):c("",!0)]),_:1},8,["label"]),l(o,{label:t(e)("menuType"),width:"80"},{default:n(({row:a})=>[a.menu_type==0?(s(),_("div",se,i(t(e)("menuTypeDir")),1)):a.menu_type==1?(s(),_("div",re,i(t(e)("menuTypeMenu")),1)):a.menu_type==2?(s(),_("div",me,i(t(e)("menuTypeButton")),1)):c("",!0)]),_:1},8,["label"]),l(o,{prop:"api_url",label:t(e)("authId"),"min-width":"150",align:"center"},null,8,["label"]),l(o,{label:t(e)("status"),"min-width":"120",align:"center"},{default:n(({row:a})=>[a.status==1?(s(),u(b,{key:0,class:"ml-2",type:"success"},{default:n(()=>[m(i(t(e)("statusNormal")),1)]),_:1})):c("",!0),a.status==0?(s(),u(b,{key:1,class:"ml-2",type:"error"},{default:n(()=>[m(i(t(e)("statusDeactivate")),1)]),_:1})):c("",!0)]),_:1},8,["label"]),l(o,{prop:"sort",label:t(e)("sort"),"min-width":"100"},null,8,["label"]),l(o,{label:t(e)("operation"),align:"right",fixed:"right",width:"130"},{default:n(({row:a})=>[l(p,{type:"primary",link:"",onClick:k=>T(a)},{default:n(()=>[m(i(t(e)("edit")),1)]),_:2},1032,["onClick"]),l(p,{type:"primary",link:"",onClick:k=>x(a.menu_key)},{default:n(()=>[m(i(t(e)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[M,r.loading]])]),_:1},8,["label"])]),_:1},8,["modelValue"]),l(Y,{ref_key:"editMenuDialog",ref:f,"menu-tree":r.data,onComplete:g},null,8,["menu-tree"])]),_:1})])}}});export{Xe as default};
