import{d as ae,y as se,f as ne,r as x,n as B,aN as re,b4 as ie,a5 as T,h as _,c as b,e as l,w as n,u as s,aT as de,Z as D,s as H,a as r,x as R,F as L,W as M,B as ue,C as j,t as d,q as a,v as me,i as m,_ as pe,b8 as z,aU as ce,a9 as _e,a0 as ve,K as fe,M as xe,L as be,E as ye,ap as ge,au as Ce,av as ke,bx as Ve,aq as he,N as we,a3 as Ee}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                    *//* empty css                        *//* empty css                 *//* empty css                  *//* empty css                       *//* empty css                 */import{_ as Fe}from"./index.vue_vue_type_style_index_0_lang-1eb01cbb.js";import"./el-form-item-4ed993c7.js";import Ue from"./index-e3ceb692.js";/* empty css                 *//* empty css                        *//* empty css                *//* empty css                       */import{z as $e,A as Pe}from"./diy-aea57979.js";import{S as Ie}from"./sortable.esm-be94e56d.js";/* empty css                        */import{r as Se}from"./range-77a5ce89.js";import{_ as Ne}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                   *//* empty css                     */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css               *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                      *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";const Be={class:"main-container"},Te={class:"flex"},De={class:"w-[360px] h-[400px] absolute mr-[30px] border-[1px] border-gray-300"},He={class:"image-slot flex justify-center items-center mt-1"},Re={class:"flex-1 ml-[430px]"},Le={class:"flex items-center border-l-[3px] border-primary pl-[5px] leading-[1.1] mt-[10px]"},Me={class:"text-[14px]"},je={class:"text-[14px] text-primary mx-[3px]"},ze={class:"text-[14px]"},qe={class:"text-[12px] ml-[8px] text-gray-500"},Oe=["data-id"],Ae={class:"flex align-center"},Ge={class:"flex flex-col justify-center items-center"},Je={class:"mr-[10px] text-sm"},Ke={class:"flex flex-col justify-center items-center"},We={class:"mr-[10px] text-sm"},Ye={class:"flex align-center"},Ze={class:"flex align-center"},Qe={class:"flex align-center"},Xe={class:"fixed-footer-wrap"},et={class:"fixed-footer"},tt=ae({__name:"tabbar_edit",setup(lt){const g=se(),q=ne(),O=g.meta.title,w=x("navPicture"),p=x(!1);g.query.key=g.query.key||"";const t=B({key:"",info:{},value:{backgroundColor:"#FFFFFF",textColor:"#333333",textHoverColor:"#333333",type:"1",list:[]}}),A=B({text:"",link:{name:"",title:"",parent:"",url:""},iconSelectPath:"",iconPath:""}),E=()=>{t.value.list.length>=5||t.value.list.push({...A})};E();const G=i=>{t.value.list.splice(i,1)},F=x();(()=>{p.value=!0,$e({key:g.query.key}).then(i=>{p.value=!1,Object.keys(t).forEach((e,u)=>{t[e]=i.data[e]})}).catch(()=>{p.value=!1})})();const J=async i=>{if(K())return!1;p.value||!i||await i.validate(async e=>{e&&(p.value=!0,Pe({key:t.key,value:t.value}).then(u=>{p.value=!1}).catch(()=>{p.value=!1}))})},U=()=>{q.push("/diy/tabbar")},K=()=>{if(t.value.list.length<2)return z({type:"error",message:a("leastTwoNav")}),!0;try{const i=x("");t.value.list.forEach((e,u)=>{if(e.iconPath||(i.value=`${a("pleaseUpload")}${u+1}${a("navIcon")}`),e.iconSelectPath||(i.value=`${a("pleaseUpload")}${u+1}${a("navSelectIcon")}`),e.text||(i.value=`${a("pleaseEnter")}[${u+1}${a("navTitle")}`),e.link.url||(i.value=`${a("pleaseChoose")}${u+1}${a("navLink")}`),i.value)throw z({type:"error",message:i.value}),Error()})}catch{return!0}return!1},$=x();re(()=>{const i=Ie.create($.value,{group:"item-wrap",animation:200,filter:".not-sort",onEnd:e=>{const u=t.value.list[e.oldIndex];t.value.list.splice(e.oldIndex,1),t.value.list.splice(e.newIndex,0,u),ie(()=>{i.sort(Se(t.value.list.length).map(C=>C.toString()))})}})});const P=x(!1),W=i=>{P.value=i};return(i,e)=>{const u=ce,C=_e,Y=T("Picture"),I=ve,Z=fe,S=Ue,v=xe,k=be,Q=Fe,X=T("CircleCloseFilled"),f=ye,N=ge,V=Ce,ee=ke,h=Ve,te=he,le=we,oe=Ee;return _(),b("div",Be,[l(C,{class:"card !border-none",shadow:"never"},{default:n(()=>[l(u,{content:s(O),icon:s(de),onBack:e[0]||(e[0]=o=>U())},null,8,["content","icon"])]),_:1}),D((_(),H(C,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:n(()=>[r("div",Te,[r("div",De,[r("div",{class:"flex items-center justify-between absolute h-[60px] left-[0px] right-[0px] bottom-[0px] border-[1px] border-primary",style:R({backgroundColor:t.value.backgroundColor})},[(_(!0),b(L,null,M(t.value.list,(o,y)=>(_(),b("div",{class:"flex flex-1 flex-col items-center justify-center",key:"b"+y},[["1","2"].includes(t.value.type.toString())?(_(),H(Z,{key:0,class:"w-[22px] h-[22px] mb-[5px] leading-1",src:s(ue)(o.iconPath),fit:i.contain},{error:n(()=>[r("div",He,[l(I,null,{default:n(()=>[l(Y,{class:"text-3xl text-gray-500"})]),_:1})])]),_:2},1032,["src","fit"])):j("",!0),["1","3"].includes(t.value.type.toString())?(_(),b("span",{key:1,class:"text-[12px]",style:R({color:t.value.textColor})},d(o.text),5)):j("",!0)]))),128))],4)]),r("div",Re,[r("div",Le,[r("span",Me,d(s(a)("editing")),1),r("span",je,d(t.info.title),1),r("span",ze,d(s(a)("bottomNav")),1),r("span",qe,d(s(a)("bottomNavHint")),1)]),l(le,{model:t.value,"label-width":"100px",ref_key:"formRef",ref:F},{default:n(()=>[l(te,{modelValue:w.value,"onUpdate:modelValue":e[11]||(e[11]=o=>w.value=o),class:"demo-tabs mt-[15px]"},{default:n(()=>[l(N,{label:s(a)("navImage"),name:"navPicture"},{default:n(()=>[r("div",{ref_key:"navItemRef",ref:$},[(_(!0),b(L,null,M(t.value.list,(o,y)=>(_(),b("div",{key:"a"+y,"data-id":y,class:me(["item-wrap border-2 border-dashed pt-[18px] m-[10px] mb-[15px] relative list-item",{"not-sort":P.value}])},[l(v,{label:s(a)("navIconOne")},{default:n(()=>[r("div",Ae,[r("div",Ge,[l(S,{modelValue:o.iconPath,"onUpdate:modelValue":c=>o.iconPath=c,width:"60px",height:"60px",limit:1},null,8,["modelValue","onUpdate:modelValue"]),r("span",Je,d(s(a)("uploadImgUnselected")),1)]),r("div",Ke,[l(S,{modelValue:o.iconSelectPath,"onUpdate:modelValue":c=>o.iconSelectPath=c,width:"60px",height:"60px",limit:1},null,8,["modelValue","onUpdate:modelValue"]),r("span",We,d(s(a)("uploadImgSelected")),1)])])]),_:2},1032,["label"]),l(v,{label:s(a)("navTitleOne")},{default:n(()=>[l(k,{class:"!w-[215px]",modelValue:o.text,"onUpdate:modelValue":c=>o.text=c,modelModifiers:{trim:!0},placeholder:s(a)("titleContent"),maxlength:"5","show-word-limit":""},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label"]),l(v,{label:s(a)("navLinkOne")},{default:n(()=>[l(Q,{modelValue:o.link,"onUpdate:modelValue":c=>o.link=c,ignore:["DIY_JUMP_OTHER_APPLET"],onConfirm:W},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label"]),l(I,{class:"close-icon cursor-pointer -top-[11px] -right-[8px]",onClick:c=>G(y)},{default:n(()=>[l(X)]),_:2},1032,["onClick"])],10,Oe))),128))],512),D(l(f,{type:"primary",class:"mt-[15px]",onClick:E},{default:n(()=>[m(d(s(a)("addnav")),1)]),_:1},512),[[pe,t.value.list.length<5]])]),_:1},8,["label"]),l(N,{label:s(a)("styleSet"),name:"setStyle"},{default:n(()=>[l(v,{label:s(a)("navType")},{default:n(()=>[l(ee,{modelValue:t.value.type,"onUpdate:modelValue":e[1]||(e[1]=o=>t.value.type=o),class:"ml-4"},{default:n(()=>[l(V,{label:"1",size:"large"},{default:n(()=>[m(d(s(a)("imageText")),1)]),_:1}),l(V,{label:"2",size:"large"},{default:n(()=>[m(d(s(a)("image")),1)]),_:1}),l(V,{label:"3",size:"large"},{default:n(()=>[m(d(s(a)("text")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),l(v,{label:s(a)("textColor")},{default:n(()=>[r("div",Ye,[l(h,{modelValue:t.value.textColor,"onUpdate:modelValue":e[2]||(e[2]=o=>t.value.textColor=o)},null,8,["modelValue"]),l(k,{class:"ml-[10px]",modelValue:t.value.textColor,"onUpdate:modelValue":e[3]||(e[3]=o=>t.value.textColor=o),modelModifiers:{trim:!0},disabled:""},null,8,["modelValue"]),l(f,{class:"ml-[10px]",type:"primary",onClick:e[4]||(e[4]=o=>t.value.textColor="#333333")},{default:n(()=>[m(d(s(a)("reset")),1)]),_:1})])]),_:1},8,["label"]),l(v,{label:s(a)("textSelectColor")},{default:n(()=>[r("div",Ze,[l(h,{modelValue:t.value.textHoverColor,"onUpdate:modelValue":e[5]||(e[5]=o=>t.value.textHoverColor=o)},null,8,["modelValue"]),l(k,{class:"ml-[10px]",modelValue:t.value.textHoverColor,"onUpdate:modelValue":e[6]||(e[6]=o=>t.value.textHoverColor=o),modelModifiers:{trim:!0},disabled:""},null,8,["modelValue"]),l(f,{class:"ml-[10px]",type:"primary",onClick:e[7]||(e[7]=o=>t.value.textHoverColor="#333333")},{default:n(()=>[m(d(s(a)("reset")),1)]),_:1})])]),_:1},8,["label"]),l(v,{label:s(a)("backgroundColor")},{default:n(()=>[r("div",Qe,[l(h,{modelValue:t.value.backgroundColor,"onUpdate:modelValue":e[8]||(e[8]=o=>t.value.backgroundColor=o)},null,8,["modelValue"]),l(k,{class:"ml-[10px]",modelValue:t.value.backgroundColor,"onUpdate:modelValue":e[9]||(e[9]=o=>t.value.backgroundColor=o),modelModifiers:{trim:!0},disabled:""},null,8,["modelValue"]),l(f,{class:"ml-[10px]",type:"primary",onClick:e[10]||(e[10]=o=>t.value.backgroundColor="#FFFFFF")},{default:n(()=>[m(d(s(a)("reset")),1)]),_:1})])]),_:1},8,["label"])]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1},8,["model"])])])]),_:1})),[[oe,p.value]]),r("div",Xe,[r("div",et,[l(f,{type:"primary",onClick:e[12]||(e[12]=o=>J(F.value))},{default:n(()=>[m(d(s(a)("save")),1)]),_:1}),l(f,{onClick:e[13]||(e[13]=o=>U())},{default:n(()=>[m(d(s(a)("back")),1)]),_:1})])])])}}});const At=Ne(tt,[["__scopeId","data-v-146909a6"]]);export{At as default};
