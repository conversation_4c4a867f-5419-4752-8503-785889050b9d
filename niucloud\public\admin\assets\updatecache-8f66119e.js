import{d,r as u,y as m,h as n,c as f,Z as h,s as x,w as v,a as e,t as c,u as a,q as t,af as g,cP as w,a9 as C,a3 as b}from"./index-30109030.js";/* empty css                   *//* empty css                */import{_ as y}from"./_plugin-vue_export-helper-c27b6911.js";const B={class:"main-container"},k={class:"flex justify-between items-center"},D={class:"text-page-title"},E={class:"flex flex-wrap px-2 plug-list pb-10 mt-[20px]"},T={class:"flex items-center p-3 w-[295px] relative plug-item mr-4 mb-4 bg-[var(--el-color-info-light-9)] cursor-pointer"},N={class:"flex flex-col ml-2"},j={class:"text-sm truncate w-[190px]"},q=["title"],I=d({__name:"updatecache",setup(L){const s=u(!1),l=m().meta.title,r=()=>{g.confirm(t("clearCacheTips"),t("warning"),{confirmButtonText:t("confirm"),cancelButtonText:t("cancel"),type:"warning"}).then(()=>{s.value=!0,w().then(i=>{s.value=!1}).catch(()=>{s.value=!1})})};return(i,o)=>{const _=C,p=b;return n(),f("div",B,[h((n(),x(_,{class:"box-card !border-none",shadow:"never"},{default:v(()=>[e("div",k,[e("span",D,c(a(l)),1)]),e("div",E,[e("div",T,[e("div",N,[e("span",j,c(a(t)("dataCache")),1),e("span",{class:"text-xs text-gray-400 mt-1 truncate w-[190px]",title:a(t)("dataCacheDesc")},c(a(t)("dataCacheDesc")),9,q)]),e("span",{class:"plug-item-operate",onClick:o[0]||(o[0]=P=>r())},c(a(t)("refresh")),1)])])]),_:1})),[[p,s.value]])])}}});const $=y(I,[["__scopeId","data-v-0ad9f40b"]]);export{$ as default};
