const t="微信打款设置",e="支付宝打款设置",c="商户号",a="请输入商户号",r="微信支付商户号（MCHID）",p="APIv3密钥",l="请输入APIv3密钥",h="微信支付商户APIv3密钥（paySignKey）",P="商户私钥",o="请上传商户私钥",i="微信支付API证书（apiclient_key.pem）",s="商户公钥",n="请上传商户公钥",C="微信支付API证书（apiclient_cert.pem）",d="支付宝应用ID",y="请输入支付宝应用ID",m="应用私钥",u="请输入应用私钥",b="应用公钥",I="请上传应用公钥",T="支付宝公钥",S="请上传支付宝公钥",K="支付宝根证书",R="请上传支付宝根证书",w="支付宝分配给开发者的应用ID",A="上传appCertPublicKey文件",D="上传alipayCertPublicKey文件",f="上传alipayRootCert文件",v="温馨提示：打款设置用于会员提现转账，发放红包等场景",_="注意：应微信方规定,在2025年1月15日前开通商家转账到零钱服务的商户号可正常使用转账功能,之后开通的不支持使用转账到零钱服务",g="微信支付公钥",k="微信支付公钥ID",x={wechatpay:t,alipay:e,mchId:c,mchIdPlaceholder:a,mchIdTips:r,mchSecretKey:p,mchSecretKeyPlaceholder:l,mchSecretKeyTips:h,mchSecretCert:P,mchSecretCertPlaceholder:o,mchSecretCertTips:i,mchPublicCertPath:s,mchPublicCertPathPlaceholder:n,mchPublicCertPathTips:C,appId:d,appIdPlaceholder:y,appSecretCert:m,appSecretCertPlaceholder:u,appPublicCertPath:b,appPublicCertPathPlaceholder:I,alipayPublicCertPath:T,alipayPublicCertPathPlaceholder:S,alipayRootCertPath:K,alipayRootCertPathPlaceholder:R,appIdTips:w,appPublicCertPathTips:A,alipayPublicCertPathTips:D,alipayRootCertPathTips:f,operationTip:v,transferTips:_,wechatpayPublicCert:g,wechatpayPublicCertId:k};export{e as alipay,T as alipayPublicCertPath,S as alipayPublicCertPathPlaceholder,D as alipayPublicCertPathTips,K as alipayRootCertPath,R as alipayRootCertPathPlaceholder,f as alipayRootCertPathTips,d as appId,y as appIdPlaceholder,w as appIdTips,b as appPublicCertPath,I as appPublicCertPathPlaceholder,A as appPublicCertPathTips,m as appSecretCert,u as appSecretCertPlaceholder,x as default,c as mchId,a as mchIdPlaceholder,r as mchIdTips,s as mchPublicCertPath,n as mchPublicCertPathPlaceholder,C as mchPublicCertPathTips,P as mchSecretCert,o as mchSecretCertPlaceholder,i as mchSecretCertTips,p as mchSecretKey,l as mchSecretKeyPlaceholder,h as mchSecretKeyTips,v as operationTip,_ as transferTips,t as wechatpay,g as wechatpayPublicCert,k as wechatpayPublicCertId};
