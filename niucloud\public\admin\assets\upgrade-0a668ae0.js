import{d as te,r as l,n as P,q as p,l as q,f as oe,h as n,c as d,Z as ae,s as C,w as o,u as c,a as e,t as u,i as E,C as f,e as a,F as se,W as le,$ as ne,a7 as re,E as ie,ab as ce,ac as de,ad as ue,ae as pe,U as _e,a9 as me,L as ve,M as fe,N as he,V as xe,a3 as ge,p as ye,g as Ve}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                *//* empty css                     *//* empty css                         *//* empty css                         *//* empty css                  */import{s as we,a as be,g as ke}from"./module-62fd0e42.js";import Fe from"./index-03d52cd7.js";import Ce from"./index-780655d9.js";import{_ as Ee}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                    *//* empty css               *//* empty css                *//* empty css                 */import"./dark-63c0c649.js";import"./upgrade-5c77adbf.js";/* empty css                 *//* empty css                   */const Ie=""+new URL("upgrade-615f348d.png",import.meta.url).href,h=w=>(ye("data-v-86c7c85c"),w=w(),Ve(),w),Le={class:"main-container"},De={key:0},Se={key:0,class:"mb-[30px]"},Ae=h(()=>e("div",{class:"title text-[16px] font-bold text-[#1D1F3A] mb-[20px]"},"版本信息",-1)),Ue={class:"text-[14px] text-[#1D1F3A] mb-[20px]"},Be=h(()=>e("span",null,"系统当前版本：",-1)),Ne={class:"font-bold"},Re={class:"flex"},Me=h(()=>e("div",{class:"w-[92px] h-[92px] rounded-[10px] flex justify-center items-center mr-[20px]"},[e("img",{src:Ie,class:"w-[92px] h-[92px]"})],-1)),Te={class:"flex flex-col justify-between items-start"},je=h(()=>e("div",{class:"text-[14px] text-[#1D1F3A]"},"系统最新版本为",-1)),Pe={class:"text-[14px] text-[#1D1F3A] font-bold"},qe={key:0,class:"text-[#9699B6] text-[16px]"},ze=h(()=>e("span",null,"已是最新",-1)),He=[ze],$e={key:1},We=h(()=>e("div",{class:"panel-title bg-[#F4F5F7] border-[#E6E6E6] border-solid border-b-[1px] h-[40px] flex items-center p-[10px]"},[e("span",{class:"text-[14px] font-500 text-[#1D1F3A]"},"升级记录")],-1)),Ze={class:"time-dialog",style:{overflow:"auto"}},Ge={class:"flex justify-between items-start flex-col"},Je={class:"text-[#1D1F3A] text-[14px] leading-[20px]"},Ke={class:"text-[#9699B6] text-[13px] mt-2"},Oe={class:"ml-auto text-[#374151] flex items-center"},Qe={class:"text-[#374151] text-[14px]"},Xe={key:0,class:"iconfont iconjiantouxia ml-[4px] !text-[10px] transition-transform duration-300"},Ye={key:1,class:"iconfont iconjiantoushang ml-[4px] !text-[10px] transition-transform duration-300"},et={key:0,class:"px-[20px] py-[20px] bg-overlay timeline-log-wrap whitespace-pre-wrap rounded-[4px] bg-[#F9F9FB] text-[#4F516D]"},tt=["innerHTML"],ot={class:"mt-[20px]"},at={class:"text-sm mt-[10px] text-info"},st={class:"mt-[20px]"},lt={class:"mt-[10px] text-right"},nt=te({__name:"upgrade",setup(w){const I=l(null),z=l(null),_=l(!1),b=l([]),L=l(0),D=l(!1),m=P({auth_code:"",auth_secret:""}),S=l(),H=P({auth_code:[{required:!0,message:p("authCodePlaceholder"),trigger:"blur"}],auth_secret:[{required:!0,message:p("authSecretPlaceholder"),trigger:"blur"}]}),x=l(!1),$=async t=>{x.value||!t||await t.validate(async s=>{s&&(x.value=!0,we(m).then(()=>{x.value=!1,B()}).catch(()=>{x.value=!1,_.value=!1}))})},g=l(!1);(()=>{g.value=!0,be().then(({data:t})=>{b.value=t,g.value=!1,D.value?(!r.value||r.value&&r.value.version_no==v.value)&&ne({message:p("versionTips"),type:"success"}):D.value=!0}).catch(()=>{g.value=!1})})();const W=q(()=>{if(!r.value||r.value.version_no===v.value)return!1;const t=String(v.value),s=String(r.value.version_no),y=parseInt(t.replace(/\./g,""),10);return parseInt(s.replace(/\./g,""),10)>y}),r=q(()=>b.value.length?b.value[0]:null),Z=()=>{_.value=!0},v=l(""),A=l("");(()=>{re().then(t=>{v.value=t.data.version.version,A.value=t.data.version.code})})();const U=l({company_name:"",site_address:"",auth_code:""}),k=l(!1),F=l(!1),G=()=>{var t;if(!U.value.auth_code){Z();return}k.value||(k.value=!0,F.value=!0,(t=I.value)==null||t.open("",()=>{k.value=!1,F.value=!1}))},B=()=>{ke().then(t=>{t.data.data&&t.data.data.length!=0&&(U.value=t.data.data,_.value=!1)}).catch(()=>{_.value=!1})};return B(),oe(),(t,s)=>{const y=ie,N=ce,J=de,K=ue,O=pe,Q=_e,R=me,M=ve,T=fe,X=he,Y=xe,ee=ge;return n(),d("div",Le,[ae((n(),C(R,{class:"box-card !border-none min-h-[500px]",shadow:"never"},{default:o(()=>[g.value?f("",!0):(n(),d("div",De,[c(r)?(n(),d("div",Se,[Ae,e("div",Ue,[Be,e("span",Ne,"v"+u(v.value),1)]),e("div",Re,[Me,e("div",Te,[je,e("div",Pe,"v"+u(c(r).version_no)+"（"+u(A.value)+"）",1),c(W)?(n(),d("div",$e,[!c(r)||c(r)&&c(r).version_no==v.value?f("",!0):(n(),C(y,{key:0,class:"w-[102px] !h-[32px]",type:"primary",loading:F.value,onClick:G},{default:o(()=>[E("一键升级")]),_:1},8,["loading"]))])):(n(),d("div",qe,He))])])])):f("",!0),We,e("div",null,[e("div",Ze,[a(Q,null,{default:o(()=>[a(O,{style:{width:"100%"}},{default:o(()=>[(n(!0),d(se,null,le(b.value,(i,j)=>(n(),C(K,{key:j,placement:"left",hollow:!0},{default:o(()=>[a(J,{modelValue:L.value,"onUpdate:modelValue":s[0]||(s[0]=V=>L.value=V),accordion:""},{default:o(()=>[a(N,{name:j},{title:o(()=>[e("div",Ge,[e("span",Je,"版本： v"+u(i.version_no),1),e("span",Ke,u(i.release_time),1)])]),icon:o(({isActive:V})=>[e("div",Oe,[e("span",Qe,u(V?"收起":"更新内容"),1),V?f("",!0):(n(),d("span",Xe)),V?(n(),d("span",Ye)):f("",!0)])]),default:o(()=>[i.upgrade_log?(n(),d("div",et,[e("div",{innerHTML:i.upgrade_log},null,8,tt)])):f("",!0)]),_:2},1032,["name"])]),_:2},1032,["modelValue"])]),_:2},1024))),128))]),_:1})]),_:1})])])]))]),_:1})),[[ee,g.value]]),a(Y,{modelValue:_.value,"onUpdate:modelValue":s[4]||(s[4]=i=>_.value=i),title:"授权码认证",width:"400px"},{default:o(()=>[a(X,{model:m,"label-width":"0",ref_key:"formRef",ref:S,rules:H,class:"page-form"},{default:o(()=>[a(R,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[a(T,{prop:"auth_code"},{default:o(()=>[a(M,{modelValue:m.auth_code,"onUpdate:modelValue":s[1]||(s[1]=i=>m.auth_code=i),modelModifiers:{trim:!0},placeholder:c(p)("authCodePlaceholder"),class:"input-width",clearable:"",size:"large"},null,8,["modelValue","placeholder"])]),_:1}),e("div",ot,[a(T,{prop:"auth_secret"},{default:o(()=>[a(M,{modelValue:m.auth_secret,"onUpdate:modelValue":s[2]||(s[2]=i=>m.auth_secret=i),modelModifiers:{trim:!0},clearable:"",placeholder:c(p)("authSecretPlaceholder"),class:"input-width",size:"large"},null,8,["modelValue","placeholder"])]),_:1})]),e("div",at,u(c(p)("authInfoTips")),1),e("div",st,[a(y,{type:"primary",class:"w-full",size:"large",loading:x.value,onClick:s[3]||(s[3]=i=>$(S.value))},{default:o(()=>[E(u(c(p)("confirm")),1)]),_:1},8,["loading"])]),e("div",lt,[a(y,{type:"primary",link:"",onClick:t.market},{default:o(()=>[E(u(c(p)("notHaveAuth")),1)]),_:1},8,["onClick"])])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),a(Fe,{ref_key:"upgradeRef",ref:I},null,512),a(Ce,{ref_key:"upgradeLogRef",ref:z},null,512)])}}});const Bt=Ee(nt,[["__scopeId","data-v-86c7c85c"]]);export{Bt as default};
