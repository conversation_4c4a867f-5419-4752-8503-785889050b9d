import{d as B,G as C,j as R,r as F,l as K,aN as N,bZ as g,h as l,s as x,w as o,e as c,u as i,c as m,B as U,W as V,F as G,a as p,y as H,H as Z,K as q,bG as A,cZ as D,U as I,O as L,A as O}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                 *//* empty css                        */import{_ as W}from"./icon-addon-339e16d0.js";import{_ as $}from"./menu-item.vue_vue_type_style_index_0_lang-5933cf52.js";import{a as z}from"./site-0a26f9e1.js";const J={key:0,class:"logo flex items-center m-auto h-[64px]"},P=p("div",{class:"flex justify-center items-center w-full h-[40px]"},[p("img",{class:"max-w-[40px]",src:W,alt:"","object-fit":"contain"})],-1),Q={key:1,class:"logo flex items-center justify-center h-[64px]"},T=p("i",{class:"text-3xl iconfont iconyunkongjian"},null,-1),X=[T],Y=p("div",{class:"h-[48px]"},null,-1),de=B({__name:"side",setup(ee){const f=C(),t=R(),y=H(),s=t.siteInfo,k=t.routers,v=t.addonIndexRoute,r=F([]),u={},w=K(()=>t.siteInfo.icon?t.siteInfo.icon:f.website.icon),E=async()=>{var d,_;const a=(await z()).data,n=((_=(d=a==null?void 0:a.marketing)==null?void 0:d.list)==null?void 0:_.map(h=>h.key))??[];Z.set({key:"darksideMarketingKeys",data:n})};if(N(()=>{E()}),k.forEach((e,a)=>{e.original_name=e.name,e.meta.addon==""?e.meta.attr==""&&e.name!="sign"&&e.name!="verify"&&(e.children&&e.children.length&&(e.name=g(e.children)),r.value.push(e)):e.meta.addon!=""&&(s==null?void 0:s.apps.length)==1&&(s==null?void 0:s.apps[0].key)==e.meta.addon&&e.meta.show?e.children?(e.children.forEach(n=>{n.original_name=n.name,n.children&&n.children.length&&(n.name=g(n.children))}),r.value.unshift(...e.children)):r.value.unshift(e):u[e.meta.addon]=e}),(s==null?void 0:s.apps.length)>1){const e=[];s==null||s.apps.forEach(a=>{u[a.key]&&(u[a.key].name=v[a.key],e.push(u[a.key]))}),r.value.unshift(...e)}return(e,a)=>{const n=q,d=A,_=D,h=I,b=L,S=O;return l(),x(S,{class:"w-[200px] h-screen flex flex-col"},{default:o(()=>[c(d,{class:"logo-wrap flex items-center justify-center h-[64px]"},{default:o(()=>[i(f).menuIsCollapse?(l(),m("div",Q,X)):(l(),m("div",J,[c(n,{style:{width:"40px",height:"40px"},src:i(U)(i(w)),fit:"contain"},{error:o(()=>[P]),_:1},8,["src"])]))]),_:1}),c(b,{class:"menu-wrap"},{default:o(()=>[c(h,null,{default:o(()=>[c(_,{"default-active":i(y).name,router:!0,"background-color":"--side-dark-color","text-color":"#fff","active-text-color":"#fff","unique-opened":!0,collapse:i(f).menuIsCollapse},{default:o(()=>[(l(!0),m(G,null,V(r.value,(j,M)=>(l(),x($,{routes:j,key:M},null,8,["routes"]))),128))]),_:1},8,["default-active","collapse"]),Y]),_:1})]),_:1})]),_:1})}}});export{de as _};
