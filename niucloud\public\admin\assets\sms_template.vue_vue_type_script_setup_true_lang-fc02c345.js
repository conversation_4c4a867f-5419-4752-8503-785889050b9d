import{d as de,n as _e,R as H,r as b,aN as fe,l as I,$ as B,h as d,c as h,a as p,e as l,w as t,u as n,q as s,F as $,W as w,s as g,i as v,t as i,Z as K,C as D,af as ve,L as ce,M as be,a4 as ye,a1 as ge,E as ke,N as he,ag as xe,ah as Ve,a2 as Ce,V as Ee,au as je,av as Te,a$ as De,ao as Pe,a3 as Re}from"./index-30109030.js";/* empty css                   *//* empty css               *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";import{u as $e,v as we,w as Ue,x as Fe,y as Ne,z as Be}from"./notice-9865bf12.js";/* empty css                       *//* empty css                 *//* empty css                   */const Le={class:"flex justify-between items-start my-[10px]"},Se={key:0,class:"text-red-600"},Oe={class:"mt-[16px] flex justify-end"},Ie={class:"dialog-footer"},Me={class:"input-width"},ze=p("div",{class:"ml-[100px] mb-[10px] mt-[-10px] text-[12px] text-[#999] leading-[20px]"},[p("div",null,"验证码：仅支持验证码类型变量"),p("div",null,"行业通知：不支持验证码类型变量"),p("div",null,"营销推广：不支持变量")],-1),Ae={class:"flex flex-1 items-center"},We={class:"w-32 mr-1"},qe={class:"dialog-footer"},Ge={key:0,class:"h-[500px] overflow-y-auto"},Je={class:"dialog-footer"},kt=de({__name:"sms_template",props:{username:{type:String,default:""},signature:{type:String,default:""}},setup(Q){const C=Q,r=_e({page:1,limit:10,total:0,loading:!1,data:[],allData:[],searchParam:{template_id:"",name:"",status:""}}),X=()=>{const{template_id:o,name:a,status:y}=r.searchParam;return r.allData.filter(_=>{const x=!o||String(_.template_id||"").includes(o),E=!a||String(_.name||"").includes(a),c=!y||_.audit_info.audit_status==y;return x&&E&&c})};H(()=>[r.limit,r.page],()=>{A()});const j=()=>{r.loading=!0,$e({sms_type:"niuyun",username:C.username}).then(o=>{r.allData=o.data,r.page=1,A()}).catch(()=>{r.loading=!1})},M=b(null),Y=o=>{o&&(r.searchParam={template_id:"",name:"",status:""},j())},z=b([]),A=()=>{const o=X();r.total=o.length;const a=(r.page-1)*r.limit,y=a+r.limit;z.value=o.slice(a,y),r.loading=!1};fe(()=>{C.username&&j()});const P=b(!1),R=b({}),ee=()=>{we("niuyun",C.username).then(o=>{R.value=o.data.repeat_list,R.value&&Object.keys(R.value).length>0?P.value=!0:j()})},te=I(()=>Object.entries(R.value).map(([o,a])=>({name:o,platforms:a}))),u=b(null),U=b(!1),ae=o=>{U.value=!0,u.value=o},le=o=>{ve.confirm(s("确定要清除报备信息吗"),s("提示"),{confirmButtonText:s("确定"),cancelButtonText:s("取消"),type:"warning"}).then(()=>{Ue(C.username,o.template_id).then(()=>{j()})}).catch(()=>{})},L=b({}),W=b({}),q=b({}),T=b(!1),f=b({template_type:1,template_key:"",params_json:{}});(()=>{Fe().then(o=>{L.value=o.data.template_params_type_list,W.value=o.data.template_type_list,q.value=o.data.template_status_list})})();const ne=I(()=>f.value.template_type===1?L.value.filter(o=>o.type==="valid_code"):L.value),S=I(()=>f.value.template_type===3&&u.value.variable&&Object.keys(u.value.variable).length>0);H(S,o=>{o&&B.error("营销推广类型不支持变量")});const F=b(!1),se=o=>{if(F.value=!0,C.signature)o.template_id?(T.value=!0,u.value=o,Ne("niuyun",C.username,{template_key:o.key}).then(y=>{var x;const _=((x=y.data)==null?void 0:x.param_json)??{};if(f.value.template_key=y.data.template_key,f.value.template_type=Number(y.data.template_type),f.value.params_json={},u.value.variable)for(const E in u.value.variable)f.value.params_json[E]=_[E]??"";F.value=!1})):(T.value=!0,F.value=!1,u.value=o,f.value.template_type=1,f.value.template_key=u.value.key,f.value.params_json={});else{B.error("请先配置签名");return}},oe=()=>{if(!u.value.sms){B.error("请先配置模版内容");return}if(Object.entries(u.value.variable).some(([a])=>!f.value.params_json[a])){B.error("请为每个变量选择类型");return}u.value.template_id&&(f.value.template_id=Number(u.value.template_id)),Be(u.value.sms_type,C.username,f.value).then(a=>{T.value=!1,j()})};return(o,a)=>{const y=ce,_=be,x=ye,E=ge,c=ke,N=he,V=xe,G=Ve,re=Ce,O=Ee,ie=je,ue=Te,pe=De,me=Pe,J=Re;return d(),h("div",null,[p("div",Le,[l(N,{inline:!0,model:r.searchParam,ref_key:"searchFormRef",ref:M},{default:t(()=>[l(_,{label:n(s)("模版ID"),prop:"template_id"},{default:t(()=>[l(y,{modelValue:r.searchParam.template_id,"onUpdate:modelValue":a[0]||(a[0]=e=>r.searchParam.template_id=e),modelModifiers:{trim:!0},placeholder:n(s)("请输入模版ID")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(_,{label:n(s)("模版名称"),prop:"name"},{default:t(()=>[l(y,{modelValue:r.searchParam.name,"onUpdate:modelValue":a[1]||(a[1]=e=>r.searchParam.name=e),modelModifiers:{trim:!0},placeholder:n(s)("请输入模版名称")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(_,{label:n(s)("状态"),prop:"status"},{default:t(()=>[l(E,{modelValue:r.searchParam.status,"onUpdate:modelValue":a[2]||(a[2]=e=>r.searchParam.status=e),placeholder:n(s)("请选择状态")},{default:t(()=>[l(x,{label:n(s)("全部"),value:""},null,8,["label"]),(d(!0),h($,null,w(q.value,(e,m)=>(d(),g(x,{key:m,label:e,value:m},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),l(_,null,{default:t(()=>[l(c,{type:"primary",onClick:a[3]||(a[3]=e=>j())},{default:t(()=>[v(i(n(s)("search")),1)]),_:1}),l(c,{onClick:a[4]||(a[4]=e=>Y(M.value))},{default:t(()=>[v(i(n(s)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),l(c,{type:"primary",onClick:a[5]||(a[5]=e=>ee())},{default:t(()=>[v(i(n(s)("同步模版状态")),1)]),_:1})]),K((d(),g(G,{data:z.value,size:"large",ref:"goodBankListTableRef"},{empty:t(()=>[p("span",null,i(r.loading?"":n(s)("emptyData")),1)]),default:t(()=>[l(V,{prop:"template_id",label:n(s)("模版ID"),"min-width":"100"},null,8,["label"]),l(V,{prop:"name",label:n(s)("模版名称"),"min-width":"130"},null,8,["label"]),l(V,{prop:"template_type_name",label:n(s)("模版类型"),"min-width":"130"},{default:t(({row:e})=>[p("div",null,i(e.template_type_name?e.template_type_name:"--"),1)]),_:1},8,["label"]),l(V,{prop:"sms",label:n(s)("模版内容"),"min-width":"200"},{default:t(({row:e})=>{var m;return[p("div",null,i((m=e.sms)==null?void 0:m.content),1)]}),_:1},8,["label"]),l(V,{prop:"audit_info",label:n(s)("审核状态"),"min-width":"130"},{default:t(({row:e})=>{var m,k,Z;return[p("div",null,[v(i((m=e.audit_info)==null?void 0:m.audit_status_name)+" ",1),(k=e.audit_info)!=null&&k.error_status_name?(d(),h("span",Se,"（"+i((Z=e.audit_info)==null?void 0:Z.error_status_name)+"）",1)):D("",!0)])]}),_:1},8,["label"]),l(V,{label:n(s)("operation"),fixed:"right",align:"right","min-width":"120"},{default:t(({row:e})=>[e.audit_info.audit_status!=2?(d(),g(c,{key:0,type:"primary",link:"",onClick:m=>se(e)},{default:t(()=>[v(i(e.audit_info.audit_status!=1&&e.audit_info.audit_status!=2?n(s)("报备"):n(s)("修改")),1)]),_:2},1032,["onClick"])):D("",!0),e.audit_info.audit_status==2?(d(),g(c,{key:1,type:"primary",link:"",onClick:m=>le(e)},{default:t(()=>[v(i(n(s)("清除报备信息")),1)]),_:2},1032,["onClick"])):D("",!0),l(c,{type:"primary",link:"",onClick:m=>ae(e)},{default:t(()=>[v(i(n(s)("详情")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[J,r.loading]]),p("div",Oe,[l(re,{"current-page":r.page,"onUpdate:current-page":a[6]||(a[6]=e=>r.page=e),"page-size":r.limit,"onUpdate:page-size":a[7]||(a[7]=e=>r.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:r.total},null,8,["current-page","page-size","total"])]),l(O,{modelValue:U.value,"onUpdate:modelValue":a[9]||(a[9]=e=>U.value=e),title:n(s)("模版详情"),width:"600px","destroy-on-close":""},{footer:t(()=>[p("span",Ie,[l(c,{type:"primary",onClick:a[8]||(a[8]=e=>U.value=!1)},{default:t(()=>[v(i(n(s)("confirm")),1)]),_:1})])]),default:t(()=>[l(N,{"label-width":"100px",ref:"formRef",class:"page-form"},{default:t(()=>[l(_,{label:n(s)("短信类型"),prop:"template_id"},{default:t(()=>[p("div",null,i(u.value.sms_type),1)]),_:1},8,["label"]),l(_,{label:n(s)("模版名称"),prop:"template_id"},{default:t(()=>[p("div",null,i(u.value.name),1)]),_:1},8,["label"]),l(_,{label:n(s)("模版类型"),prop:"title"},{default:t(()=>[p("div",null,i(u.value.title),1)]),_:1},8,["label"]),u.value.sms?(d(),g(_,{key:0,label:n(s)("短信内容"),prop:"title"},{default:t(()=>{var e;return[p("div",null,i((e=u.value.sms)==null?void 0:e.content),1)]}),_:1},8,["label"])):D("",!0),l(_,{label:n(s)("审核状态"),prop:"title"},{default:t(()=>[p("div",null,i(u.value.audit_info.audit_status_name),1)]),_:1},8,["label"])]),_:1},512)]),_:1},8,["modelValue","title"]),l(O,{modelValue:T.value,"onUpdate:modelValue":a[13]||(a[13]=e=>T.value=e),title:n(s)("模版报备"),width:"820px","destroy-on-close":""},{footer:t(()=>[p("span",qe,[l(c,{onClick:a[11]||(a[11]=e=>T.value=!1)},{default:t(()=>[v(i(n(s)("cancel")),1)]),_:1}),l(c,{type:"primary",onClick:a[12]||(a[12]=e=>oe()),disabled:n(S)},{default:t(()=>[v(i(n(s)("confirm")),1)]),_:1},8,["disabled"])])]),default:t(()=>[K((d(),g(N,{"label-width":"100px",ref:"formRef",class:"page-form"},{default:t(()=>[l(_,{label:n(s)("模版名称"),prop:"template_id"},{default:t(()=>[p("div",Me,i(u.value.name),1)]),_:1},8,["label"]),l(_,{label:n(s)("模版类型"),prop:"title"},{default:t(()=>[l(ue,{modelValue:f.value.template_type,"onUpdate:modelValue":a[10]||(a[10]=e=>f.value.template_type=e)},{default:t(()=>[(d(!0),h($,null,w(Object.entries(W.value),([e,m])=>(d(),g(ie,{key:e,label:Number(e)},{default:t(()=>[v(i(m),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),ze,u.value.variable&&Object.keys(u.value.variable).length>0?(d(),g(_,{key:0,label:n(s)("变量类型"),prop:"params_json"},{default:t(()=>[(d(!0),h($,null,w(u.value.variable,(e,m)=>(d(),h("div",{key:m,class:"mb-2 flex items-center"},[p("div",Ae,[p("div",We,i(e),1),l(E,{modelValue:f.value.params_json[m],"onUpdate:modelValue":k=>f.value.params_json[m]=k,placeholder:"请选择类型",class:"flex-1",filterable:"",clearable:"",disabled:n(S)},{default:t(()=>[(d(!0),h($,null,w(n(ne),k=>(d(),g(x,{key:k.type,label:k.name+"（"+k.desc+"）",value:k.type},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])]))),128))]),_:1},8,["label"])):D("",!0)]),_:1})),[[J,F.value]])]),_:1},8,["modelValue","title"]),l(O,{modelValue:P.value,"onUpdate:modelValue":a[16]||(a[16]=e=>P.value=e),title:n(s)("同步模版状态"),width:"800px","destroy-on-close":""},{footer:t(()=>[p("span",Je,[l(c,{onClick:a[14]||(a[14]=e=>P.value=!1)},{default:t(()=>[v(i(n(s)("cancel")),1)]),_:1}),l(c,{type:"primary",onClick:a[15]||(a[15]=e=>P.value=!1)},{default:t(()=>[v(i(n(s)("confirm")),1)]),_:1})])]),default:t(()=>[l(pe,{type:"warning",closable:!1,class:"!mb-[10px]"},{default:t(()=>[v(" 以下模版名称重复，请先调整模版名称后重新同步模版 ")]),_:1}),l(N,{"label-width":"100px",ref:"formRef",class:"page-form"},{default:t(()=>[Object.keys(R.value).length?(d(),h("div",Ge,[l(G,{data:n(te),border:"",style:{width:"100%"}},{default:t(()=>[l(V,{label:"模版名称",prop:"name"}),l(V,{label:"插件名称"},{default:t(({row:e})=>[(d(!0),h($,null,w(e.platforms,m=>(d(),g(me,{key:m,class:"mr-1 mb-1"},{default:t(()=>[v(i(m),1)]),_:2},1024))),128))]),_:1})]),_:1},8,["data"])])):D("",!0)]),_:1},512)]),_:1},8,["modelValue","title"])])}}});export{kt as _};
