import{d as z,y as V,n as L,r as y,h as _,c as S,e as o,w as n,a as f,u as r,q as l,i as p,t as m,Z as j,s as h,aD as I,af as M,aE as U,aF as q,L as Z,M as A,E as G,N as H,ag as J,ao as K,ah as O,a2 as Q,a9 as W,a3 as X}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";import{_ as Y}from"./edit-role.vue_vue_type_script_setup_true_async_true_lang-93a09892.js";import{_ as ee}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 */const te={class:"main-container"},ae={class:"flex justify-between items-center mt-[20px]"},oe={class:"mt-[16px] flex justify-end"},le=z({__name:"role",setup(ne){V().meta.title;const e=L({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{search:""}}),k=y(),R=s=>{s&&(s.resetFields(),i())},i=(s=1)=>{e.loading=!0,e.page=s,I({page:e.page,limit:e.limit,role_name:e.searchParam.search}).then(t=>{e.loading=!1,e.data=t.data.data,e.total=t.data.total}).catch(()=>{e.loading=!1})};i();const c=y(null),D=()=>{c.value.setFormData(),c.value.showDialog=!0},F=s=>{c.value.setFormData(s),c.value.showDialog=!0},T=s=>{M.confirm(l("roleDeleteTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{U(s).then(()=>{i()}).catch(()=>{})})},g=y(!1),b=(s,t)=>{g.value||(g.value=!0,q({role_id:s,status:t}).then(C=>{i(),g.value=!1}))};return(s,t)=>{const C=Z,x=A,d=G,$=H,u=J,E=K,B=O,w=Q,N=W,P=X;return _(),S("div",te,[o(N,{class:"box-card !border-none setting-card",shadow:"never"},{default:n(()=>[f("div",ae,[o($,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:k},{default:n(()=>[o(x,{label:r(l)("roleName"),prop:"search"},{default:n(()=>[o(C,{modelValue:e.searchParam.search,"onUpdate:modelValue":t[0]||(t[0]=a=>e.searchParam.search=a),modelModifiers:{trim:!0},class:"w-[240px]",placeholder:r(l)("roleNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(x,null,{default:n(()=>[o(d,{type:"primary",onClick:t[1]||(t[1]=a=>i())},{default:n(()=>[p(m(r(l)("search")),1)]),_:1}),o(d,{onClick:t[2]||(t[2]=a=>R(k.value))},{default:n(()=>[p(m(r(l)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),o(d,{type:"primary",class:"w-[100px] self-start",onClick:D},{default:n(()=>[p(m(r(l)("addRole")),1)]),_:1})]),f("div",null,[j((_(),h(B,{data:e.data,size:"large"},{empty:n(()=>[f("span",null,m(e.loading?"":r(l)("emptyData")),1)]),default:n(()=>[o(u,{prop:"role_name",label:r(l)("roleName")},null,8,["label"]),o(u,{label:r(l)("status")},{default:n(({row:a})=>[a.status==1?(_(),h(E,{key:0,type:"success",onClick:v=>b(a.role_id,0),class:"cursor-pointer"},{default:n(()=>[p(m(a.status_name),1)]),_:2},1032,["onClick"])):(_(),h(E,{key:1,type:"error",onClick:v=>b(a.role_id,1),class:"cursor-pointer"},{default:n(()=>[p(m(a.status_name),1)]),_:2},1032,["onClick"]))]),_:1},8,["label"]),o(u,{prop:"create_time",label:r(l)("createTime")},null,8,["label"]),o(u,{label:r(l)("operation"),align:"right",fixed:"right",width:"130"},{default:n(({row:a})=>[o(d,{type:"primary",link:"",onClick:v=>F(a)},{default:n(()=>[p(m(r(l)("edit")),1)]),_:2},1032,["onClick"]),o(d,{type:"primary",link:"",onClick:v=>T(a.role_id)},{default:n(()=>[p(m(r(l)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[P,e.loading]]),f("div",oe,[o(w,{"current-page":e.page,"onUpdate:current-page":t[3]||(t[3]=a=>e.page=a),"page-size":e.limit,"onUpdate:page-size":t[4]||(t[4]=a=>e.limit=a),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:t[5]||(t[5]=a=>i()),onCurrentChange:i},null,8,["current-page","page-size","total"])])]),o(Y,{ref_key:"editRoleDialog",ref:c,onComplete:t[6]||(t[6]=a=>i())},null,512)]),_:1})])}}});const we=ee(le,[["__scopeId","data-v-46463133"]]);export{we as default};
