import{d as ae,r as m,aN as le,b4 as j,a5 as te,h as i,c as g,e as o,w as n,u as l,q as a,Z as G,s as S,a as r,t as v,F as N,W as B,c_ as oe,C as $,i as se,$ as H,L as ne,M as re,a0 as ue,N as de,ap as ce,aW as ie,a4 as me,a1 as pe,aq as _e,a9 as fe,E as ve,a3 as he}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 */import{S as be}from"./sortable.esm-be94e56d.js";import{ak as ge,al as xe,am as Ce,an as we,ao as Se,ap as Ve}from"./goods-e07707eb.js";import{r as ye}from"./range-77a5ce89.js";import{_ as Ee}from"./_plugin-vue_export-helper-c27b6911.js";const ke={class:"main-container"},Te={class:"flex justify-between items-center mb-[20px]"},Ue={class:"text-[14px]"},Fe={class:"text-[12px] text-[#999]"},Ge={class:"flex justify-between items-center mb-[20px]"},Ne={class:"text-[14px]"},Be={class:"search-wrap"},De={class:"text-[12px] text-[#999]"},Ie={class:"text-[12px] text-[#999] flex flex-col leading-[20px] mt-1"},Me={class:"text-[12px] text-[#999]"},Le={class:"text-[12px] text-[#999]"},Pe={key:0,class:"fixed-footer-wrap"},We={class:"fixed-footer"},qe=ae({__name:"search_config",setup(Re){const x=m("search"),b=m(!1),C=m(!1),w=m(!1),f=m({default_word:"",search_words:[]}),p=m({default_sort:"",sort_type:"",sort_column:"0",init:{sort_type:{},sort_column:{}}}),V=m(-1),D=m(),d=m([]);le(()=>{j(()=>{P()})});const I=()=>{b.value=!0,ge().then(s=>{f.value=s.data,f.value.search_words&&f.value.search_words.length&&(d.value=[],f.value.search_words.forEach(e=>{d.value.push({id:W(),name:e})})),b.value=!1}).catch(()=>{b.value=!1})};I();const M=()=>{xe().then(s=>{V.value=Number(s.data.is_enable)})};M();const L=()=>{Ce().then(s=>{p.value=s.data})};L();const z=s=>{x.value=s.props.name},K=(s,e)=>{s&&d.value.some((u,E)=>E!==e&&u.name===s)&&(H.error(a("searchTips")),d.value[e].name="")},y=m(),P=()=>{if(!y.value)return;const s=be.create(y.value,{group:"draggable-element",animation:200,onEnd:e=>{const _=d.value[e.oldIndex];d.value.splice(e.oldIndex,1),d.value.splice(e.newIndex,0,_),j(()=>{s.sort(ye(d.value.length).map(u=>u.toString()))})}})},W=(s=5)=>Number(Math.random().toString().substr(3,s)+Date.now()).toString(36),O=()=>{d.value.push({id:W(),name:""}),P()},Z=s=>{d.value.splice(s,1)},c=m(!1),A=async s=>{await s.validate(async e=>{if(e)if(x.value==="search"){for(let u=0;u<d.value.length;u++)if(!d.value[u].name){H.error(a("keyWordTips"));return}if(c.value)return;c.value=!0,b.value=!0;const _=d.value.map(u=>u.name);f.value.search_words=_.join(","),we(f.value).then(u=>{c.value=!1,I()}).catch(()=>{b.value=!1,c.value=!1})}else if(x.value==="code"){if(c.value)return;c.value=!0,C.value=!0,Se({is_enable:V.value}).then(_=>{M(),c.value=!1,C.value=!1}).catch(()=>{c.value=!1,C.value=!1})}else{if(c.value)return;c.value=!0,w.value=!0,Ve(p.value).then(_=>{L(),c.value=!1,w.value=!1}).catch(()=>{c.value=!1,w.value=!1})}})};return(s,e)=>{const _=ne,u=re,E=te("CircleCloseFilled"),J=ue,k=de,T=ce,Q=ie,q=me,R=pe,X=_e,Y=fe,ee=ve,U=he;return i(),g("div",ke,[o(Y,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[o(X,{modelValue:x.value,"onUpdate:modelValue":e[5]||(e[5]=t=>x.value=t),onTabClick:z},{default:n(()=>[o(T,{label:l(a)("goodsSearch"),name:"search"},{default:n(()=>[G((i(),S(k,{model:f.value,"label-width":"140px",ref_key:"formRef",ref:D,class:"page-form"},{default:n(()=>[r("div",Te,[r("span",Ue,v(l(a)("defaultSearch")),1)]),o(u,{label:l(a)("defaultWord")},{default:n(()=>[r("div",null,[o(_,{modelValue:f.value.default_word,"onUpdate:modelValue":e[0]||(e[0]=t=>f.value.default_word=t),modelModifiers:{trim:!0},type:"textarea",clearable:"",placeholder:l(a)("defaultWordPlaceholder"),class:"input-width",maxlength:"12"},null,8,["modelValue","placeholder"]),r("div",Fe,v(l(a)("defaultWordTips")),1)])]),_:1},8,["label"]),r("div",Ge,[r("span",Ne,v(l(a)("hotSearch")),1)]),o(u,{label:l(a)("indexKeyword")},{default:n(()=>[r("div",Be,[r("ul",{ref_key:"searchRef",ref:y},[(i(!0),g(N,null,B(d.value,(t,h)=>(i(),g("li",{class:"draggable-element",key:t.id},[o(_,{modelValue:t.name,"onUpdate:modelValue":F=>t.name=F,modelModifiers:{trim:!0},clearable:"",placeholder:l(a)("searchPlaceholder"),class:"input-width","suffix-icon":l(oe),maxlength:"12",onBlur:F=>K(t.name,h)},null,8,["modelValue","onUpdate:modelValue","placeholder","suffix-icon","onBlur"]),o(J,{class:"icon",size:20,color:"#7b7b7b",onClick:F=>Z(h)},{default:n(()=>[o(E)]),_:2},1032,["onClick"])]))),128))],512),d.value.length<10?(i(),g("span",{key:0,class:"text-primary text-[14px] cursor-pointer",onClick:O},v(l(a)("addSearch")),1)):$("",!0)])]),_:1},8,["label"])]),_:1},8,["model"])),[[U,b.value]])]),_:1},8,["label"]),o(T,{label:l(a)("goodsCode"),name:"code"},{default:n(()=>[G((i(),S(k,{"label-width":"140px",class:"page-form"},{default:n(()=>[o(u,{label:l(a)("enable")},{default:n(()=>[r("div",null,[o(Q,{modelValue:V.value,"onUpdate:modelValue":e[1]||(e[1]=t=>V.value=t),"active-value":1,"inactive-value":0},null,8,["modelValue"]),r("div",De,v(l(a)("enableTips")),1)])]),_:1},8,["label"])]),_:1})),[[U,C.value]])]),_:1},8,["label"]),o(T,{label:l(a)("goodSort"),name:"sort"},{default:n(()=>[G((i(),S(k,{"label-width":"140px",class:"page-form"},{default:n(()=>[o(u,{label:l(a)("sortType")},{default:n(()=>[r("div",null,[o(R,{modelValue:p.value.sort_type,"onUpdate:modelValue":e[2]||(e[2]=t=>p.value.sort_type=t),placeholder:l(a)("sortTypePlaceholder")},{default:n(()=>[(i(!0),g(N,null,B(p.value.init.sort_type,(t,h)=>(i(),S(q,{key:h,label:t,value:h},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),r("div",Ie,[r("span",null,v(l(a)("sortTypeTips")),1)])])]),_:1},8,["label"]),o(u,{label:l(a)("sortColumn")},{default:n(()=>[r("div",null,[o(R,{modelValue:p.value.sort_column,"onUpdate:modelValue":e[3]||(e[3]=t=>p.value.sort_column=t),placeholder:l(a)("sortColumnPlaceholder")},{default:n(()=>[(i(!0),g(N,null,B(p.value.init.sort_column,(t,h)=>(i(),S(q,{key:h,label:t,value:h},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),r("div",Me,v(l(a)("sortColumnTips")),1)])]),_:1},8,["label"]),o(u,{label:l(a)("defaultSort")},{default:n(()=>[r("div",null,[o(_,{modelValue:p.value.default_sort,"onUpdate:modelValue":e[4]||(e[4]=t=>p.value.default_sort=t),modelModifiers:{trim:!0},clearable:"",placeholder:l(a)("defaultSortPlaceholder"),class:"input-width-short",maxlength:"8"},null,8,["modelValue","placeholder"]),r("div",Le,v(l(a)("defaultSortTips")),1)])]),_:1},8,["label"])]),_:1})),[[U,w.value]])]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1}),!b.value||!C.value||!w.value?(i(),g("div",Pe,[r("div",We,[o(ee,{type:"primary",onClick:e[6]||(e[6]=t=>A(D.value))},{default:n(()=>[se(v(l(a)("save")),1)]),_:1})])])):$("",!0)])}}});const na=Ee(qe,[["__scopeId","data-v-d4a80175"]]);export{na as default};
