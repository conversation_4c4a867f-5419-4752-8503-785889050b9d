import{d as T,y as L,f as q,r as v,Z as R,h as a,c as l,e as i,w as x,u as n,aT as $,a as t,t as e,q as o,F as d,W as m,s as j,B as z,$ as G,aU as H,a9 as M,ag as O,ah as P,a3 as S}from"./index-30109030.js";/* empty css                   *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                        *//* empty css               *//* empty css                *//* empty css                       */import{g as U}from"./verify-1f1231ea.js";const W={class:"main-container"},Z={class:"panel-title !text-sm"},A={class:"flex items-center mt-[15px]"},J={class:"text-[14px] w-[130px] text-right mr-[20px]"},K={class:"text-[14px] text-[#666666]"},Q={class:"flex items-center mt-[15px]"},X={class:"text-[14px] w-[130px] text-right mr-[20px]"},Y=t("span",{class:"text-[14px] text-[#666666]"}," 已核销 ",-1),I={class:"flex items-center mt-[15px]"},tt={class:"text-[14px] w-[130px] text-right mr-[20px]"},et={class:"text-[14px] text-[#666666]"},st={class:"flex items-center mt-[15px]"},at={class:"text-[14px] w-[130px] text-right mr-[20px]"},nt={class:"text-[14px] text-[#666666]"},lt={class:"text-[14px] w-[130px] text-right mr-[20px]"},ot={class:"text-[14px] text-[#666666]"},it={class:"text-[14px] w-[130px] text-right mr-[20px]"},rt={class:"text-[14px] text-[#666666]"},ct={class:"panel-title !text-sm"},xt={class:"text-[14px] w-[130px] text-right mr-[20px]"},pt={class:"text-[14px] text-[#666666]"},_t={class:"panel-title !text-sm"},dt={class:"flex"},mt={class:"flex items-center shrink-0"},ut=["src"],ht={class:"flex flex-col"},vt={class:"multi-hidden text-[14px]"},Lt=T({__name:"verify_detail",setup(ft){const y=L(),b=q(),E=y.meta.title,C=()=>{b.push("/marketing/verify")},u=v(!0),w=y.query.code,r=v({}),f=v({}),k=v([]);return(async()=>{if(u.value=!0,w){const c=await(await U(w)).data;if(!c||Object.keys(c).length==0)return G.error(o("memberNull")),setTimeout(()=>{b.go(-1)},2e3),!1;r.value=c,f.value=c.value.content||{},k.value=c.value.list||[],u.value=!1}else u.value=!1})(),(c,D)=>{const V=H,h=M,B=O,F=P,N=S;return R((a(),l("div",W,[i(h,{class:"card !border-none",shadow:"never"},{default:x(()=>[i(V,{content:n(E),icon:n($),onBack:D[0]||(D[0]=s=>C())},null,8,["content","icon"])]),_:1}),i(h,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:x(()=>[t("h3",Z,e(n(o)("核销信息")),1),t("div",A,[t("span",J,e(n(o)("核销类型")),1),t("span",K,e(r.value.type_name),1)]),t("div",Q,[t("span",X,e(n(o)("核销状态")),1),Y]),t("div",I,[t("span",tt,e(n(o)("核销人员")),1),t("span",et,e(r.value.member?r.value.member.nickname:"--"),1)]),t("div",st,[t("span",at,e(n(o)("核销时间")),1),t("span",nt,e(r.value.create_time),1)]),(a(!0),l(d,null,m(f.value.fixed,(s,p)=>(a(),l("div",{class:"flex items-center mt-[15px]",key:p},[t("span",lt,e(s.title),1),t("span",ot,e(s.value),1)]))),128)),(a(!0),l(d,null,m(r.value.verify_info,(s,p)=>(a(),l("div",{key:p},[(a(!0),l(d,null,m(s,(_,g)=>(a(),l("div",{class:"flex items-center mt-[15px]",key:g},[t("span",it,e(_.name),1),t("span",rt,e(_.value),1)]))),128))]))),128))]),_:1}),(a(!0),l(d,null,m(f.value.diy,(s,p)=>(a(),j(h,{class:"box-card mt-[15px] !border-none",shadow:"never",key:p},{default:x(()=>[t("h3",ct,e(s.title),1),(a(!0),l(d,null,m(s.list,(_,g)=>(a(),l("div",{class:"flex items-center mt-[15px]",key:g},[t("span",xt,e(_.title),1),t("span",pt,e(_.value),1)]))),128))]),_:2},1024))),128)),i(h,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:x(()=>[t("h3",_t,e(n(o)("商品信息")),1),i(F,{data:k.value,size:"large"},{default:x(()=>[i(B,{label:n(o)("商品名称"),align:"left",width:"300"},{default:x(({row:s})=>[t("div",dt,[t("div",mt,[t("img",{class:"w-[50px] h-[50px] mr-[10px]",src:n(z)(s.cover)},null,8,ut)]),t("div",ht,[t("p",vt,e(s.name),1)])])]),_:1},8,["label"]),i(B,{prop:"num",label:n(o)("数量"),"min-width":"50",align:"right"},null,8,["label"])]),_:1},8,["data"])]),_:1})])),[[N,u.value]])}}});export{Lt as default};
