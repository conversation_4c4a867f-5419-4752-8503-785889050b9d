import{d as re,y as ie,n as I,r as w,f as de,l as ue,q as o,h as c,c as V,e as a,w as l,a as _,t as u,u as s,i as f,F as E,W as T,s as g,Z as j,C as D,cE as me,cF as pe,cG as ce,cH as _e,cI as fe,cJ as ye,af as q,cK as ve,cL as ge,E as be,a$ as ke,a4 as we,a1 as he,M as xe,N as Ve,a9 as Ce,ag as Ee,bk as Te,bl as De,bm as Le,ah as Ue,a2 as $e,L as Fe,au as Pe,av as Be,V as Re,a3 as ze}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                         *//* empty css                        *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 */import{_ as Me}from"./cron-info.vue_vue_type_script_setup_true_lang-e90a462e.js";const Ne={class:"main-container"},Oe={class:"flex justify-between items-center mb-[20px]"},Ie={class:"text-page-title"},je={class:"flex items-center"},qe={class:"mt-2"},Se={class:"flex justify-between"},Ge={class:"mt-[20px]"},We={class:"flex items-center"},Ae={class:"el-dropdown-link text-primary"},He={class:"mt-[16px] flex justify-end"},Je={class:"flex"},Ke={class:"input-width flex items-center text-sm"},Ze={class:"dialog-footer"},Ct=re({__name:"schedule",setup(Qe){const S=ie().meta.title,r=I({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{type:"",status:"all"}}),L=w([]),$=w([]),F=w([]),P=w(),G=i=>{i&&(i.resetFields(),v())};(async()=>{L.value=await(await me()).data,$.value=await(await pe()).data,F.value=await(await ce()).data})();const v=(i=1)=>{r.loading=!0,r.page=i,_e({page:r.page,limit:r.limit,...r.searchParam}).then(t=>{r.loading=!1,r.data=t.data.data,r.total=t.data.total}).catch(()=>{r.loading=!1})};v();const W=de(),h=w(!1),n=I({...{id:0,key:"",status:2,time:{type:"min",week:"",day:"",hour:"",min:""}}}),B=w(),A=()=>{n.id=0,n.key="",n.status=2,n.time.type="min",n.time.week="",n.time.day="",n.time.hour="",n.time.min="",h.value=!0},H=ue(()=>({key:[{required:!0,message:o("titlePlaceholder"),trigger:"blur"}],timeDate:[{required:!0,validator:J,trigger:"blur"}]})),J=(i,t,p)=>{const m=n.time,d=k=>/^[1-9]\d*$/.test(k),y=()=>p(new Error(o("cronTimeTips")));switch(m.type){case"min":return d(m.min)?p():y();case"hour":return d(m.hour)&&d(m.min)?p():y();case"day":return d(m.day)&&d(m.hour)&&d(m.min)?p():y();case"week":return m.week!==""&&d(m.hour)&&d(m.min)?p():y();case"month":return d(m.day)&&d(m.hour)&&d(m.min)?p():y();default:return y()}},C=w(!1),K=async i=>{C.value||!i||await i.validate(async t=>{if(t){C.value=!0;const p=n;(n.id>0?fe:ye)(p).then(d=>{C.value=!1,h.value=!1,v()}).catch(()=>{C.value=!1})}})},Z=i=>{n.id=i.id,n.key=i.key,n.status=i.status,n.time=i.time,h.value=!0},Q=i=>{q.confirm(o("cronDeleteTips"),o("warning"),{confirmButtonText:o("confirm"),cancelButtonText:o("cancel"),type:"warning"}).then(()=>{ve(i).then(()=>{v()}).catch(()=>{})})},X=i=>{q.confirm(o(`确认要立即执行一次"${i.name}"任务吗？`),o("warning"),{confirmButtonText:o("confirm"),cancelButtonText:o("cancel"),type:"warning"}).then(()=>{ge({id:i.id}).then(t=>{}).catch(()=>{})})},Y=w(null),ee=i=>{W.push({path:"/tools/schedule_log",query:{id:i}})};return(i,t)=>{const p=be,m=ke,d=we,y=he,k=xe,R=Ve,z=Ce,x=Ee,M=Te,te=De,ae=Le,le=Ue,oe=$e,U=Fe,N=Pe,ne=Be,se=Re,O=ze;return c(),V("div",Ne,[a(z,{class:"box-card !border-none",shadow:"never"},{default:l(()=>[_("div",Oe,[_("span",Ie,u(s(S)),1),a(p,{type:"primary",onClick:A},{default:l(()=>[f(u(s(o)("addCron")),1)]),_:1})]),a(m,{type:"info"},{default:l(()=>[_("div",je,[_("div",null,[_("p",null,u(s(o)("cronTipsOne")),1),_("p",qe,u(s(o)("cronTipsTwo")),1)])])]),_:1}),a(z,{class:"box-card !border-none mb-[10px] table-search-wrap",shadow:"never"},{default:l(()=>[_("div",Se,[a(R,{inline:!0,model:r.searchParam,ref_key:"searchFormRef",ref:P},{default:l(()=>[a(k,{label:s(o)("title"),prop:"key"},{default:l(()=>[a(y,{modelValue:r.searchParam.key,"onUpdate:modelValue":t[0]||(t[0]=e=>r.searchParam.key=e),placeholder:"全部",filterable:"",remote:"",clearable:"","remote-method":v},{default:l(()=>[a(d,{label:"全部",value:"all"}),(c(!0),V(E,null,T(L.value,e=>(c(),g(d,{key:e.key,label:e.name,value:e.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),a(k,{label:s(o)("status"),prop:"status"},{default:l(()=>[a(y,{modelValue:r.searchParam.status,"onUpdate:modelValue":t[1]||(t[1]=e=>r.searchParam.status=e),placeholder:"全部",filterable:"",remote:"",clearable:"","remote-method":v},{default:l(()=>[a(d,{label:"全部",value:"all"}),a(d,{label:"启用",value:"1"}),a(d,{label:"关闭",value:"0"})]),_:1},8,["modelValue"])]),_:1},8,["label"]),a(k,null,{default:l(()=>[a(p,{type:"primary",onClick:t[2]||(t[2]=e=>v())},{default:l(()=>[f(u(s(o)("search")),1)]),_:1}),a(p,{onClick:t[3]||(t[3]=e=>G(P.value))},{default:l(()=>[f(u(s(o)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])])]),_:1}),_("div",Ge,[j((c(),g(le,{data:r.data,size:"large"},{empty:l(()=>[_("span",null,u(r.loading?"":s(o)("emptyData")),1)]),default:l(()=>[a(x,{prop:"key",label:s(o)("key"),"min-width":"150"},null,8,["label"]),a(x,{prop:"name",label:s(o)("title"),"min-width":"150"},null,8,["label"]),a(x,{label:s(o)("crondType"),"min-width":"150"},{default:l(({row:e})=>[f(u(e.crontab_content),1)]),_:1},8,["label"]),a(x,{prop:"status_name",label:s(o)("openStatus"),"min-width":"100"},null,8,["label"]),a(x,{label:s(o)("operation"),align:"right",fixed:"right",width:"180"},{default:l(({row:e})=>[_("div",We,[a(p,{type:"primary",link:"",onClick:b=>Z(e)},{default:l(()=>[f(u(s(o)("edit")),1)]),_:2},1032,["onClick"]),a(p,{type:"primary",link:"",onClick:b=>Q(e.id)},{default:l(()=>[f(u(s(o)("delete")),1)]),_:2},1032,["onClick"]),a(ae,{class:"ml-[12px]"},{dropdown:l(()=>[a(te,null,{default:l(()=>[a(M,{onClick:b=>X(e)},{default:l(()=>[f(u(s(o)("doOne")),1)]),_:2},1032,["onClick"]),a(M,{onClick:b=>ee(e.id)},{default:l(()=>[f(u(s(o)("cronLog")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),default:l(()=>[_("span",Ae,u(s(o)("more")),1)]),_:2},1024)])]),_:1},8,["label"])]),_:1},8,["data"])),[[O,r.loading]]),_("div",He,[a(oe,{"current-page":r.page,"onUpdate:current-page":t[4]||(t[4]=e=>r.page=e),"page-size":r.limit,"onUpdate:page-size":t[5]||(t[5]=e=>r.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:r.total,onSizeChange:t[6]||(t[6]=e=>v()),onCurrentChange:v},null,8,["current-page","page-size","total"])])])]),_:1}),a(Me,{ref_key:"cronDialog",ref:Y,onComplete:v},null,512),a(se,{modelValue:h.value,"onUpdate:modelValue":t[15]||(t[15]=e=>h.value=e),title:s(o)("editCron"),width:"750px","destroy-on-close":!0},{footer:l(()=>[_("span",Ze,[a(p,{type:"primary",onClick:t[14]||(t[14]=e=>K(B.value))},{default:l(()=>[f(u(s(o)("confirm")),1)]),_:1})])]),default:l(()=>[j((c(),g(R,{model:n,"label-width":"110px",ref_key:"formRef",ref:B,rules:s(H),class:"page-form"},{default:l(()=>[a(k,{label:s(o)("cronTemplate"),class:"items-center",prop:"key"},{default:l(()=>[a(y,{modelValue:n.key,"onUpdate:modelValue":t[7]||(t[7]=e=>n.key=e)},{default:l(()=>[(c(!0),V(E,null,T(L.value,e=>(c(),g(d,{key:e.key,label:e.name,value:e.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),a(k,{label:s(o)("cronTime"),prop:"timeDate"},{default:l(()=>[a(y,{modelValue:n.time.type,"onUpdate:modelValue":t[8]||(t[8]=e=>n.time.type=e),class:"w-[150px]"},{default:l(()=>[(c(!0),V(E,null,T($.value,(e,b)=>(c(),g(d,{key:b,label:e,value:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_("div",Je,[n.time.type=="week"?(c(),g(y,{key:0,modelValue:n.time.week,"onUpdate:modelValue":t[9]||(t[9]=e=>n.time.week=e),class:"ml-2 w-[120px]"},{default:l(()=>[(c(!0),V(E,null,T(F.value,(e,b)=>(c(),g(d,{key:b,label:e,value:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):D("",!0),["month","day"].indexOf(n.time.type)!=-1?(c(),g(U,{key:1,modelValue:n.time.day,"onUpdate:modelValue":t[10]||(t[10]=e=>n.time.day=e),modelModifiers:{trim:!0},class:"ml-2 w-[120px]"},{append:l(()=>[f(u(s(o)("day")),1)]),_:1},8,["modelValue"])):D("",!0),["month","day","hour","week"].indexOf(n.time.type)!=-1?(c(),g(U,{key:2,modelValue:n.time.hour,"onUpdate:modelValue":t[11]||(t[11]=e=>n.time.hour=e),modelModifiers:{trim:!0},class:"ml-2 w-[120px]"},{append:l(()=>[f(u(s(o)("hour")),1)]),_:1},8,["modelValue"])):D("",!0),["month","day","hour","week","min"].indexOf(n.time.type)!=-1?(c(),g(U,{key:3,modelValue:n.time.min,"onUpdate:modelValue":t[12]||(t[12]=e=>n.time.min=e),modelModifiers:{trim:!0},class:"ml-2 w-[120px]"},{append:l(()=>[f(u(s(o)("min")),1)]),_:1},8,["modelValue"])):D("",!0)])]),_:1},8,["label"]),a(k,{label:s(o)("isopen")},{default:l(()=>[_("div",Ke,[a(ne,{modelValue:n.status,"onUpdate:modelValue":t[13]||(t[13]=e=>n.status=e)},{default:l(()=>[a(N,{label:1},{default:l(()=>[f(u(s(o)("yes")),1)]),_:1}),a(N,{label:2},{default:l(()=>[f(u(s(o)("no")),1)]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[O,i.loading]])]),_:1},8,["modelValue","title"])])}}});export{Ct as default};
