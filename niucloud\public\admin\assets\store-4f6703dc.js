import{d as zl,f as Pl,y as ql,r as c,H as dl,j as Kl,a7 as Ol,l as cl,R as Hl,n as rl,q as a,a5 as ge,h as d,c as _,a as n,e,w as l,t as u,u as i,C as h,D as Ql,F as D,W as J,s as g,i as f,Z as W,_ as ae,v as Zl,X as Jl,bZ as pl,b8 as Wl,al as _l,af as se,$ as Xl,ap as Yl,aq as Gl,a0 as et,L as lt,a4 as tt,a1 as at,E as st,ag as nt,K as ot,ao as it,aY as ut,ah as dt,aa as ct,b_ as rt,a8 as pt,M as _t,a9 as ft,N as vt,V as mt,aO as ht,aP as xt,aQ as gt,aR as yt,U as kt,b9 as bt,a3 as wt,p as Ct,g as It}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                     *//* empty css               *//* empty css                *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css               *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                    */import{_ as Tt}from"./icon-addon-339e16d0.js";import{_ as ye}from"./apply_empty-cdca3e85.js";import{T as Ut,_ as Vt,b as Et,a as At}from"./dark-63c0c649.js";import{c as Bt,d as St,p as Ft,e as $t,i as Dt,f as Rt,h as Lt,j as Mt,u as jt,k as Nt}from"./addon-6e674eac.js";import{d as zt}from"./tools-a40a4fbf.js";import{g as fl,d as Pt,s as qt}from"./module-62fd0e42.js";import Kt from"./index-03d52cd7.js";import Ot from"./index-2c5e161c.js";import Ht from"./index-780655d9.js";import{_ as Qt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                 */import"./upgrade-5c77adbf.js";/* empty css                         *//* empty css                         *//* empty css                 *//* empty css                   */const T=ne=>(Ct("data-v-e8673776"),ne=ne(),It(),ne),Zt={class:"main-container"},Jt={class:"flex justify-between items-center"},Wt={class:"text-page-title"},Xt={class:"custom-tabs-label"},Yt={key:0,class:"w-[15px] h-[15px] bg-[#DA203E] absolute text-[#fff] text-[11px] flex items-center justify-center rounded-full top-[3px] right-[-12px]"},Gt={class:"flex justify-between my-[10px]"},ea={class:"flex items-center search-form"},la={class:"relative"},ta=T(()=>n("span",{style:{opacity:"0"}},".",-1)),aa=[ta],sa={class:"flex items-center cursor-pointer relative left-[-10px]"},na=T(()=>n("div",{class:"flex items-center w-full h-full"},[n("img",{class:"max-w-full max-h-full",src:Tt,alt:""})],-1)),oa={class:"flex-1 w-0 flex flex-col justify-center pl-[20px] font-500 text-[13px]"},ia={class:"w-[236px] truncate leading-[18px]"},ua={key:0,class:"w-[236px] truncate leading-[18px] mt-[6px]"},da={key:1,class:"w-[236px] truncate leading-[18px] mt-[6px]"},ca={class:"mt-[3px] flex flex-nowrap"},ra={class:"flex items-center"},pa={class:"font-500 text-[13px] mr-[5px]"},_a={class:"font-500 text-[13px]"},fa={class:"font-500 text-[13px] multi-hidden"},va={class:"font-500 text-[13px] multi-hidden"},ma={class:"font-500 text-[13px]"},ha={key:1,class:"data-loading"},xa=T(()=>n("span",null,null,-1)),ga={key:0,class:"h-[100px]"},ya=T(()=>n("div",{class:"w-[230px] mx-auto"},[n("img",{src:ye,class:"max-w-full",alt:""})],-1)),ka={class:"flex items-center"},ba=T(()=>n("div",{class:"w-[230px] mx-auto"},[n("img",{src:ye,class:"max-w-full",alt:""})],-1)),wa={class:"flex items-center"},Ca={key:4,class:"mx-auto overview-empty flex flex-col items-center pt-14 pb-6"},Ia=T(()=>n("div",{class:"mb-[20px] text-sm text-[#888]"},"检测到当前账号尚未绑定授权，请先绑定授权！",-1)),Ta={class:"flex flex-1 flex-wrap justify-center relative"},Ua={class:"px-[18px] py-[8px]"},Va=T(()=>n("p",{class:"leading-[32px] text-[14px]"},"您在官方应用市场购买任意一款应用，即可获得授权码。输入正确授权码认证通过后，即可支持在线升级和其它相关服务",-1)),Ea={class:"flex justify-end mt-[36px]"},Aa=T(()=>n("div",{class:"w-[230px] mx-auto"},[n("img",{src:ye,class:"max-w-full",alt:""})],-1)),Ba={class:"flex items-center"},Sa=T(()=>n("div",{class:"w-[230px] mx-auto"},[n("img",{src:ye,class:"max-w-full",alt:""})],-1)),Fa={class:"flex items-center"},$a={class:"mt-[20px]"},Da={class:"text-sm mt-[10px] text-info"},Ra={class:"mt-[20px]"},La={class:"mt-[10px] text-right"},Ma={class:"input-width"},ja={class:"input-width"},Na={class:"input-width"},za={class:"input-width"},Pa={class:"dialog-footer"},qa={class:"min-h-[150px]"},Ka={key:0,class:"my-3"},Oa={class:"pt-[20px] pl-[20px]"},Ha={class:"flex items-center"},Qa=T(()=>n("span",{class:"ml-[5px] leading-[20px]"},"编译权限错误，查看解决方案",-1)),Za=T(()=>n("div",{class:"border-[1px] border-primary rounded-[3px] w-[72px] h-[26px] leading-[25px] text-center"},"立即查看",-1)),Ja={class:"px-[20px] pt-[10px] text-[14px]"},Wa={key:0},Xa={key:1},Ya={key:0,class:"text-right"},Ga={key:1},es={class:"flex justify-end"},ls={class:"w-[400px]"},ts={class:"w-[400px]"},as={class:"h-[50vh] mt-[20px]"},ss={class:"h-[50vh] mt-[20px] flex flex-col"},ns=T(()=>n("img",{src:Et,alt:""},null,-1)),os={class:"text-[16px] text-[#4F516D] mt-[5px]"},is={key:0,class:"text-[16px] text-[#9699B6] mt-[10px]"},us={class:"mt-[20px]"},ds={class:"mt-[50px]"},cs=T(()=>n("img",{src:At,alt:""},null,-1)),rs={class:"min-h-[150px]"},ps={key:0,class:"bg-[#fff] my-3"},_s={class:"pt-[20px] pl-[20px]"},fs={class:"px-[20px] pt-[10px] text-[14px]"},vs={key:0},ms={key:1},hs={key:0},xs={key:1},gs=T(()=>n("span",null,"本地已经存在该插件/应用，再次下载会覆盖该插件/应用。",-1)),ys={class:"dialog-footer"},ks=zl({__name:"store",setup(ne){Pl();const Fe=ql(),b=c(dl.get("storeActiveName")||"installed"),ke=c(null),oe=c(null),C=c(!0),j=c(""),be=c([]),ie=Kl(),X=c(!1),R=c(null),ue=c(""),$e=c(null);Ol().then(o=>{ue.value=o.data.version.version});const De=c({});(()=>{Bt().then(o=>{De.value=o.data.type_list})})();const Re=c(),vl=()=>{Me(Re.value,!0)},Le=o=>{b.value=o,dl.set({key:"storeActiveName",data:o})};Fe.query.id&&Le(Fe.query.id);const Me=(o,t=!1)=>{if(o.is_download&&b.value=="all"&&!t)return X.value=!0,Re.value=o,!1;j.value||(j.value=o.key,Pt({addon:o.key,version:o.version}).then(()=>{X.value=!1,ze(o.key),L(),j.value=""}).catch(()=>{j.value=""}))},we=c("");fl().then(o=>{o.data.data&&o.data.data.auth_code&&(we.value=o.data.data.auth_code)});const Ce=c(""),Ie=c(""),E=c({installed:[],uninstalled:[],all:[],recentlyUpdated:[]}),B=o=>{const t=new Map,p=[];return o.forEach(r=>{t.set(r.key,{...r,children:[]})}),o.forEach(r=>{r.support_app&&t.has(r.support_app)&&t.get(r.support_app).children.push(t.get(r.key))}),t.forEach(r=>{(!r.support_app||!t.has(r.support_app))&&p.push(r)}),p},de=()=>{const o=Ce.value,t=Ie.value;if((!o||o==="")&&(t===""||t==null)){E.value.installed=B(v.value.installed),E.value.uninstalled=B(v.value.uninstalled),E.value.all=B(v.value.all),E.value.recentlyUpdated=B(v.value.recentlyUpdated);return}const p=r=>r.filter(M=>{const w=!o||M.title.includes(o),ee=!t||M.type===t;return w&&ee});E.value.installed=B(p(v.value.installed)),E.value.uninstalled=B(p(v.value.uninstalled)),E.value.all=B(p(v.value.all)),E.value.recentlyUpdated=B(p(v.value.recentlyUpdated))},v=c({installed:[],uninstalled:[],all:[],recentlyUpdated:[],error:""}),L=()=>{C.value=!0,St({}).then(o=>{var p;const t=o.data.list;v.value.error=o.data.error,v.value.installed=[],v.value.uninstalled=[],v.value.all=[],v.value.recentlyUpdated=[];for(const r in t)t[r].is_local==!1&&v.value.all.push(t[r]),t[r].install_info&&((p=Object.keys(t[r].install_info))!=null&&p.length)?(v.value.installed.push(t[r]),t[r].install_info.version!=t[r].version&&v.value.recentlyUpdated.push(t[r])):t[r].is_download==!0&&v.value.uninstalled.push(t[r]);de(),ie.routers.forEach((r,M)=>{r.children&&r.children.length?(r.name=pl(r.children),je.value[r.meta.app]=pl(r.children)):je.value[r.meta.app]=r.name}),C.value=!1}).catch(()=>{C.value=!1})};L();const je=c({}),S=c(""),F=c(!1),U=c(0),V=c({});let Te=null;const Ne=new Ut,ml=(o,t,p,r,M)=>{if(t=="开始安装插件"){p(Ne);const w=hl(["/","——","\\","|"]);Te=setInterval(()=>{Ne.flush("> "+w.next().value)},150)}};function hl(o){let t=0;return{next(){return t+1==o.length&&(t=0),{value:o[t++]}}}}const ze=o=>{S.value=o,Ft(o).then(t=>{U.value=0,ce.value=!1,$.value=!1,P.value="",F.value=!0,be.value=[],V.value=t.data,ie.clearRouters()})},Pe=c(null),N=c(0);let y=null,Y=null;const Ue=(o=!0)=>{$t().then(t=>{if(t.data){if(Pe.value=Date.now(),N.value=0,y&&clearInterval(y),y=setInterval(()=>{N.value++},1e3),o&&(Ve=[],S.value=t.data.addon,F.value||(Y=Wl.success({title:a("warning"),dangerouslyUseHTMLString:!0,message:_l("div",{},[a("installingTips"),_l("span",{class:"text-primary cursor-pointer",onClick:gl},[a("installPercent")])]),duration:0,showClose:!1}))),t.data.error){R.value.pushMessage({content:t.data.error,class:"error"}),Oe.value=t.data.error,$.value=!0,y&&(clearInterval(y),y=null);return}t.data.mode=="cloud"&&bl(),setTimeout(()=>{Ue(!1)},2e3)}else o||(U.value=2,y&&(clearInterval(y),y=null),L(),ie.clearRouters(),Y.close())}).catch(t=>{R.value.pushMessage({content:t.message,class:"error"})})};Ue();const ce=c(!1),qe=()=>{ce.value=!0,U.value=1,$.value=!1},xl=cl(()=>{const o=N.value,t=Math.floor(o/3600),p=Math.floor(o%3600/60),r=o%60;return[t>0?`${t}小时`:"",p>0?`${p}分钟`:"",`${r}秒`].filter(Boolean).join("")}),gl=()=>{F.value=!0,U.value=1},z=c(!1),P=c(""),yl=()=>{!V.value.is_pass||z.value||(P.value="local",z.value=!0,Pe.value=Date.now(),N.value=0,y&&clearInterval(y),y=setInterval(()=>{N.value++},1e3),Dt({addon:S.value}).then(o=>{U.value=2,y&&(clearInterval(y),y=null),L(),z.value=!1,o.data.length&&(be.value=o.data)}).catch(o=>{z.value=!1}))},q=c(!1),kl=()=>{if(!we.value){Ke();return}!V.value.is_pass||q.value||(q.value=!0,P.value="cloud",Rt({addon:S.value}).then(o=>{U.value=1,R.value.execute("clear"),R.value.execute("开始安装插件"),Ue(),q.value=!1}).catch(o=>{q.value=!1}))},Ke=()=>{se.confirm(a("authTips"),a("warning"),{distinguishCancelAndClose:!0,confirmButtonText:a("toBind"),cancelButtonText:a("toNiucloud")}).then(()=>{We()}).catch(o=>{o==="cancel"&&window.open("https://www.niucloud.com/app")})},$=c(!1),Oe=c("");let Ve=[];const bl=()=>{Lt(S.value).then(o=>{const t=o.data.data??[];t[0]&&t[0].length&&F.value==!0&&t[0].forEach(p=>{Ve.includes(p.action)||(R.value.pushMessage({content:`${p.action}`}),Ve.push(p.action),p.code==0&&R.value.pushMessage({content:p.msg,class:"error"}))})}).catch(()=>{Y==null||Y.close()})};Hl(S,o=>{V.value={}});const Ee=c(!1),re=c({}),wl=o=>{se.confirm(a("uninstallTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{Il(o)})},He=o=>{var t;(t=ke.value)==null||t.open(o)},Cl=()=>{var o;if(!we.value){Ke();return}if(oe.value.cloudBuildTask){(o=oe.value)==null||o.open();return}se.confirm(a("cloudBuildTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{var t;(t=oe.value)==null||t.open()})},Il=o=>{Mt(o).then(({data:t})=>{t.is_pass?jt({addon:o}).then(p=>{L(),ie.clearRouters(),C.value=!1}).catch(()=>{C.value=!1}):(re.value=t,Ee.value=!0)})},Qe=()=>{window.open("https://www.niucloud.com/app")},Tl=o=>{U.value==1&&!ce.value&&!$.value?se.confirm(a("installShowDialogCloseTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{Nt(S.value),y&&(clearInterval(y),y=null),ce.value=!1,P.value="",$.value=!1,o()}):U.value==2?(Le("installed"),location.reload()):o(),Te&&clearInterval(Te)},pe=c(!1),K=c({}),Ul=o=>{pe.value=!0,K.value=o},Ze=c(""),Vl=o=>{var t;Ze.value=o.key,(t=$e.value)==null||t.open()},_e=c(!1),fe=c(""),Je=c(null),G=c(!1),A=c(!0);(()=>{A.value=!0,fl().then(o=>{A.value=!1,o.data.data&&o.data.data.length!=0&&(fe.value=o.data.data)}).catch(()=>{A.value=!1,_e.value=!1})})();const We=()=>{_e.value=!0},O=rl({auth_code:"",auth_secret:""}),Ae=c(),El=rl({auth_code:[{required:!0,message:a("authCodePlaceholder"),trigger:"blur"}],auth_secret:[{required:!0,message:a("authSecretPlaceholder"),trigger:"blur"}]}),Al=async o=>{G.value||!o||await o.validate(async t=>{t&&(G.value=!0,qt(O).then(()=>{G.value=!1,setTimeout(()=>{location.reload()},1e3)}).catch(()=>{G.value=!1}))})},Xe=()=>{window.open("https://www.niucloud.com/app")},Bl=()=>{window.open("https://doc.niucloud.com/v6.html?keywords=/chang-jian-wen-ti-chu-li/er-shi-wu-3001-sheng-7ea7-yun-bian-yi-mu-lu-du-xie-quan-xian-zhuang-tai-bu-tong-guo-ru-he-chu-li")},Sl=o=>{se.confirm(a("deleteAddonTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{zt(o).then(()=>{L()})})},Fl=o=>{if(!o.support_version)return!1;const t=o.support_version.split("."),p=ue.value.split(".");return parseFloat(`${t[0]}.${t[1]}`)<parseFloat(`${p[0]}.${p[1]}`)};let Be=[];const $l=o=>{Be=o.map(t=>t.key)},Dl=()=>{if(!Be.length){Xl({message:"请先勾选要升级的插件",type:"error",duration:5e3});return}He(Be.toString())},Rl=cl(()=>new Set((E.value[b.value]||[]).map(o=>o.key)));return(o,t)=>{const p=Yl,r=Gl,M=ge("search"),w=et,ee=lt,Ye=tt,Ll=at,m=st,I=nt,Ml=ot,Ge=it,ve=ut,el=ge("QuestionFilled"),ll=dt,me=ct,tl=rt,jl=pt,H=_t,al=ft,sl=vt,le=mt,Se=ht,Nl=xt,k=gt,Q=yt,he=ge("Select"),xe=ge("CloseBold"),nl=kt,ol=bt,il=wt;return d(),_(D,null,[n("div",Zt,[e(al,{class:"box-card !border-none",shadow:"never"},{default:l(()=>{var ul;return[n("div",Jt,[n("span",Wt,u(i(a)("localAppText")),1)]),e(r,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=s=>b.value=s),class:"mt-[10px]"},{default:l(()=>[e(p,{label:i(a)("installLabel"),name:"installed"},null,8,["label"]),e(p,{label:i(a)("uninstalledLabel"),name:"uninstalled"},null,8,["label"]),e(p,{label:i(a)("buyLabel"),name:"all"},null,8,["label"]),e(p,{label:i(a)("recentlyUpdated"),name:"recentlyUpdated"},{label:l(()=>[n("span",Xt,[n("span",null,u(i(a)("recentlyUpdated")),1),v.value.recentlyUpdated.length>0?(d(),_("span",Yt,u(v.value.recentlyUpdated.length),1)):h("",!0)])]),_:1},8,["label"])]),_:1},8,["modelValue"]),n("div",Gt,[n("div",ea,[e(ee,{class:"!w-[192px] !h-[32px] rounded-[4px]",placeholder:i(a)("search"),modelValue:Ce.value,"onUpdate:modelValue":t[1]||(t[1]=s=>Ce.value=s),modelModifiers:{trim:!0},onKeyup:Ql(de,["enter"])},{suffix:l(()=>[e(w,{class:"el-input__icon cursor-pointer",size:"14px",onClick:de},{default:l(()=>[e(M)]),_:1})]),_:1},8,["placeholder","modelValue","onKeyup"]),e(Ll,{modelValue:Ie.value,"onUpdate:modelValue":t[2]||(t[2]=s=>Ie.value=s),placeholder:"请选择类型",class:"!w-[192px] !h-[32px] rounded-[4px] ml-[20px]"},{default:l(()=>[e(Ye,{label:i(a)("全部"),value:""},null,8,["label"]),(d(!0),_(D,null,J(De.value,(s,x)=>(d(),g(Ye,{key:x,label:s,value:x},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e(m,{type:"primary",onClick:de,class:"ml-[20px]"},{default:l(()=>[f(u(i(a)("搜索")),1)]),_:1})]),n("div",null,[W(e(m,{type:"primary",onClick:Dl,loading:(ul=ke.value)==null?void 0:ul.loading,disabled:A.value},{default:l(()=>[f(u(i(a)("batchUpgrade")),1)]),_:1},8,["loading","disabled"]),[[ae,b.value==="recentlyUpdated"]])])]),n("div",la,[v.value[b.value].length&&!C.value?(d(),g(ll,{key:0,"tree-props":{children:"children"},"default-expand-all":!0,data:E.value[b.value],"row-key":"key",size:"large",onSelectionChange:$l},{default:l(()=>[e(I,{width:"24"},{default:l(({row:s})=>{var x;return[n("div",{class:Zl(["tree-child-cell",{"is-tree-parent":(x=s.children)==null?void 0:x.length,"is-tree-child":typeof s.support_app=="string"&&s.support_app!==""&&i(Rl).has(s.support_app)}])},aa,2)]}),_:1}),b.value==="recentlyUpdated"?(d(),g(I,{key:0,type:"selection"})):h("",!0),e(I,{label:i(a)("appName"),align:"left",width:"500"},{default:l(({row:s})=>{var x,te;return[n("div",sa,[e(Ml,{class:"w-[54px] h-[54px]",src:s.icon,fit:"contain"},{error:l(()=>[na]),_:2},1032,["src"]),n("div",oa,[n("div",ia,u(s.title),1),s.install_info&&((x=Object.keys(s.install_info))!=null&&x.length)?(d(),_("div",ua,u(s.install_info.version),1)):(d(),_("div",da,u(s.version),1)),n("div",ca,[b.value=="recentlyUpdated"&&s.install_info&&((te=Object.keys(s.install_info))!=null&&te.length)&&s.install_info.version!=s.version?(d(),g(Ge,{key:0,type:"danger",size:"small"},{default:l(()=>[f(u(i(a)("newVersion"))+u(s.version),1)]),_:2},1024)):h("",!0),Fl(s)?(d(),g(ve,{key:1,effect:"dark",content:`该插件适配框架版本为${s.support_version}，与已安装框架版本${ue.value}不完全兼容`,placement:"top-start"},{default:l(()=>[e(Ge,{type:"warning",size:"small",class:"ml-[3px]"},{default:l(()=>[f("该插件适配框架版本为"+u(s.support_version)+"，与已安装框架版本"+u(ue.value)+"不完全兼容",1)]),_:2},1024)]),_:2},1032,["content"])):h("",!0)])])])]}),_:1},8,["label"]),e(I,{align:"left","min-width":"150"},{header:l(()=>[n("div",ra,[n("span",pa,u(i(a)("appIdentification")),1),e(ve,{class:"box-item",effect:"light",content:i(a)("tipText"),placement:"bottom"},{default:l(()=>[e(w,{class:"cursor-pointer text-[16px] text-[#a9a9a9]"},{default:l(()=>[e(el)]),_:1})]),_:1},8,["content"])])]),default:l(({row:s})=>[n("span",_a,u(s.key),1)]),_:1}),e(I,{label:i(a)("introduction"),align:"left","min-width":"250"},{default:l(({row:s})=>[n("span",fa,u(s.desc),1)]),_:1},8,["label"]),e(I,{label:i(a)("type"),align:"left","min-width":"80"},{default:l(({row:s})=>[n("span",va,u(s.type==="app"?i(a)("app"):i(a)("addon")),1)]),_:1},8,["label"]),e(I,{label:i(a)("author"),align:"left","min-width":"80"},{default:l(({row:s})=>[n("span",ma,u(s.author),1)]),_:1},8,["label"]),e(I,{label:i(a)("operation"),fixed:"right",align:"right",width:"250"},{default:l(({row:s})=>{var x,te;return[b.value=="recentlyUpdated"&&s.install_info&&((x=Object.keys(s.install_info))!=null&&x.length)&&s.install_info.version!=s.version?(d(),g(m,{key:0,class:"!text-[13px]",type:"primary",link:"",onClick:Z=>He(s.key)},{default:l(()=>[f(u(i(a)("upgrade")),1)]),_:2},1032,["onClick"])):h("",!0),s.install_info&&((te=Object.keys(s.install_info))!=null&&te.length)?(d(),g(m,{key:1,class:"!text-[13px]",type:"primary",link:"",onClick:Z=>wl(s.key)},{default:l(()=>[f(u(i(a)("unload")),1)]),_:2},1032,["onClick"])):h("",!0),s.is_download&&(!s.install_info||!Object.keys(s.install_info).length)?(d(),_(D,{key:2},[e(m,{class:"!text-[13px]",type:"primary",link:"",onClick:Z=>ze(s.key)},{default:l(()=>[f(u(i(a)("install")),1)]),_:2},1032,["onClick"]),e(m,{class:"!text-[13px]",type:"primary",link:"",onClick:Z=>Sl(s.key)},{default:l(()=>[f(u(i(a)("delete")),1)]),_:2},1032,["onClick"])],64)):h("",!0),s.is_download?h("",!0):(d(),g(m,{key:3,class:"!text-[13px]",loading:j.value==s.key,disabled:j.value!="",type:"primary",link:"",onClick:Jl(Z=>Me(s),["stop"])},{default:l(()=>[n("span",null,u(i(a)("down")),1)]),_:2},1032,["loading","disabled","onClick"])),e(m,{class:"!text-[13px]",type:"primary",link:"",onClick:Z=>Ul(s)},{default:l(()=>[f(u(i(a)("detail")),1)]),_:2},1032,["onClick"]),e(m,{class:"!text-[13px]",type:"primary",link:"",onClick:Z=>Vl(s)},{default:l(()=>[f("更新信息")]),_:2},1032,["onClick"])]}),_:1},8,["label"])]),_:1},8,["data"])):h("",!0),C.value||!v.value[b.value].length?(d(),_("div",ha,[e(ll,{data:[],size:"large",class:"pt-[5px]"},{empty:l(()=>[xa]),default:l(()=>[e(I,{label:i(a)("appName"),align:"left",width:"320"},null,8,["label"]),e(I,{align:"left","min-width":"120"}),e(I,{label:i(a)("introduction"),align:"left","min-width":"200"},null,8,["label"]),e(I,{label:i(a)("type"),align:"left","min-width":"100"},null,8,["label"]),e(I,{label:i(a)("author"),align:"left","min-width":"100"},null,8,["label"]),e(I,{label:i(a)("operation"),fixed:"right",align:"right",width:"150"},null,8,["label"])]),_:1}),C.value?W((d(),_("div",ga,null,512)),[[il,C.value]]):h("",!0)])):h("",!0),!v.value.installed.length&&!C.value&&b.value=="installed"&&!A.value?(d(),g(me,{key:2,class:"mx-auto overview-empty"},{image:l(()=>[ya]),description:l(()=>[n("p",ka,u(i(a)("installed-empty")),1)]),_:1})):h("",!0),!v.value.uninstalled.length&&!C.value&&b.value=="uninstalled"&&!A.value?(d(),g(me,{key:3,class:"mx-auto overview-empty"},{image:l(()=>[ba]),description:l(()=>[n("p",wa,[n("span",null,u(i(a)("descriptionLeft")),1),e(tl,{type:"primary",onClick:Xe,class:"mx-[5px]"},{default:l(()=>[f(u(i(a)("link")),1)]),_:1}),n("span",null,u(i(a)("descriptionRight")),1)])]),_:1})):h("",!0),!v.value.all.length&&!C.value&&!fe.value&&b.value=="all"&&!A.value?(d(),_("div",Ca,[Ia,n("div",Ta,[e(m,{class:"w-[154px] !h-[48px] mt-[8px]",type:"primary",onClick:We},{default:l(()=>[f("授权码认证")]),_:1}),e(jl,{ref_key:"getAuthCodeDialog",ref:Je,placement:"bottom",width:478,trigger:"click",class:"mt-[8px]"},{reference:l(()=>[e(m,{class:"w-[154px] !h-[48px] mt-[8px] !text-[var(--el-color-primary)] hover:!text-[var(--el-color-primary)] !bg-transparent",plain:"",type:"primary"},{default:l(()=>[f("如何获取授权码?")]),_:1})]),default:l(()=>[n("div",Ua,[Va,n("div",Ea,[e(m,{class:"w-[182px] !h-[48px]",plain:"",onClick:Qe},{default:l(()=>[f("去应用市场逛逛")]),_:1}),e(m,{class:"w-[100px] !h-[48px]",plain:"",onClick:t[3]||(t[3]=s=>Je.value.hide())},{default:l(()=>[f("关闭")]),_:1})])])]),_:1},512)])])):h("",!0),!v.value.all.length&&!C.value&&fe.value&&b.value=="all"&&!A.value?(d(),g(me,{key:5,class:"mx-auto overview-empty"},{image:l(()=>[Aa]),description:l(()=>[n("p",Ba,[n("span",null,u(i(a)("buyDescriptionLeft")),1),e(tl,{type:"primary",onClick:Xe,class:"mx-[5px]"},{default:l(()=>[f(u(i(a)("link")),1)]),_:1}),n("span",null,u(i(a)("descriptionRight")),1)])]),_:1})):h("",!0),!v.value.recentlyUpdated.length&&!C.value&&fe.value&&b.value=="recentlyUpdated"&&!A.value?(d(),g(me,{key:6,class:"mx-auto overview-empty"},{image:l(()=>[Sa]),description:l(()=>[n("p",Fa,u(i(a)("recentlyUpdatedEmpty")),1)]),_:1})):h("",!0)]),e(le,{modelValue:_e.value,"onUpdate:modelValue":t[7]||(t[7]=s=>_e.value=s),title:"授权码认证",width:"400px"},{default:l(()=>[e(sl,{model:O,"label-width":"0",ref_key:"formRef",ref:Ae,rules:El,class:"page-form"},{default:l(()=>[e(al,{class:"box-card !border-none",shadow:"never"},{default:l(()=>[e(H,{prop:"auth_code"},{default:l(()=>[e(ee,{modelValue:O.auth_code,"onUpdate:modelValue":t[4]||(t[4]=s=>O.auth_code=s),modelModifiers:{trim:!0},placeholder:i(a)("authCodePlaceholder"),class:"input-width",clearable:"",size:"large"},null,8,["modelValue","placeholder"])]),_:1}),n("div",$a,[e(H,{prop:"auth_secret"},{default:l(()=>[e(ee,{modelValue:O.auth_secret,"onUpdate:modelValue":t[5]||(t[5]=s=>O.auth_secret=s),modelModifiers:{trim:!0},clearable:"",placeholder:i(a)("authSecretPlaceholder"),class:"input-width",size:"large"},null,8,["modelValue","placeholder"])]),_:1})]),n("div",Da,u(i(a)("authInfoTips")),1),n("div",Ra,[e(m,{type:"primary",class:"w-full",size:"large",loading:G.value,onClick:t[6]||(t[6]=s=>Al(Ae.value))},{default:l(()=>[f(u(i(a)("confirm")),1)]),_:1},8,["loading"])]),n("div",La,[e(m,{type:"primary",link:"",onClick:Qe},{default:l(()=>[f(u(i(a)("notHaveAuth")),1)]),_:1})])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(le,{modelValue:pe.value,"onUpdate:modelValue":t[9]||(t[9]=s=>pe.value=s),title:i(a)("plugDetail"),width:"500px","destroy-on-close":!0},{footer:l(()=>[n("span",Pa,[e(m,{type:"primary",onClick:t[8]||(t[8]=s=>pe.value=!1)},{default:l(()=>[f(u(i(a)("confirm")),1)]),_:1})])]),default:l(()=>[e(sl,{model:K.value,"label-width":"120px",ref_key:"formRef",ref:Ae,class:"page-form"},{default:l(()=>[e(H,{label:i(a)("title")},{default:l(()=>[n("div",Ma,u(K.value.title),1)]),_:1},8,["label"]),e(H,{label:i(a)("desc")},{default:l(()=>[n("div",ja,u(K.value.desc),1)]),_:1},8,["label"]),e(H,{label:i(a)("author")},{default:l(()=>[n("div",Na,u(K.value.author),1)]),_:1},8,["label"]),e(H,{label:i(a)("version")},{default:l(()=>[n("div",za,u(K.value.version),1)]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(le,{modelValue:F.value,"onUpdate:modelValue":t[14]||(t[14]=s=>F.value=s),title:i(a)("addonInstall"),width:"850px","close-on-click-modal":!1,"close-on-press-escape":!1,"before-close":Tl},{default:l(()=>[U.value!=2&&!$.value?(d(),g(Nl,{key:0,space:200,active:U.value,class:"number-of-steps","process-status":"process","align-center":""},{default:l(()=>[e(Se,{title:i(a)("envCheck"),class:"flex-1"},null,8,["title"]),e(Se,{title:i(a)("installProgress"),class:"flex-1"},null,8,["title"]),e(Se,{title:i(a)("installComplete"),class:"flex-1"},null,8,["title"])]),_:1},8,["active"])):h("",!0),W((d(),_("div",null,[n("div",qa,[V.value.dir?(d(),_("div",Ka,[n("p",Oa,u(i(a)("dirPermission")),1),V.value.is_pass?h("",!0):(d(),_("div",{key:0,class:"mt-[10px] mx-[20px] text-[14px] cursor-pointer text-primary flex items-center justify-between bg-[#EFF6FF] rounded-[4px] p-[10px]",onClick:Bl},[n("div",Ha,[e(w,{size:17},{default:l(()=>[e(el)]),_:1}),Qa]),Za])),n("div",Ja,[e(Q,{class:"py-[10px] items table-head-bg pl-[15px] mb-[10px]"},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(i(a)("path")),1)]),_:1}),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("demand")),1)]),_:1}),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("status")),1)]),_:1})]),_:1}),e(nl,{style:{height:"calc(300px)",overflow:"auto"}},{default:l(()=>[(d(!0),_(D,null,J(V.value.dir.is_readable,(s,x)=>(d(),g(Q,{class:"pb-[10px] items pl-[15px]",key:x},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(s.dir),1)]),_:2},1024),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("readable")),1)]),_:1}),e(k,{span:3},{default:l(()=>[s.status?(d(),_("span",Wa,[e(w,{color:"green"},{default:l(()=>[e(he)]),_:1})])):(d(),_("span",Xa,[e(w,{color:"red"},{default:l(()=>[e(xe)]),_:1})]))]),_:2},1024)]),_:2},1024))),128)),(d(!0),_(D,null,J(V.value.dir.is_write,(s,x)=>(d(),g(Q,{class:"pb-[10px] items pl-[15px]",key:x},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(s.dir),1)]),_:2},1024),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("write")),1)]),_:1}),e(k,{span:3},{default:l(()=>[s.status?(d(),_("span",Ya,[e(w,{color:"green"},{default:l(()=>[e(he)]),_:1})])):(d(),_("span",Ga,[e(w,{color:"red"},{default:l(()=>[e(xe)]),_:1})]))]),_:2},1024)]),_:2},1024))),128))]),_:1})])])):h("",!0)]),n("div",es,[e(ve,{effect:"dark",placement:"top"},{content:l(()=>[n("div",ls,u(i(a)("installTips")),1)]),default:l(()=>[e(m,{disabled:!V.value.is_pass||q.value,loading:z.value,onClick:yl},{default:l(()=>[f(u(i(a)("localInstall")),1)]),_:1},8,["disabled","loading"])]),_:1}),e(ve,{effect:"dark",placement:"top"},{content:l(()=>[n("div",ts,u(i(a)("cloudInstallTips")),1)]),default:l(()=>[e(m,{type:"primary",disabled:!V.value.is_pass||z.value,loading:q.value,onClick:kl},{default:l(()=>[f(u(i(a)("cloudInstall")),1)]),_:1},8,["disabled","loading"])]),_:1})])])),[[ae,U.value==0],[il,!V.value.dir]]),W(n("div",as,[e(i(Vt),{ref_key:"terminalRef",ref:R,context:S.value,"init-log":null,"show-header":!1,"show-log-time":!0,onExecCmd:ml},null,8,["context"])],512),[[ae,U.value==1&&!$.value]]),W(n("div",ss,[e(ol,{icon:"success",title:i(a)("addonInstallSuccess")},{icon:l(()=>[ns]),extra:l(()=>[(d(!0),_(D,null,J(be.value,(s,x)=>(d(),_("div",{class:"mb-[10px]",key:x},[n("div",os,u(s),1)]))),128)),N.value>0?(d(),_("div",is,"本次安装用时"+u(i(xl)),1)):h("",!0),n("div",us,[P.value=="cloud"?(d(),g(m,{key:0,onClick:t[10]||(t[10]=s=>qe()),class:"!w-[90px]"},{default:l(()=>[f("返回")]),_:1})):h("",!0),e(m,{onClick:t[11]||(t[11]=s=>F.value=!1),type:"primary",class:"!w-[90px]"},{default:l(()=>[f("完成")]),_:1})])]),_:1},8,["title"])],512),[[ae,U.value==2]]),W(n("div",ds,[e(ol,{icon:"error",title:i(a)("安装失败"),"sub-title":Oe.value},{icon:l(()=>[cs]),extra:l(()=>[P.value=="cloud"?(d(),g(m,{key:0,onClick:t[12]||(t[12]=s=>qe()),class:"!w-[90px]"},{default:l(()=>[f("错误信息")]),_:1})):h("",!0),e(m,{onClick:t[13]||(t[13]=s=>F.value=!1),type:"primary",class:"!w-[90px]"},{default:l(()=>[f("完成")]),_:1})]),_:1},8,["title","sub-title"])],512),[[ae,$.value]])]),_:1},8,["modelValue","title"]),e(le,{modelValue:Ee.value,"onUpdate:modelValue":t[15]||(t[15]=s=>Ee.value=s),title:i(a)("addonUninstall"),width:"850px","close-on-click-modal":!1,"close-on-press-escape":!1},{default:l(()=>[e(nl,{"max-height":"50vh"},{default:l(()=>[n("div",rs,[re.value.dir?(d(),_("div",ps,[n("p",_s,u(i(a)("dirPermission")),1),n("div",fs,[e(Q,{class:"py-[10px] items table-head-bg pl-[15px] mb-[10px]"},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(i(a)("path")),1)]),_:1}),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("demand")),1)]),_:1}),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("status")),1)]),_:1})]),_:1}),(d(!0),_(D,null,J(re.value.dir.is_readable,(s,x)=>(d(),g(Q,{class:"pb-[10px] items pl-[15px]",key:x},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(s.dir),1)]),_:2},1024),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("readable")),1)]),_:1}),e(k,{span:3},{default:l(()=>[s.status?(d(),_("span",vs,[e(w,{color:"green"},{default:l(()=>[e(he)]),_:1})])):(d(),_("span",ms,[e(w,{color:"red"},{default:l(()=>[e(xe)]),_:1})]))]),_:2},1024)]),_:2},1024))),128)),(d(!0),_(D,null,J(re.value.dir.is_write,(s,x)=>(d(),g(Q,{class:"pb-[10px] items pl-[15px]",key:x},{default:l(()=>[e(k,{span:18},{default:l(()=>[n("span",null,u(s.dir),1)]),_:2},1024),e(k,{span:3},{default:l(()=>[n("span",null,u(i(a)("write")),1)]),_:1}),e(k,{span:3},{default:l(()=>[s.status?(d(),_("span",hs,[e(w,{color:"green"},{default:l(()=>[e(he)]),_:1})])):(d(),_("span",xs,[e(w,{color:"red"},{default:l(()=>[e(xe)]),_:1})]))]),_:2},1024)]),_:2},1024))),128))])])):h("",!0)])]),_:1})]),_:1},8,["modelValue","title"]),e(le,{modelValue:X.value,"onUpdate:modelValue":t[17]||(t[17]=s=>X.value=s),title:"下载提示",width:"30%"},{footer:l(()=>[n("span",ys,[e(m,{onClick:t[16]||(t[16]=s=>X.value=!1)},{default:l(()=>[f("取消")]),_:1}),e(m,{type:"primary",onClick:vl},{default:l(()=>[f("确定")]),_:1})])]),default:l(()=>[gs]),_:1},8,["modelValue"])]}),_:1})]),e(Ht,{upgradeKey:Ze.value,ref_key:"upgradeLogRef",ref:$e},null,8,["upgradeKey"]),e(Kt,{ref_key:"upgradeRef",ref:ke,onComplete:L,onCloudbuild:Cl},null,512),e(Ot,{ref_key:"cloudBuildRef",ref:oe},null,512)],64)}}});const fn=Qt(ks,[["__scopeId","data-v-e8673776"]]);export{fn as default};
