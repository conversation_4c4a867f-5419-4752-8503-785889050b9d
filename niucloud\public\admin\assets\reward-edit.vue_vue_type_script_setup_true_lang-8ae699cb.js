import{d as C,r as m,n as B,q as e,aN as F,h as b,s as y,w as d,a as j,e as o,i as p,t as u,u as r,c as S,F as I,W as T,$ as E,a4 as A,a1 as O,M as $,L as G,ax as W,bL as z,au as H,av as J,N as K,E as Q,V as X}from"./index-30109030.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                        */import"./el-form-item-4ed993c7.js";/* empty css               *//* empty css                  *//* empty css                  */import{o as Z,p as ee,b as le}from"./project-8517d3f3.js";import te from"./index-e3ceb692.js";const ae={class:"text-sm text-gray-500 mt-1"},oe={class:"dialog-footer"},Re=C({__name:"reward-edit",emits:["complete"],setup(re,{expose:R,emit:D}){const n=m(!1),c=m(!1),_=m(),V=m([]),l=B({reward_id:"",project_id:"",name:"",amount:0,image:"",description:"",limit_count:0,delivery_time:"",sort:0,status:1}),N={project_id:[{required:!0,message:e("projectRequired"),trigger:"change"}],name:[{required:!0,message:e("rewardNameRequired"),trigger:"blur"}],amount:[{required:!0,message:e("supportAmountRequired"),trigger:"blur"}],description:[{required:!0,message:e("rewardDescriptionRequired"),trigger:"blur"}]},x=(s={})=>{Object.assign(l,{reward_id:"",project_id:"",name:"",amount:0,image:"",description:"",limit_count:0,delivery_time:"",sort:0,status:1},s)},U=async()=>{_.value&&await _.value.validate(async s=>{if(s){c.value=!0;try{l.reward_id?(await Z(l.reward_id,l),E.success(e("editSuccess"))):(await ee(l),E.success(e("addSuccess"))),n.value=!1,D("complete")}catch(t){console.error("Save reward failed:",t)}finally{c.value=!1}}})},k=async()=>{try{const s=await le({});V.value=s.data}catch(s){console.error("Load project list failed:",s)}};return F(()=>{k()}),R({showEdit:n,setFormData:x}),(s,t)=>{const q=A,Y=O,i=$,g=G,f=W,L=z,w=H,M=J,P=K,v=Q,h=X;return b(),y(h,{modelValue:n.value,"onUpdate:modelValue":t[10]||(t[10]=a=>n.value=a),title:l.reward_id?r(e)("editReward"):r(e)("addReward"),width:"700px","destroy-on-close":!0},{footer:d(()=>[j("span",oe,[o(v,{onClick:t[9]||(t[9]=a=>n.value=!1)},{default:d(()=>[p(u(r(e)("cancel")),1)]),_:1}),o(v,{type:"primary",loading:c.value,onClick:U},{default:d(()=>[p(u(r(e)("confirm")),1)]),_:1},8,["loading"])])]),default:d(()=>[o(P,{ref_key:"formRef",ref:_,model:l,rules:N,"label-width":"120px"},{default:d(()=>[o(i,{label:r(e)("projectName"),prop:"project_id"},{default:d(()=>[o(Y,{modelValue:l.project_id,"onUpdate:modelValue":t[0]||(t[0]=a=>l.project_id=a),placeholder:r(e)("projectNamePlaceholder"),style:{width:"100%"},filterable:""},{default:d(()=>[(b(!0),S(I,null,T(V.value,a=>(b(),y(q,{key:a.project_id,label:a.name,value:a.project_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),o(i,{label:r(e)("rewardName"),prop:"name"},{default:d(()=>[o(g,{modelValue:l.name,"onUpdate:modelValue":t[1]||(t[1]=a=>l.name=a),placeholder:r(e)("rewardNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(i,{label:r(e)("supportAmount"),prop:"amount"},{default:d(()=>[o(f,{modelValue:l.amount,"onUpdate:modelValue":t[2]||(t[2]=a=>l.amount=a),min:1,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1},8,["label"]),o(i,{label:r(e)("rewardImage"),prop:"image"},{default:d(()=>[o(te,{modelValue:l.image,"onUpdate:modelValue":t[3]||(t[3]=a=>l.image=a)},null,8,["modelValue"])]),_:1},8,["label"]),o(i,{label:r(e)("rewardDescription"),prop:"description"},{default:d(()=>[o(g,{modelValue:l.description,"onUpdate:modelValue":t[4]||(t[4]=a=>l.description=a),type:"textarea",rows:4,placeholder:r(e)("rewardDescriptionPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(i,{label:r(e)("limitCount"),prop:"limit_count"},{default:d(()=>[o(f,{modelValue:l.limit_count,"onUpdate:modelValue":t[5]||(t[5]=a=>l.limit_count=a),min:0,style:{width:"100%"}},null,8,["modelValue"]),j("div",ae,u(r(e)("limitCountTip")),1)]),_:1},8,["label"]),o(i,{label:r(e)("deliveryTime"),prop:"delivery_time"},{default:d(()=>[o(L,{modelValue:l.delivery_time,"onUpdate:modelValue":t[6]||(t[6]=a=>l.delivery_time=a),type:"date",placeholder:r(e)("deliveryTimePlaceholder"),style:{width:"100%"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(i,{label:r(e)("sort"),prop:"sort"},{default:d(()=>[o(f,{modelValue:l.sort,"onUpdate:modelValue":t[7]||(t[7]=a=>l.sort=a),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1},8,["label"]),o(i,{label:r(e)("status"),prop:"status"},{default:d(()=>[o(M,{modelValue:l.status,"onUpdate:modelValue":t[8]||(t[8]=a=>l.status=a)},{default:d(()=>[o(w,{label:1},{default:d(()=>[p(u(r(e)("enable")),1)]),_:1}),o(w,{label:0},{default:d(()=>[p(u(r(e)("disable")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}});export{Re as _};
