import{d as M,y as R,r as v,n as D,q as l,aS as $,R as I,$ as h,h as V,c as q,Z as B,s as T,w as s,e as t,a as d,t as i,u as o,i as f,c1 as A,L as K,M as N,E as L,a9 as j,N as W,a3 as Z}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                */import{_ as z}from"./index.vue_vue_type_style_index_0_lang-9be1835f.js";/* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                 */import{h as G,i as H,j as J}from"./wxoplatform-89e3ebb7.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                    */const Q={class:"main-container"},X={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},Y={class:"box-card mt-[20px] !border-none",shadow:"never"},ee={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},le={class:"form-tip"},ae={class:"box-card mt-[20px] !border-none",shadow:"never"},oe={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},te={class:"box-card mt-[20px] !border-none",shadow:"never"},se={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},re={class:"input-width"},de={class:"form-tip"},pe={class:"fixed-footer-wrap"},ie={class:"fixed-footer"},ue=M({__name:"setting",setup(ne){R().meta.title;const _=v(!0),r=v({app_id:"",app_secret:"",token:"",aes_key:"",develop_app_id:"",develop_upload_private_key:""}),p=v({});G().then(({data:n})=>{p.value=n}),H().then(({data:n})=>{r.value=n,_.value=!1});const b=v(),x=D({app_id:[{required:!0,message:l("appidPlaceholder"),trigger:"blur"}],app_secret:[{required:!0,message:l("appSecretPlaceholder"),trigger:"blur"}],token:[{required:!0,message:l("tokenPlaceholder"),trigger:"blur"}],aes_key:[{required:!0,message:l("aesKeyPlaceholder"),trigger:"blur"}]}),k=()=>{r.value.aes_key=A(43)},w=async n=>{_.value||!n||await n.validate(async e=>{e&&(_.value=!0,J(r.value).then(()=>{_.value=!1}).catch(()=>{_.value=!1}))})},{copy:E,isSupported:C,copied:g}=$(),c=n=>{if(!C.value){h({message:l("notSupportCopy"),type:"warning"});return}E(n)};return I(g,()=>{g.value&&h({message:l("copySuccess"),type:"success"})}),(n,e)=>{const m=K,u=N,y=L,P=z,F=j,U=W,S=Z;return V(),q("div",Q,[B((V(),T(U,{model:r.value,rules:x,"label-width":"150px",ref_key:"formRef",ref:b,class:"page-form p-[20px]"},{default:s(()=>[t(F,{class:"box-card !border-none",shadow:"never"},{default:s(()=>[d("h3",X,i(o(l)("oplatformSetting")),1),t(u,{label:"APPID",prop:"app_id"},{default:s(()=>[t(m,{modelValue:r.value.app_id,"onUpdate:modelValue":e[0]||(e[0]=a=>r.value.app_id=a),modelModifiers:{trim:!0},placeholder:o(l)("appidPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1}),t(u,{label:"APPSECRET",prop:"app_secret"},{default:s(()=>[t(m,{modelValue:r.value.app_secret,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value.app_secret=a),modelModifiers:{trim:!0},placeholder:o(l)("appSecretPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1}),d("div",Y,[d("h3",ee,i(o(l)("messagesReceiving")),1),t(u,{label:o(l)("empowerReceiveUrl")},{default:s(()=>[t(m,{modelValue:p.value.auth_serve_url,"onUpdate:modelValue":e[3]||(e[3]=a=>p.value.auth_serve_url=a),modelModifiers:{trim:!0},placeholder:"Please input",class:"!w-[500px]",readonly:!0},{append:s(()=>[d("div",{class:"cursor-pointer",onClick:e[2]||(e[2]=a=>c(p.value.auth_serve_url))},i(o(l)("copy")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(u,{label:o(l)("messageReceiveUrl")},{default:s(()=>[t(m,{modelValue:p.value.message_serve_url,"onUpdate:modelValue":e[5]||(e[5]=a=>p.value.message_serve_url=a),modelModifiers:{trim:!0},placeholder:"Please input",class:"!w-[500px]",readonly:!0},{append:s(()=>[d("div",{class:"cursor-pointer",onClick:e[4]||(e[4]=a=>c(p.value.message_serve_url))},i(o(l)("copy")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(u,{label:o(l)("messageValidationToken"),prop:"token"},{default:s(()=>[t(m,{modelValue:r.value.token,"onUpdate:modelValue":e[6]||(e[6]=a=>r.value.token=a),modelModifiers:{trim:!0},class:"input-width",clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),t(u,{label:o(l)("messageDecryptKey"),prop:"aes_key"},{default:s(()=>[t(m,{modelValue:r.value.aes_key,"onUpdate:modelValue":e[8]||(e[8]=a=>r.value.aes_key=a),modelModifiers:{trim:!0},class:"input-width"},{append:s(()=>[d("div",{class:"cursor-pointer",onClick:e[7]||(e[7]=a=>c(r.value.aes_key))},i(o(l)("copy")),1)]),_:1},8,["modelValue"]),d("div",le,[f(i(o(l)("messageDecryptKeyTips")),1),t(y,{type:"primary",link:"",onClick:k},{default:s(()=>[f(i(o(l)("regenerate")),1)]),_:1})])]),_:1},8,["label"])]),d("div",ae,[d("h3",oe,i(o(l)("domainSetting")),1),t(u,{label:o(l)("empowerStartDomain")},{default:s(()=>[t(m,{modelValue:p.value.auth_launch_domain,"onUpdate:modelValue":e[10]||(e[10]=a=>p.value.auth_launch_domain=a),modelModifiers:{trim:!0},placeholder:"Please input",class:"input-width",readonly:!0},{append:s(()=>[d("div",{class:"cursor-pointer",onClick:e[9]||(e[9]=a=>c(p.value.auth_launch_domain))},i(o(l)("copy")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(u,{label:o(l)("wechatDomain")},{default:s(()=>[t(m,{modelValue:p.value.wechat_auth_domain,"onUpdate:modelValue":e[12]||(e[12]=a=>p.value.wechat_auth_domain=a),modelModifiers:{trim:!0},placeholder:"Please input",class:"input-width",readonly:!0},{append:s(()=>[d("div",{class:"cursor-pointer",onClick:e[11]||(e[11]=a=>c(p.value.wechat_auth_domain))},i(o(l)("copy")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"])]),d("div",te,[d("h3",se,i(o(l)("developerWeappUpload")),1),t(u,{label:o(l)("developAppid"),prop:"develop_app_id"},{default:s(()=>[t(m,{modelValue:r.value.develop_app_id,"onUpdate:modelValue":e[13]||(e[13]=a=>r.value.develop_app_id=a),modelModifiers:{trim:!0},placeholder:o(l)("developAppidPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(u,{label:o(l)("uploadKey"),prop:"develop_upload_private_key"},{default:s(()=>[d("div",re,[t(P,{modelValue:r.value.develop_upload_private_key,"onUpdate:modelValue":e[14]||(e[14]=a=>r.value.develop_upload_private_key=a),api:"sys/document/wechat"},null,8,["modelValue"])]),d("div",de,i(o(l)("uploadIpTips"))+i(p.value.upload_ip),1)]),_:1},8,["label"])])]),_:1})]),_:1},8,["model","rules"])),[[S,_.value]]),d("div",pe,[d("div",ie,[t(y,{type:"primary",loading:_.value,onClick:e[15]||(e[15]=a=>w(b.value))},{default:s(()=>[f(i(o(l)("save")),1)]),_:1},8,["loading"])])])])}}});const Ee=O(ue,[["__scopeId","data-v-84dded3b"]]);export{Ee as default};
