const e="订单编号",o="订单信息",n="交易流水号",t="订单类型",s="订单来源",r="收货人",c="收货人手机号",d="收货地址",i="商品信息",a="商品名称",l="价格",u="数量",y="商品总额",f="优惠金额",m="配送金额",p="订单日志",T="请输入订单编号",M="订单状态",h="请选择订单状态",P="订单类型",v="请选择订单类型",I="支付时间",N="商品",b="单价（元）/数量",S="订单金额",g="买家/收货人",k="配送方式",j="开始时间",R="结束时间",D="件",G="支付方式",A="备注",F="修改地址",C="提醒",L="买家付款成功后，货款将直接进入您的商户号（微信、支付宝）",W="请及时关注你发出的包裹状态，确保可以配送至买家手中",x="如果买家表示没收到货或货物有问题，请及时联系买家处理，友好协商",w="关闭订单",O="确认收货",q="订单发货",B="请选择配送方式",E="物流公司",U="请选择物流公司",_="物流单号",z="请输入物流单号",H="请选择订单项",J="虚拟发货",K="关闭订单后该订单将无法支付，是否确认关闭？",Q="是否确认用户已经收货？",V="请选择要发货的商品",X="买家留言",Y="总优惠金额",Z="优惠券优惠金额",$="满减优惠金额",ee="物流信息",oe="发货时间",ne="物流公司",te="物流单号",se="物流包裹信息",re="发货信息",ce="商品信息",de="物流信息",ie="自提点名称",ae="自提点地址",le="自提点电话",ue="营业时间",ye="发货状态",fe="无需物流",me="暂无物流信息",pe="退款状态",Te="暂无数据",Me="修改价格",he="调整价格",Pe="元",ve="注意 : 只有订单未付款时才支持改价,改价后请联系买家刷新订单核实订单金额后再支付。当订单总额为0元时，订单将自动支付",Ie="运费",Ne="商品信息",be="单价",Se="数量",ge="小计",ke="优惠金额",je="调整金额",Re="总计",De="积分",Ge="打印电子面单",Ae="面单模板",Fe="请选择面单模板",Ce="打印结果",Le="包裹编号",We="状态",xe="状态码",we="备注",Oe="打印面单",qe="打印小票",Be="发货方式",Ee="手动填写",Ue="电子面单",_e="联系人",ze="请输入联系人",He="联系方式",Je="请输入联系方式",Ke="自提门店",Qe="地址",Ve="请选择省",Xe="请选择市",Ye="请选择区/县",Ze="请输入详细地址",$e="商家主动退款",eo="退款金额",oo="主动退款",no="完成状态",to="退款方式",so="退款说明",ro="1、如果是退部分金额，退款后可以是部分退款状态或退款完成状态",co="2、如果是退全部金额，则退款后一定是退款完成状态",io="3、退款完成才会执行相关业务如核销码失效，卡包失效等操作",ao="原路退款",lo="线下退款",uo="退款到余额",yo="部分退款状态",fo="退款完成状态",mo="主动退款金额不能为空或为零",po="主动退款金额不能大于可退款总额",To="商品项",Mo="商品信息",ho="规格",Po="售价",vo="实付金额",Io="数量",No="请选择退款的商品",bo="表单详情",So="要求自提时间",go="核销码",ko="买家",jo="预留手机",Ro="提货人",Do="核销人",Go={orderNo:e,orderInfo:o,outTradeNo:n,orderType:t,orderForm:s,takerName:r,takerMobile:c,takerFullAddress:d,goodsDetail:i,goodsName:a,price:l,num:u,goodsMoney:y,preferentialMoney:f,deliveryMoney:m,operateLog:p,orderNoPlaceholder:T,orderStatus:M,orderStatusPlaceholder:h,orderFrom:P,orderFromPlaceholder:v,payTime:I,orderGoods:N,goodsPriceNumber:b,orderMoney:S,buyInfo:g,deliveryType:k,startDate:j,endDate:R,piece:D,payType:G,notes:A,editAddress:F,remind:C,remindTips1:L,remindTips2:W,remindTips3:x,close:w,finish:O,delivery:q,deliveryTypePlaceholder:B,company:E,companyPlaceholder:U,expressNumber:_,expressNumberPlaceholder:z,orderGoodsIdsPlaceholder:H,virtualDelivery:J,orderCloseTips:K,orderFinishTips:Q,orderGoodsPlaceholder:V,memberRemark:X,discountMoney:Y,couponMoney:Z,manjianDiscountMoney:$,orderDelivery:ee,devliveryTime:oe,companyName:ne,logisticNo:te,packageInfo:se,deliveryInfo:re,goodsInfo:ce,logisticInfo:de,storeName:ie,storeAddress:ae,storeMobile:le,tradeTime:ue,deliveryStatusName:ye,package:"包裹",noLogisticsRequired:fe,notLogistics:me,refundStatusName:pe,orderInfoEmpty:Te,editPrice:Me,adjustMoneyDialogTitle:he,adjustMoneyUnit:Pe,adjustMoneyTips:ve,adjustMoneyDeliveryMoney:Ie,adjustMoneyGoodsInfo:Ne,adjustMoneyPrice:be,adjustMoneyNum:Se,adjustMoneySubTotal:ge,adjustMoneyDiscountMoney:ke,adjustMoneyLabel:je,adjustMoneyTotal:Re,point:De,electronicSheetPrintTitle:Ge,electronicSheetTemplate:Ae,electronicSheetTemplatePlaceholder:Fe,electronicSheetPrintResult:Ce,deliveryPackageNo:Le,printStatus:We,printResultCode:xe,printRemark:we,electronicSheetPrintOperation:Oe,printTicket:qe,deliveryWay:Be,manualWriteWay:Ee,electronicSheetWay:Ue,contacts:_e,contactsPlaceholder:ze,ContactInformation:He,ContactInformationPlaceholder:Je,selfPickupStores:Ke,address:Qe,province:Ve,city:Xe,area:Ye,detailedAddress:Ze,refundTitle:$e,refundMoney:eo,voluntaryRefund:oo,refundState:no,refundType:to,refundInstructions:so,refundInstructionsOne:ro,refundInstructionsTwo:co,refundInstructionsThree:io,backRefund:ao,offlineRefund:lo,refundToBalance:uo,partialRefund:yo,refundFinish:fo,shopActiveRefundMoneyPlaceholder:mo,shopActiveRefundMoneyTwoPlaceholder:po,refundGoodsItem:To,refundGoodsInfo:Mo,refundGoodsSku:ho,refundGoodsPrice:Po,refundPayPrice:vo,refundGoodsNum:Io,refundGoodsPlaceholder:No,formDetail:bo,buyerAskDeliveryTime:So,verifyCode:go,buyers:ko,buyersReserveMobile:jo,storeTakerName:Ro,verifierMember:Do};export{He as ContactInformation,Je as ContactInformationPlaceholder,Qe as address,Ie as adjustMoneyDeliveryMoney,he as adjustMoneyDialogTitle,ke as adjustMoneyDiscountMoney,Ne as adjustMoneyGoodsInfo,je as adjustMoneyLabel,Se as adjustMoneyNum,be as adjustMoneyPrice,ge as adjustMoneySubTotal,ve as adjustMoneyTips,Re as adjustMoneyTotal,Pe as adjustMoneyUnit,Ye as area,ao as backRefund,g as buyInfo,So as buyerAskDeliveryTime,ko as buyers,jo as buyersReserveMobile,Xe as city,w as close,E as company,ne as companyName,U as companyPlaceholder,_e as contacts,ze as contactsPlaceholder,Z as couponMoney,Go as default,q as delivery,re as deliveryInfo,m as deliveryMoney,Le as deliveryPackageNo,ye as deliveryStatusName,k as deliveryType,B as deliveryTypePlaceholder,Be as deliveryWay,Ze as detailedAddress,oe as devliveryTime,Y as discountMoney,F as editAddress,Me as editPrice,Oe as electronicSheetPrintOperation,Ce as electronicSheetPrintResult,Ge as electronicSheetPrintTitle,Ae as electronicSheetTemplate,Fe as electronicSheetTemplatePlaceholder,Ue as electronicSheetWay,R as endDate,_ as expressNumber,z as expressNumberPlaceholder,O as finish,bo as formDetail,i as goodsDetail,ce as goodsInfo,y as goodsMoney,a as goodsName,b as goodsPriceNumber,de as logisticInfo,te as logisticNo,$ as manjianDiscountMoney,Ee as manualWriteWay,X as memberRemark,fe as noLogisticsRequired,me as notLogistics,A as notes,u as num,lo as offlineRefund,p as operateLog,K as orderCloseTips,ee as orderDelivery,Q as orderFinishTips,s as orderForm,P as orderFrom,v as orderFromPlaceholder,N as orderGoods,H as orderGoodsIdsPlaceholder,V as orderGoodsPlaceholder,o as orderInfo,Te as orderInfoEmpty,S as orderMoney,e as orderNo,T as orderNoPlaceholder,M as orderStatus,h as orderStatusPlaceholder,t as orderType,n as outTradeNo,se as packageInfo,yo as partialRefund,I as payTime,G as payType,D as piece,De as point,f as preferentialMoney,l as price,we as printRemark,xe as printResultCode,We as printStatus,qe as printTicket,Ve as province,fo as refundFinish,Mo as refundGoodsInfo,To as refundGoodsItem,Io as refundGoodsNum,No as refundGoodsPlaceholder,Po as refundGoodsPrice,ho as refundGoodsSku,so as refundInstructions,ro as refundInstructionsOne,io as refundInstructionsThree,co as refundInstructionsTwo,eo as refundMoney,vo as refundPayPrice,no as refundState,pe as refundStatusName,$e as refundTitle,uo as refundToBalance,to as refundType,C as remind,L as remindTips1,W as remindTips2,x as remindTips3,Ke as selfPickupStores,mo as shopActiveRefundMoneyPlaceholder,po as shopActiveRefundMoneyTwoPlaceholder,j as startDate,ae as storeAddress,le as storeMobile,ie as storeName,Ro as storeTakerName,d as takerFullAddress,c as takerMobile,r as takerName,ue as tradeTime,Do as verifierMember,go as verifyCode,J as virtualDelivery,oo as voluntaryRefund};
