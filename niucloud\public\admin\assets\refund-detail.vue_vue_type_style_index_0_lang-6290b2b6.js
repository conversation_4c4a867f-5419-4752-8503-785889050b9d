import{d as Ee,f as Fe,r as v,n as W,l as $,q as l,h as u,s as x,w as a,Z as X,c as _,e as s,C as p,u as t,a as o,t as r,F as P,W as I,B as L,i as E,X as Ce,af as Z,ap as De,aq as Te,M as Ne,aQ as Be,aR as Me,K as Ue,ag as Ae,ah as Se,N as qe,aa as $e,a9 as Pe,L as Ie,E as Le,V as Oe,a4 as je,a1 as ze,bS as Ke,a3 as Qe}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                 *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                 *//* empty css                        *//* empty css               */import"./el-form-item-4ed993c7.js";/* empty css                    */import{_ as We}from"./goods_default-664bb559.js";import{x as G,y as H,z as Xe,A as Ze}from"./order-caf8547a.js";import{f as Ge}from"./shop_address-02f3761f.js";const He={class:"main-container"},Je={key:0},Ye={class:"input-width"},el={class:"input-width"},ll={class:"input-width"},al={class:"input-width"},tl={key:0},sl={class:"input-width"},ol={class:"input-width"},rl={class:"input-width"},nl={key:1},dl={class:"input-width"},ul={class:"input-width"},il={class:"input-width"},pl={class:"input-width"},_l={class:"input-width"},cl={class:"input-width line-feed"},fl={key:1},ml={class:"text-[14px] min-w-[110px] border-solid border-l-[3px] border-[var(--el-color-primary)] pl-[5px]"},vl={class:"px-[30px] mt-[20px] mb-[20px]"},xl={class:"input-width"},bl={class:"input-width"},yl={class:"input-width"},hl={key:0,class:"input-width"},gl={key:1,class:"input-width"},wl={class:"input-width"},kl={key:0,class:"input-width flex"},Rl=o("div",{class:"image-slot"},[o("img",{class:"w-[70px] h-[70px]",src:We})],-1),Vl={class:"max-w-[100%] break-all"},El={key:0,class:"text-[14px] min-w-[110px] border-solid border-l-[3px] border-[var(--el-color-primary)] pl-[5px]"},Fl={key:1,class:"px-[30px] mt-[20px] mb-[20px]"},Cl={class:"input-width"},Dl={class:"input-width"},Tl={class:"input-width"},Nl={class:"text-[14px] min-w-[110px] border-solid border-l-[3px] border-[var(--el-color-primary)] pl-[5px]"},Bl={class:"px-[30px] mt-[20px] mb-[20px]"},Ml={class:"ml-[30px] text-[14px] mr-[20px]"},Ul={class:"text-[14px]"},Al={key:0,class:"mt-[20px] flex"},Sl={class:"ml-[30px] text-[14px] mr-[20px] flex-shrink-0"},ql={class:"text-[14px] break-all"},$l={class:"flex mt-[10px]"},Pl={class:"flex ml-[30px] mt-[15px]"},Il={class:"text-[14px] text-[#ff7f5b]"},Ll={class:"ml-[10px]"},Ol={class:"text-[14px] text-[#a4a4a4]"},jl={class:"text-[14px] text-[#a4a4a4]"},zl={class:"text-[14px] text-[#a4a4a4]"},Kl={key:2},Ql={class:"flex"},Wl={class:"flex items-center w-[50px] h-[50px] mr-[10px]"},Xl=["src"],Zl={class:"flex flex-col flex-1"},Gl={class:"text-[12px] text-[#999] truncate"},Hl={key:3},Jl={class:"mb-[100px] px-[20px]",style:{"min-height":"100px"}},Yl={class:"mr-[20px] min-w-[71px]"},ea={class:"leading-[1] text-[14px] w-[100px] flex justify-end"},la={class:"leading-[1] text-[14px] w-[100px] flex justify-end mt-[15px]"},aa=o("div",{class:"w-[16px] h-[16px] flex items-center bg-[#D1EBFF] border-[1px] border-[#0091FF] rounded-[999px]"},[o("div",{class:"w-[8px] h-[8px] mx-auto bg-[#0091FF] rounded-[999px]"})],-1),ta={key:0,class:"w-[2px] h-[50px] bg-[#D1EBFF] mx-auto"},sa={class:"leading-[1] ml-[20px] text-[14px]"},oa={class:"leading-[1] ml-[20px] text-[14px] mt-[15px]"},ra={class:"dialog-footer"},na={key:0,class:"mt-[10px] text-[#999] text-[12px] leading-[20px]"},da={class:"dialog-footer"},ua={class:"dialog-footer"},Pa=Ee({__name:"refund-detail",emits:["load"],setup(ia,{expose:J,emit:Y}){const O=Fe(),y=v(!1);let ee="订单详情";const m=v(!1),e=v(null),F=v(!1),le=c=>{F.value=!1},h=v("order");let M="";const ae=async()=>{y.value=!0,M?await Ze(M).then(({data:c})=>{e.value=c,e.value.order_goods=[c.order_goods],y.value=!1}).catch(()=>{}):y.value=!1},g=v(!1),f=W({...{shop_reason:"",refund_address_id:"",money:"",type:"",apply_money:0,refund_type:1}}),U=v(),j=v(),te=$(()=>({money:[{required:!0,message:l("moneyPlaceholder"),trigger:"blur"}],refund_address_id:[{required:!0,message:l("refundAddressPlaceholder"),trigger:"blur"}]})),se=$(()=>({shop_reason:[{required:!0,message:l("shopReasonPlaceholder"),trigger:"blur"}]})),C=v(!1),oe=()=>{f.type="agree",f.refund_type=e.value.refund_type,f.apply_money=e.value.apply_money,f.money=e.value.apply_money,C.value=!0,e.value.refund_type==2&&re()},z=v([]),re=()=>{Ge().then(c=>{z.value=c.data,c.data.forEach(n=>{n.is_refund_address==1&&(f.refund_address_id=n.id)})})},ne=async c=>{m.value||!c||await c.validate(async n=>{n&&(m.value=!0,G({order_refund_no:e.value.order_refund_no,money:f.money,is_agree:1,refund_address_id:f.refund_address_id}).then(()=>{C.value=!1,m.value=!1,D()}).catch(()=>{m.value=!1}))})},de=()=>{f.type="refuse",f.shop_reason="",g.value=!0},ue=async c=>{m.value||!c||await c.validate(async n=>{n&&(m.value=!0,G({order_refund_no:e.value.order_refund_no,is_agree:0,shop_reason:f.shop_reason}).then(k=>{D(),m.value=!1,g.value=!1}).catch(()=>{m.value=!1,g.value=!1}))})},ie=()=>{Z.confirm(l("orderDeliveryTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{H({order_refund_no:e.value.order_refund_no,is_agree:1}).then(()=>{D()})})},w=v(!1),T=W({shop_reason:""}),K=v(),pe=$(()=>({shop_reason:[{required:!0,message:l("shopReasonPlaceholder"),trigger:"blur"}]})),_e=async c=>{m.value||!c||await c.validate(async n=>{n&&(m.value=!0,H({order_refund_no:e.value.order_refund_no,is_agree:0,shop_reason:T.shop_reason}).then(k=>{D(),m.value=!1,w.value=!1}).catch(()=>{m.value=!1,w.value=!1}))})},ce=()=>{f.type="refuse",f.shop_reason="",w.value=!0},fe=()=>{const c=O.resolve({path:"/member/refund/detail",query:{refund_no:e.value.refund_no}});window.open(c.href,"_blank")},me=c=>{const n=O.resolve({path:"/shop/order/detail",query:{order_id:c}});window.open(n.href,"_blank")},ve=()=>{Z.confirm(l("closeRefundTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{Xe(e.value.order_refund_no).then(()=>{D()})})},xe=async(c=null)=>{M=c.id,e.value=null,h.value="order",ae()},D=()=>{F.value=!1,Y("load")};return J({showDialog:F,setFormData:xe}),(c,n)=>{const k=De,be=Te,i=Ne,b=Be,N=Me,ye=Ue,A=Ae,he=Se,B=qe,ge=$e,we=Pe,S=Ie,R=Le,q=Oe,ke=je,Re=ze,Ve=Ke,Q=Qe;return u(),x(Ve,{modelValue:F.value,"onUpdate:modelValue":n[15]||(n[15]=d=>F.value=d),title:t(ee),direction:"rtl","before-close":le,class:"member-detail-drawer"},{default:a(()=>[X((u(),_("div",He,[s(be,{modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=d=>h.value=d),class:"pb-[10px]",onTabChange:c.tabChange},{default:a(()=>[s(k,{label:"订单信息",name:"order"}),s(k,{label:"售后信息",name:"refund"}),s(k,{label:"商品信息",name:"goods"}),e.value&&e.value.refund_log&&e.value.refund_log.length>0?(u(),x(k,{key:0,label:"订单日志",name:"log"})):p("",!0)]),_:1},8,["modelValue","onTabChange"]),y.value?p("",!0):(u(),x(B,{key:0,model:e.value,"label-width":"100px",ref_key:"formRef",ref:U,class:"page-form","label-position":"left"},{default:a(()=>[h.value=="order"?(u(),_("div",Je,[s(N,{class:"row-bg px-[30px] mb-[20px]",gutter:20},{default:a(()=>[s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("orderNo")},{default:a(()=>[o("div",{class:"input-width text-primary cursor-pointer",onClick:n[1]||(n[1]=d=>me(e.value.order_id))},r(e.value.order_main.order_no),1)]),_:1},8,["label"]),s(i,{label:t(l)("orderForm")},{default:a(()=>[o("div",Ye,r(e.value.order_main.order_from_name),1)]),_:1},8,["label"]),e.value.order_main.out_trade_no?(u(),x(i,{key:0,label:t(l)("outTradeNo")},{default:a(()=>[o("div",el,r(e.value.order_main.out_trade_no),1)]),_:1},8,["label"])):p("",!0),e.value.pay_refund?(u(),x(i,{key:1,label:t(l)("payType")},{default:a(()=>[o("div",ll,r(e.value.pay_refund.type_name),1)]),_:1},8,["label"])):p("",!0)]),_:1}),s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("deliveryType")},{default:a(()=>[o("div",al,r(e.value.order_main.delivery_type_name),1)]),_:1},8,["label"]),e.value.order_main.delivery_type=="express"||e.value.order_main.delivery_type=="local_delivery"?(u(),_("div",tl,[s(i,{label:t(l)("takerName")},{default:a(()=>[o("div",sl,r(e.value.order_main.taker_name),1)]),_:1},8,["label"]),s(i,{label:t(l)("takerMobile")},{default:a(()=>[o("div",ol,r(e.value.order_main.taker_mobile),1)]),_:1},8,["label"]),s(i,{label:t(l)("takerFullAddress")},{default:a(()=>[o("div",rl,r(e.value.order_main.taker_full_address),1)]),_:1},8,["label"])])):p("",!0),e.value.order_main.delivery_type=="store"?(u(),_("div",nl,[s(i,{label:t(l)("storeName")},{default:a(()=>[o("div",dl,r(e.value.store.store_name),1)]),_:1},8,["label"]),s(i,{label:t(l)("storeAddress")},{default:a(()=>[o("div",ul,r(e.value.store.full_address),1)]),_:1},8,["label"]),s(i,{label:t(l)("storeMobile")},{default:a(()=>[o("div",il,r(e.value.store.store_mobile),1)]),_:1},8,["label"]),s(i,{label:t(l)("tradeTime")},{default:a(()=>[o("div",pl,r(e.value.store.trade_time),1)]),_:1},8,["label"])])):p("",!0)]),_:1}),s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("memberRemark")},{default:a(()=>[o("div",_l,r(e.value.order_main.member_remark),1)]),_:1},8,["label"]),s(i,{label:t(l)("notes")},{default:a(()=>[o("div",cl,r(e.value.order_main.shop_remark),1)]),_:1},8,["label"])]),_:1})]),_:1})])):p("",!0),h.value=="refund"?(u(),_("div",fl,[o("div",ml,r(t(l)("afterSales")),1),o("div",vl,[s(N,{class:"row-bg px-[30px] mb-[20px]",gutter:20},{default:a(()=>[s(b,{span:12},{default:a(()=>[s(i,{label:t(l)("orderRefundNo")},{default:a(()=>[o("div",xl,r(e.value.order_refund_no),1)]),_:1},8,["label"]),s(i,{label:t(l)("refundType")},{default:a(()=>[o("div",bl,r(e.value.refund_type_name),1)]),_:1},8,["label"]),s(i,{label:t(l)("createTime")},{default:a(()=>[o("div",yl,r(e.value.create_time),1)]),_:1},8,["label"])]),_:1}),s(b,{span:12},{default:a(()=>[s(i,{label:t(l)("refundMoney")},{default:a(()=>[e.value.status==8?(u(),_("div",hl,"￥"+r(e.value.money),1)):(u(),_("div",gl,"￥"+r(e.value.apply_money),1))]),_:1},8,["label"]),s(i,{label:t(l)("refundReason")},{default:a(()=>[o("div",wl,r(e.value.reason),1)]),_:1},8,["label"])]),_:1})]),_:1}),s(N,{class:"row-bg px-[30px] mb-[20px]",gutter:20},{default:a(()=>[s(b,{span:24},{default:a(()=>[s(i,{label:t(l)("refundVoucher")},{default:a(()=>[e.value.voucher?(u(),_("div",kl,[(u(!0),_(P,null,I(e.value.voucher,(d,V)=>(u(),_("div",{class:"mr-3",key:V},[d?(u(),x(ye,{key:0,class:"w-[70px] h-[70px]",src:t(L)(d),fit:"contain","preview-src-list":[t(L)(d)]},{error:a(()=>[Rl]),_:2},1032,["src","preview-src-list"])):p("",!0)]))),128))])):p("",!0)]),_:1},8,["label"]),s(i,{label:t(l)("refundRemark")},{default:a(()=>[o("div",Vl,r(e.value.remark),1)]),_:1},8,["label"])]),_:1})]),_:1})]),e.value.status==4?(u(),_("div",El,"买家退货信息")):p("",!0),e.value.status==4?(u(),_("div",Fl,[e.value.status==4?(u(),x(N,{key:0,class:"row-bg px-[30px] mb-[20px]",gutter:20},{default:a(()=>[s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("expressCompany")},{default:a(()=>[o("div",Cl,r(e.value.delivery.express_company),1)]),_:1},8,["label"])]),_:1}),s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("expressNumber")},{default:a(()=>[o("div",Dl,r(e.value.delivery.express_number),1)]),_:1},8,["label"])]),_:1}),s(b,{span:8},{default:a(()=>[s(i,{label:t(l)("expressRemark")},{default:a(()=>[o("div",Tl,r(e.value.delivery.remark),1)]),_:1},8,["label"])]),_:1})]),_:1})):p("",!0)])):p("",!0),o("div",Nl,r(t(l)("refundStatus")),1),o("div",Bl,[o("p",null,[o("span",Ml,r(t(l)("refundStatus"))+"：",1),o("span",Ul,r(e.value.status_name),1)]),e.value.shop_reason?(u(),_("div",Al,[o("span",Sl,r(t(l)("refuseReason"))+"：",1),o("div",ql,r(e.value.shop_reason),1)])):p("",!0),o("div",$l,[e.value.status==1?(u(),_("span",{key:0,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:oe},r(t(l)("agree")),1)):p("",!0),e.value.status==1?(u(),_("span",{key:1,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:de},r(t(l)("refuse")),1)):p("",!0),e.value.status==4&&e.value.refund_type==2?(u(),_("span",{key:2,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:ie},r(t(l)("confirmDelivery")),1)):p("",!0),e.value.status==4&&e.value.refund_type==2?(u(),_("span",{key:3,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:ce},r(t(l)("refuse")),1)):p("",!0),e.value.status==6?(u(),_("span",{key:4,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:fe},r(t(l)("transferAccounts")),1)):p("",!0),e.value.status!=8&&e.value.status!=-3?(u(),_("span",{key:5,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:ve},r(t(l)("closeRefund")),1)):p("",!0)]),o("div",Pl,[o("span",Il,r(t(l)("remind"))+"：",1),o("div",Ll,[o("p",Ol,r(t(l)("remindTips1")),1),o("p",jl,r(t(l)("remindTips2")),1),o("p",zl,r(t(l)("remindTips3")),1)])])])])):p("",!0),h.value=="goods"?(u(),_("div",Kl,[s(he,{data:e.value.order_goods,size:"large"},{default:a(()=>[s(A,{label:t(l)("goodsName"),align:"left",width:"300"},{default:a(({row:d})=>[o("div",Ql,[o("div",Wl,[o("img",{class:"w-[50px] h-[50px]",src:t(L)(d.goods_image)},null,8,Xl)]),o("div",Zl,[o("span",null,r(d.goods_name),1),o("span",Gl,r(d.sku_name),1)])])]),_:1},8,["label"]),s(A,{prop:"price",label:t(l)("price"),"min-width":"50",align:"left"},null,8,["label"]),s(A,{prop:"num",label:t(l)("num"),"min-width":"50",align:"right"},null,8,["label"])]),_:1},8,["data"])])):p("",!0),h.value=="log"&&e.value.refund_log.length>0?(u(),_("div",Hl,[o("div",Jl,[e.value.refund_log.length>0?(u(!0),_(P,{key:0},I(e.value.refund_log,(d,V)=>(u(),_("div",{class:"flex",key:V},[o("div",Yl,[o("div",ea,r(d.create_time.split(" ")[0]),1),o("div",la,r(d.create_time.split(" ")[1]),1)]),o("div",null,[aa,V+1!=e.value.refund_log.length?(u(),_("div",ta)):p("",!0)]),o("div",null,[o("div",sa,r(d.main_type_name)+r(d.main_name),1),o("div",oa,r(d.type_name),1)])]))),128)):p("",!0)])])):p("",!0)]),_:1},8,["model"])),!y.value&&!e.value?(u(),x(we,{key:1,class:"box-card !border-none relative",shadow:"never"},{default:a(()=>[s(ge,{description:t(l)("orderInfoEmpty")},null,8,["description"])]),_:1})):p("",!0),s(q,{modelValue:g.value,"onUpdate:modelValue":n[5]||(n[5]=d=>g.value=d),title:t(l)("orderRefundRefuse"),width:"460px",class:"diy-dialog-wrap","destroy-on-close":!0},{footer:a(()=>[o("span",ra,[s(R,{onClick:n[3]||(n[3]=d=>g.value=!1)},{default:a(()=>[E(r(t(l)("cancel")),1)]),_:1}),s(R,{type:"primary",loading:m.value,onClick:n[4]||(n[4]=d=>ue(j.value))},{default:a(()=>[E(r(t(l)("confirm")),1)]),_:1},8,["loading"])])]),default:a(()=>[s(B,{model:f,"label-width":"90px",ref_key:"refuseFormRef",ref:j,rules:t(se)},{default:a(()=>[s(i,{label:t(l)("refuseReason"),prop:"shop_reason"},{default:a(()=>[s(S,{modelValue:f.shop_reason,"onUpdate:modelValue":n[2]||(n[2]=d=>f.shop_reason=d),modelModifiers:{trim:!0},type:"textarea",rows:"4",clearable:"",class:"input-width",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),s(q,{modelValue:C.value,"onUpdate:modelValue":n[10]||(n[10]=d=>C.value=d),title:t(l)("orderRefundAgree"),width:"460px",class:"diy-dialog-wrap","destroy-on-close":!0},{footer:a(()=>[o("span",da,[s(R,{onClick:n[8]||(n[8]=d=>C.value=!1)},{default:a(()=>[E(r(t(l)("cancel")),1)]),_:1}),s(R,{type:"primary",loading:m.value,onClick:n[9]||(n[9]=d=>ne(U.value))},{default:a(()=>[E(r(t(l)("confirm")),1)]),_:1},8,["loading"])])]),default:a(()=>[X((u(),x(B,{onSubmit:Ce(c.onSubmit,["prevent"]),model:f,"label-width":"120px",ref_key:"formRef",ref:U,rules:t(te),class:"page-form"},{default:a(()=>[s(i,{label:t(l)("applyMoney")},{default:a(()=>[o("span",null,"￥"+r(f.apply_money),1)]),_:1},8,["label"]),s(i,{label:t(l)("agreeMoney"),prop:"money"},{default:a(()=>[o("div",null,[s(S,{modelValue:f.money,"onUpdate:modelValue":n[6]||(n[6]=d=>f.money=d),modelModifiers:{trim:!0},clearable:"",class:"input-width"},null,8,["modelValue"]),e.value.gift_balance&&Number(e.value.gift_balance)>Number(e.value.member.balance)?(u(),_("div",na," 当前订单需退还"+r(e.value.gift_balance)+"元赠品余额。若用户余额不足，则默认不进行扣除。请联系客户确认退款金额。 ",1)):p("",!0)])]),_:1},8,["label"]),f.refund_type==2?(u(),x(i,{key:0,label:t(l)("refundDeliveryAddress"),prop:"refund_address_id"},{default:a(()=>[s(Re,{modelValue:f.refund_address_id,"onUpdate:modelValue":n[7]||(n[7]=d=>f.refund_address_id=d),clearable:"",class:"input-item"},{default:a(()=>[(u(!0),_(P,null,I(z.value,(d,V)=>(u(),x(ke,{key:V,label:d.full_address,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):p("",!0)]),_:1},8,["onSubmit","model","rules"])),[[Q,y.value]])]),_:1},8,["modelValue","title"]),s(q,{modelValue:w.value,"onUpdate:modelValue":n[14]||(n[14]=d=>w.value=d),title:t(l)("orderRefundRefuse"),width:"460px",class:"diy-dialog-wrap","destroy-on-close":!0},{footer:a(()=>[o("span",ua,[s(R,{onClick:n[12]||(n[12]=d=>w.value=!1)},{default:a(()=>[E(r(t(l)("cancel")),1)]),_:1}),s(R,{type:"primary",loading:m.value,onClick:n[13]||(n[13]=d=>_e(K.value))},{default:a(()=>[E(r(t(l)("confirm")),1)]),_:1},8,["loading"])])]),default:a(()=>[s(B,{model:T,"label-width":"90px",ref_key:"deliveryRefuseFormRef",ref:K,rules:t(pe)},{default:a(()=>[s(i,{label:t(l)("refuseReason"),prop:"shop_reason"},{default:a(()=>[s(S,{modelValue:T.shop_reason,"onUpdate:modelValue":n[11]||(n[11]=d=>T.shop_reason=d),modelModifiers:{trim:!0},type:"textarea",rows:"4",clearable:"",class:"input-width",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])),[[Q,y.value]])]),_:1},8,["modelValue","title"])}}});export{Pa as _};
