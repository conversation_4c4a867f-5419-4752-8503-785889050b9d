/**!
 * Sortable 1.15.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function lt(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);e&&(o=o.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),t.push.apply(t,o)}return t}function z(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?lt(Object(t),!0).forEach(function(o){Nt(n,o,t[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):lt(Object(t)).forEach(function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(t,o))})}return n}function Me(n){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Me=function(e){return typeof e}:Me=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(n)}function Nt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function $(){return $=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o])}return n},$.apply(this,arguments)}function xt(n,e){if(n==null)return{};var t={},o=Object.keys(n),i,r;for(r=0;r<o.length;r++)i=o[r],!(e.indexOf(i)>=0)&&(t[i]=n[i]);return t}function Mt(n,e){if(n==null)return{};var t=xt(n,e),o,i;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);for(i=0;i<r.length;i++)o=r[i],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(n,o)&&(t[o]=n[o])}return t}var Ft="1.15.0";function q(n){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(n)}var V=q(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ce=q(/Edge/i),st=q(/firefox/i),ye=q(/safari/i)&&!q(/chrome/i)&&!q(/android/i),mt=q(/iP(ad|od|hone)/i),vt=q(/chrome/i)&&q(/android/i),bt={capture:!1,passive:!1};function E(n,e,t){n.addEventListener(e,t,!V&&bt)}function b(n,e,t){n.removeEventListener(e,t,!V&&bt)}function Re(n,e){if(e){if(e[0]===">"&&(e=e.substring(1)),n)try{if(n.matches)return n.matches(e);if(n.msMatchesSelector)return n.msMatchesSelector(e);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(e)}catch{return!1}return!1}}function Xt(n){return n.host&&n!==document&&n.host.nodeType?n.host:n.parentNode}function G(n,e,t,o){if(n){t=t||document;do{if(e!=null&&(e[0]===">"?n.parentNode===t&&Re(n,e):Re(n,e))||o&&n===t)return n;if(n===t)break}while(n=Xt(n))}return null}var ut=/\s+/g;function F(n,e,t){if(n&&e)if(n.classList)n.classList[t?"add":"remove"](e);else{var o=(" "+n.className+" ").replace(ut," ").replace(" "+e+" "," ");n.className=(o+(t?" "+e:"")).replace(ut," ")}}function h(n,e,t){var o=n&&n.style;if(o){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(n,""):n.currentStyle&&(t=n.currentStyle),e===void 0?t:t[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=t+(typeof t=="string"?"":"px")}}function ce(n,e){var t="";if(typeof n=="string")t=n;else do{var o=h(n,"transform");o&&o!=="none"&&(t=o+" "+t)}while(!e&&(n=n.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(t)}function Et(n,e,t){if(n){var o=n.getElementsByTagName(e),i=0,r=o.length;if(t)for(;i<r;i++)t(o[i],i);return o}return[]}function L(){var n=document.scrollingElement;return n||document.documentElement}function C(n,e,t,o,i){if(!(!n.getBoundingClientRect&&n!==window)){var r,a,l,s,u,d,c;if(n!==window&&n.parentNode&&n!==L()?(r=n.getBoundingClientRect(),a=r.top,l=r.left,s=r.bottom,u=r.right,d=r.height,c=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,c=window.innerWidth),(e||t)&&n!==window&&(i=i||n.parentNode,!V))do if(i&&i.getBoundingClientRect&&(h(i,"transform")!=="none"||t&&h(i,"position")!=="static")){var m=i.getBoundingClientRect();a-=m.top+parseInt(h(i,"border-top-width")),l-=m.left+parseInt(h(i,"border-left-width")),s=a+r.height,u=l+r.width;break}while(i=i.parentNode);if(o&&n!==window){var y=ce(i||n),v=y&&y.a,w=y&&y.d;y&&(a/=w,l/=v,c/=v,d/=w,s=a+d,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:d}}}function ft(n,e,t){for(var o=ee(n,!0),i=C(n)[e];o;){var r=C(o)[t],a=void 0;if(t==="top"||t==="left"?a=i>=r:a=i<=r,!a)return o;if(o===L())break;o=ee(o,!1)}return!1}function de(n,e,t,o){for(var i=0,r=0,a=n.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==p.ghost&&(o||a[r]!==p.dragged)&&G(a[r],t.draggable,n,!1)){if(i===e)return a[r];i++}r++}return null}function ot(n,e){for(var t=n.lastElementChild;t&&(t===p.ghost||h(t,"display")==="none"||e&&!Re(t,e));)t=t.previousElementSibling;return t||null}function k(n,e){var t=0;if(!n||!n.parentNode)return-1;for(;n=n.previousElementSibling;)n.nodeName.toUpperCase()!=="TEMPLATE"&&n!==p.clone&&(!e||Re(n,e))&&t++;return t}function ct(n){var e=0,t=0,o=L();if(n)do{var i=ce(n),r=i.a,a=i.d;e+=n.scrollLeft*r,t+=n.scrollTop*a}while(n!==o&&(n=n.parentNode));return[e,t]}function Yt(n,e){for(var t in n)if(n.hasOwnProperty(t)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===n[t][o])return Number(t)}return-1}function ee(n,e){if(!n||!n.getBoundingClientRect)return L();var t=n,o=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var i=h(t);if(t.clientWidth<t.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return L();if(o||e)return t;o=!0}}while(t=t.parentNode);return L()}function kt(n,e){if(n&&e)for(var t in e)e.hasOwnProperty(t)&&(n[t]=e[t]);return n}function ze(n,e){return Math.round(n.top)===Math.round(e.top)&&Math.round(n.left)===Math.round(e.left)&&Math.round(n.height)===Math.round(e.height)&&Math.round(n.width)===Math.round(e.width)}var _e;function wt(n,e){return function(){if(!_e){var t=arguments,o=this;t.length===1?n.call(o,t[0]):n.apply(o,t),_e=setTimeout(function(){_e=void 0},e)}}}function Rt(){clearTimeout(_e),_e=void 0}function yt(n,e,t){n.scrollLeft+=e,n.scrollTop+=t}function _t(n){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(n).cloneNode(!0):t?t(n).clone(!0)[0]:n.cloneNode(!0)}var Y="Sortable"+new Date().getTime();function Bt(){var n=[],e;return{captureAnimationState:function(){if(n=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(i){if(!(h(i,"display")==="none"||i===p.ghost)){n.push({target:i,rect:C(i)});var r=z({},n[n.length-1].rect);if(i.thisAnimationDuration){var a=ce(i,!0);a&&(r.top-=a.f,r.left-=a.e)}i.fromRect=r}})}},addAnimationState:function(o){n.push(o)},removeAnimationState:function(o){n.splice(Yt(n,{target:o}),1)},animateAll:function(o){var i=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var r=!1,a=0;n.forEach(function(l){var s=0,u=l.target,d=u.fromRect,c=C(u),m=u.prevFromRect,y=u.prevToRect,v=l.rect,w=ce(u,!0);w&&(c.top-=w.f,c.left-=w.e),u.toRect=c,u.thisAnimationDuration&&ze(m,c)&&!ze(d,c)&&(v.top-c.top)/(v.left-c.left)===(d.top-c.top)/(d.left-c.left)&&(s=Wt(v,m,y,i.options)),ze(c,d)||(u.prevFromRect=d,u.prevToRect=c,s||(s=i.options.animation),i.animate(u,v,c,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(e),r?e=setTimeout(function(){typeof o=="function"&&o()},a):typeof o=="function"&&o(),n=[]},animate:function(o,i,r,a){if(a){h(o,"transition",""),h(o,"transform","");var l=ce(this.el),s=l&&l.a,u=l&&l.d,d=(i.left-r.left)/(s||1),c=(i.top-r.top)/(u||1);o.animatingX=!!d,o.animatingY=!!c,h(o,"transform","translate3d("+d+"px,"+c+"px,0)"),this.forRepaintDummy=Ht(o),h(o,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){h(o,"transition",""),h(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},a)}}}}function Ht(n){return n.offsetWidth}function Wt(n,e,t,o){return Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*o.animation}var le=[],je={initializeByDefault:!0},Oe={mount:function(e){for(var t in je)je.hasOwnProperty(t)&&!(t in e)&&(e[t]=je[t]);le.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),le.push(e)},pluginEvent:function(e,t,o){var i=this;this.eventCanceled=!1,o.cancel=function(){i.eventCanceled=!0};var r=e+"Global";le.forEach(function(a){t[a.pluginName]&&(t[a.pluginName][r]&&t[a.pluginName][r](z({sortable:t},o)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](z({sortable:t},o)))})},initializePlugins:function(e,t,o,i){le.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var u=new l(e,t,e.options);u.sortable=e,u.options=e.options,e[s]=u,$(o,u.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var a=this.modifyOption(e,r,e.options[r]);typeof a<"u"&&(e.options[r]=a)}},getEventProperties:function(e,t){var o={};return le.forEach(function(i){typeof i.eventProperties=="function"&&$(o,i.eventProperties.call(t[i.pluginName],e))}),o},modifyOption:function(e,t,o){var i;return le.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[t]=="function"&&(i=r.optionListeners[t].call(e[r.pluginName],o))}),i}};function Gt(n){var e=n.sortable,t=n.rootEl,o=n.name,i=n.targetEl,r=n.cloneEl,a=n.toEl,l=n.fromEl,s=n.oldIndex,u=n.newIndex,d=n.oldDraggableIndex,c=n.newDraggableIndex,m=n.originalEvent,y=n.putSortable,v=n.extraEventProperties;if(e=e||t&&t[Y],!!e){var w,R=e.options,j="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!V&&!Ce?w=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(w=document.createEvent("Event"),w.initEvent(o,!0,!0)),w.to=a||t,w.from=l||t,w.item=i||t,w.clone=r,w.oldIndex=s,w.newIndex=u,w.oldDraggableIndex=d,w.newDraggableIndex=c,w.originalEvent=m,w.pullMode=y?y.lastPutMode:void 0;var A=z(z({},v),Oe.getEventProperties(o,e));for(var B in A)w[B]=A[B];t&&t.dispatchEvent(w),R[j]&&R[j].call(e,w)}}var Lt=["evt"],N=function(e,t){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=o.evt,r=Mt(o,Lt);Oe.pluginEvent.bind(p)(e,t,z({dragEl:f,parentEl:S,ghostEl:g,rootEl:_,nextEl:ae,lastDownEl:Fe,cloneEl:D,cloneHidden:J,dragStarted:be,putSortable:O,activeSortable:p.active,originalEvent:i,oldIndex:fe,oldDraggableIndex:De,newIndex:X,newDraggableIndex:Q,hideGhostForTarget:Ct,unhideGhostForTarget:Ot,cloneNowHidden:function(){J=!0},cloneNowShown:function(){J=!1},dispatchSortableEvent:function(l){P({sortable:t,name:l,originalEvent:i})}},r))};function P(n){Gt(z({putSortable:O,cloneEl:D,targetEl:f,rootEl:_,oldIndex:fe,oldDraggableIndex:De,newIndex:X,newDraggableIndex:Q},n))}var f,S,g,_,ae,Fe,D,J,fe,X,De,Q,Ae,O,ue=!1,Be=!1,He=[],ie,H,Ue,qe,dt,ht,be,se,Se,Te=!1,Pe=!1,Xe,I,$e=[],Je=!1,We=[],Le=typeof document<"u",Ne=mt,pt=Ce||V?"cssFloat":"float",zt=Le&&!vt&&!mt&&"draggable"in document.createElement("div"),Dt=function(){if(Le){if(V)return!1;var n=document.createElement("x");return n.style.cssText="pointer-events:auto",n.style.pointerEvents==="auto"}}(),St=function(e,t){var o=h(e),i=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),r=de(e,0,t),a=de(e,1,t),l=r&&h(r),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+C(r).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+C(a).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=i&&o[pt]==="none"||a&&o[pt]==="none"&&u+d>i)?"vertical":"horizontal"},jt=function(e,t,o){var i=o?e.left:e.top,r=o?e.right:e.bottom,a=o?e.width:e.height,l=o?t.left:t.top,s=o?t.right:t.bottom,u=o?t.width:t.height;return i===l||r===s||i+a/2===l+u/2},Ut=function(e,t){var o;return He.some(function(i){var r=i[Y].options.emptyInsertThreshold;if(!(!r||ot(i))){var a=C(i),l=e>=a.left-r&&e<=a.right+r,s=t>=a.top-r&&t<=a.bottom+r;if(l&&s)return o=i}}),o},Tt=function(e){function t(r,a){return function(l,s,u,d){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(a||c))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return t(r(l,s,u,d),a)(l,s,u,d);var m=(a?l:s).options.group.name;return r===!0||typeof r=="string"&&r===m||r.join&&r.indexOf(m)>-1}}var o={},i=e.group;(!i||Me(i)!="object")&&(i={name:i}),o.name=i.name,o.checkPull=t(i.pull,!0),o.checkPut=t(i.put),o.revertClone=i.revertClone,e.group=o},Ct=function(){!Dt&&g&&h(g,"display","none")},Ot=function(){!Dt&&g&&h(g,"display","")};Le&&!vt&&document.addEventListener("click",function(n){if(Be)return n.preventDefault(),n.stopPropagation&&n.stopPropagation(),n.stopImmediatePropagation&&n.stopImmediatePropagation(),Be=!1,!1},!0);var re=function(e){if(f){e=e.touches?e.touches[0]:e;var t=Ut(e.clientX,e.clientY);if(t){var o={};for(var i in e)e.hasOwnProperty(i)&&(o[i]=e[i]);o.target=o.rootEl=t,o.preventDefault=void 0,o.stopPropagation=void 0,t[Y]._onDragOver(o)}}},qt=function(e){f&&f.parentNode[Y]._isOutsideThisEl(e.target)};function p(n,e){if(!(n&&n.nodeType&&n.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(n));this.el=n,this.options=e=$({},e),n[Y]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(n.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return St(n,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:p.supportPointer!==!1&&"PointerEvent"in window&&!ye,emptyInsertThreshold:5};Oe.initializePlugins(this,n,t);for(var o in t)!(o in e)&&(e[o]=t[o]);Tt(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:zt,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?E(n,"pointerdown",this._onTapStart):(E(n,"mousedown",this._onTapStart),E(n,"touchstart",this._onTapStart)),this.nativeDraggable&&(E(n,"dragover",this),E(n,"dragenter",this)),He.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),$(this,Bt())}p.prototype={constructor:p,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(se=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,f):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,o=this.el,i=this.options,r=i.preventOnFilter,a=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,d=i.filter;if(tn(o),!f&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||i.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&ye&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=G(s,i.draggable,o,!1),!(s&&s.animated)&&Fe!==s)){if(fe=k(s),De=k(s,i.draggable),typeof d=="function"){if(d.call(this,e,s,this)){P({sortable:t,rootEl:u,name:"filter",targetEl:s,toEl:o,fromEl:o}),N("filter",t,{evt:e}),r&&e.cancelable&&e.preventDefault();return}}else if(d&&(d=d.split(",").some(function(c){if(c=G(u,c.trim(),o,!1),c)return P({sortable:t,rootEl:c,name:"filter",targetEl:s,fromEl:o,toEl:o}),N("filter",t,{evt:e}),!0}),d)){r&&e.cancelable&&e.preventDefault();return}i.handle&&!G(u,i.handle,o,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,t,o){var i=this,r=i.el,a=i.options,l=r.ownerDocument,s;if(o&&!f&&o.parentNode===r){var u=C(o);if(_=r,f=o,S=f.parentNode,ae=f.nextSibling,Fe=o,Ae=a.group,p.dragged=f,ie={target:f,clientX:(t||e).clientX,clientY:(t||e).clientY},dt=ie.clientX-u.left,ht=ie.clientY-u.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,f.style["will-change"]="all",s=function(){if(N("delayEnded",i,{evt:e}),p.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!st&&i.nativeDraggable&&(f.draggable=!0),i._triggerDragStart(e,t),P({sortable:i,name:"choose",originalEvent:e}),F(f,a.chosenClass,!0)},a.ignore.split(",").forEach(function(d){Et(f,d.trim(),Ve)}),E(l,"dragover",re),E(l,"mousemove",re),E(l,"touchmove",re),E(l,"mouseup",i._onDrop),E(l,"touchend",i._onDrop),E(l,"touchcancel",i._onDrop),st&&this.nativeDraggable&&(this.options.touchStartThreshold=4,f.draggable=!0),N("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(Ce||V))){if(p.eventCanceled){this._onDrop();return}E(l,"mouseup",i._disableDelayedDrag),E(l,"touchend",i._disableDelayedDrag),E(l,"touchcancel",i._disableDelayedDrag),E(l,"mousemove",i._delayedDragTouchMoveHandler),E(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&E(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){f&&Ve(f),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;b(e,"mouseup",this._disableDelayedDrag),b(e,"touchend",this._disableDelayedDrag),b(e,"touchcancel",this._disableDelayedDrag),b(e,"mousemove",this._delayedDragTouchMoveHandler),b(e,"touchmove",this._delayedDragTouchMoveHandler),b(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?E(document,"pointermove",this._onTouchMove):t?E(document,"touchmove",this._onTouchMove):E(document,"mousemove",this._onTouchMove):(E(f,"dragend",this),E(_,"dragstart",this._onDragStart));try{document.selection?Ye(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(ue=!1,_&&f){N("dragStarted",this,{evt:t}),this.nativeDraggable&&E(document,"dragover",qt);var o=this.options;!e&&F(f,o.dragClass,!1),F(f,o.ghostClass,!0),p.active=this,e&&this._appendGhost(),P({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(H){this._lastX=H.clientX,this._lastY=H.clientY,Ct();for(var e=document.elementFromPoint(H.clientX,H.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(H.clientX,H.clientY),e!==t);)t=e;if(f.parentNode[Y]._isOutsideThisEl(e),t)do{if(t[Y]){var o=void 0;if(o=t[Y]._onDragOver({clientX:H.clientX,clientY:H.clientY,target:e,rootEl:t}),o&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Ot()}},_onTouchMove:function(e){if(ie){var t=this.options,o=t.fallbackTolerance,i=t.fallbackOffset,r=e.touches?e.touches[0]:e,a=g&&ce(g,!0),l=g&&a&&a.a,s=g&&a&&a.d,u=Ne&&I&&ct(I),d=(r.clientX-ie.clientX+i.x)/(l||1)+(u?u[0]-$e[0]:0)/(l||1),c=(r.clientY-ie.clientY+i.y)/(s||1)+(u?u[1]-$e[1]:0)/(s||1);if(!p.active&&!ue){if(o&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(g){a?(a.e+=d-(Ue||0),a.f+=c-(qe||0)):a={a:1,b:0,c:0,d:1,e:d,f:c};var m="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",m),h(g,"mozTransform",m),h(g,"msTransform",m),h(g,"transform",m),Ue=d,qe=c,H=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!g){var e=this.options.fallbackOnBody?document.body:_,t=C(f,!0,Ne,!0,e),o=this.options;if(Ne){for(I=e;h(I,"position")==="static"&&h(I,"transform")==="none"&&I!==document;)I=I.parentNode;I!==document.body&&I!==document.documentElement?(I===document&&(I=L()),t.top+=I.scrollTop,t.left+=I.scrollLeft):I=L(),$e=ct(I)}g=f.cloneNode(!0),F(g,o.ghostClass,!1),F(g,o.fallbackClass,!0),F(g,o.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",t.top),h(g,"left",t.left),h(g,"width",t.width),h(g,"height",t.height),h(g,"opacity","0.8"),h(g,"position",Ne?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),p.ghost=g,e.appendChild(g),h(g,"transform-origin",dt/parseInt(g.style.width)*100+"% "+ht/parseInt(g.style.height)*100+"%")}},_onDragStart:function(e,t){var o=this,i=e.dataTransfer,r=o.options;if(N("dragStart",this,{evt:e}),p.eventCanceled){this._onDrop();return}N("setupClone",this),p.eventCanceled||(D=_t(f),D.removeAttribute("id"),D.draggable=!1,D.style["will-change"]="",this._hideClone(),F(D,this.options.chosenClass,!1),p.clone=D),o.cloneId=Ye(function(){N("clone",o),!p.eventCanceled&&(o.options.removeCloneOnHide||_.insertBefore(D,f),o._hideClone(),P({sortable:o,name:"clone"}))}),!t&&F(f,r.dragClass,!0),t?(Be=!0,o._loopId=setInterval(o._emulateDragOver,50)):(b(document,"mouseup",o._onDrop),b(document,"touchend",o._onDrop),b(document,"touchcancel",o._onDrop),i&&(i.effectAllowed="move",r.setData&&r.setData.call(o,i,f)),E(document,"drop",o),h(f,"transform","translateZ(0)")),ue=!0,o._dragStartId=Ye(o._dragStarted.bind(o,t,e)),E(document,"selectstart",o),be=!0,ye&&h(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,o=e.target,i,r,a,l=this.options,s=l.group,u=p.active,d=Ae===s,c=l.sort,m=O||u,y,v=this,w=!1;if(Je)return;function R(ve,At){N(ve,v,z({evt:e,isOwner:d,axis:y?"vertical":"horizontal",revert:a,dragRect:i,targetRect:r,canSort:c,fromSortable:m,target:o,completed:A,onMove:function(at,Pt){return xe(_,t,f,i,at,C(at),e,Pt)},changed:B},At))}function j(){R("dragOverAnimationCapture"),v.captureAnimationState(),v!==m&&m.captureAnimationState()}function A(ve){return R("dragOverCompleted",{insertion:ve}),ve&&(d?u._hideClone():u._showClone(v),v!==m&&(F(f,O?O.options.ghostClass:u.options.ghostClass,!1),F(f,l.ghostClass,!0)),O!==v&&v!==p.active?O=v:v===p.active&&O&&(O=null),m===v&&(v._ignoreWhileAnimating=o),v.animateAll(function(){R("dragOverAnimationComplete"),v._ignoreWhileAnimating=null}),v!==m&&(m.animateAll(),m._ignoreWhileAnimating=null)),(o===f&&!f.animated||o===t&&!o.animated)&&(se=null),!l.dragoverBubble&&!e.rootEl&&o!==document&&(f.parentNode[Y]._isOutsideThisEl(e.target),!ve&&re(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),w=!0}function B(){X=k(f),Q=k(f,l.draggable),P({sortable:v,name:"change",toEl:t,newIndex:X,newDraggableIndex:Q,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=G(o,l.draggable,t,!0),R("dragOver"),p.eventCanceled)return w;if(f.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||v._ignoreWhileAnimating===o)return A(!1);if(Be=!1,u&&!l.disabled&&(d?c||(a=S!==_):O===this||(this.lastPutMode=Ae.checkPull(this,u,f,e))&&s.checkPut(this,u,f,e))){if(y=this._getDirection(e,o)==="vertical",i=C(f),R("dragOverValid"),p.eventCanceled)return w;if(a)return S=_,j(),this._hideClone(),R("revert"),p.eventCanceled||(ae?_.insertBefore(f,ae):_.appendChild(f)),A(!0);var x=ot(t,l.draggable);if(!x||Zt(e,y,this)&&!x.animated){if(x===f)return A(!1);if(x&&t===e.target&&(o=x),o&&(r=C(o)),xe(_,t,f,i,o,r,e,!!o)!==!1)return j(),x&&x.nextSibling?t.insertBefore(f,x.nextSibling):t.appendChild(f),S=t,B(),A(!0)}else if(x&&Kt(e,y,this)){var te=de(t,0,l,!0);if(te===f)return A(!1);if(o=te,r=C(o),xe(_,t,f,i,o,r,e,!1)!==!1)return j(),t.insertBefore(f,te),S=t,B(),A(!0)}else if(o.parentNode===t){r=C(o);var W=0,ne,he=f.parentNode!==t,M=!jt(f.animated&&f.toRect||i,o.animated&&o.toRect||r,y),pe=y?"top":"left",K=ft(o,"top","top")||ft(f,"top","top"),ge=K?K.scrollTop:void 0;se!==o&&(ne=r[pe],Te=!1,Pe=!M&&l.invertSwap||he),W=Qt(e,o,r,y,M?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Pe,se===o);var U;if(W!==0){var oe=k(f);do oe-=W,U=S.children[oe];while(U&&(h(U,"display")==="none"||U===g))}if(W===0||U===o)return A(!1);se=o,Se=W;var me=o.nextElementSibling,Z=!1;Z=W===1;var Ie=xe(_,t,f,i,o,r,e,Z);if(Ie!==!1)return(Ie===1||Ie===-1)&&(Z=Ie===1),Je=!0,setTimeout(Vt,30),j(),Z&&!me?t.appendChild(f):o.parentNode.insertBefore(f,Z?me:o),K&&yt(K,0,ge-K.scrollTop),S=f.parentNode,ne!==void 0&&!Pe&&(Xe=Math.abs(ne-C(o)[pe])),B(),A(!0)}if(t.contains(f))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){b(document,"mousemove",this._onTouchMove),b(document,"touchmove",this._onTouchMove),b(document,"pointermove",this._onTouchMove),b(document,"dragover",re),b(document,"mousemove",re),b(document,"touchmove",re)},_offUpEvents:function(){var e=this.el.ownerDocument;b(e,"mouseup",this._onDrop),b(e,"touchend",this._onDrop),b(e,"pointerup",this._onDrop),b(e,"touchcancel",this._onDrop),b(document,"selectstart",this)},_onDrop:function(e){var t=this.el,o=this.options;if(X=k(f),Q=k(f,o.draggable),N("drop",this,{evt:e}),S=f&&f.parentNode,X=k(f),Q=k(f,o.draggable),p.eventCanceled){this._nulling();return}ue=!1,Pe=!1,Te=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),et(this.cloneId),et(this._dragStartId),this.nativeDraggable&&(b(document,"drop",this),b(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),ye&&h(document.body,"user-select",""),h(f,"transform",""),e&&(be&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(_===S||O&&O.lastPutMode!=="clone")&&D&&D.parentNode&&D.parentNode.removeChild(D),f&&(this.nativeDraggable&&b(f,"dragend",this),Ve(f),f.style["will-change"]="",be&&!ue&&F(f,O?O.options.ghostClass:this.options.ghostClass,!1),F(f,this.options.chosenClass,!1),P({sortable:this,name:"unchoose",toEl:S,newIndex:null,newDraggableIndex:null,originalEvent:e}),_!==S?(X>=0&&(P({rootEl:S,name:"add",toEl:S,fromEl:_,originalEvent:e}),P({sortable:this,name:"remove",toEl:S,originalEvent:e}),P({rootEl:S,name:"sort",toEl:S,fromEl:_,originalEvent:e}),P({sortable:this,name:"sort",toEl:S,originalEvent:e})),O&&O.save()):X!==fe&&X>=0&&(P({sortable:this,name:"update",toEl:S,originalEvent:e}),P({sortable:this,name:"sort",toEl:S,originalEvent:e})),p.active&&((X==null||X===-1)&&(X=fe,Q=De),P({sortable:this,name:"end",toEl:S,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){N("nulling",this),_=f=S=g=ae=D=Fe=J=ie=H=be=X=Q=fe=De=se=Se=O=Ae=p.dragged=p.ghost=p.clone=p.active=null,We.forEach(function(e){e.checked=!0}),We.length=Ue=qe=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":f&&(this._onDragOver(e),$t(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,o=this.el.children,i=0,r=o.length,a=this.options;i<r;i++)t=o[i],G(t,a.draggable,this.el,!1)&&e.push(t.getAttribute(a.dataIdAttr)||en(t));return e},sort:function(e,t){var o={},i=this.el;this.toArray().forEach(function(r,a){var l=i.children[a];G(l,this.options.draggable,i,!1)&&(o[r]=l)},this),t&&this.captureAnimationState(),e.forEach(function(r){o[r]&&(i.removeChild(o[r]),i.appendChild(o[r]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return G(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var o=this.options;if(t===void 0)return o[e];var i=Oe.modifyOption(this,e,t);typeof i<"u"?o[e]=i:o[e]=t,e==="group"&&Tt(o)},destroy:function(){N("destroy",this);var e=this.el;e[Y]=null,b(e,"mousedown",this._onTapStart),b(e,"touchstart",this._onTapStart),b(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(b(e,"dragover",this),b(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),He.splice(He.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!J){if(N("hideClone",this),p.eventCanceled)return;h(D,"display","none"),this.options.removeCloneOnHide&&D.parentNode&&D.parentNode.removeChild(D),J=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(J){if(N("showClone",this),p.eventCanceled)return;f.parentNode==_&&!this.options.group.revertClone?_.insertBefore(D,f):ae?_.insertBefore(D,ae):_.appendChild(D),this.options.group.revertClone&&this.animate(f,D),h(D,"display",""),J=!1}}};function $t(n){n.dataTransfer&&(n.dataTransfer.dropEffect="move"),n.cancelable&&n.preventDefault()}function xe(n,e,t,o,i,r,a,l){var s,u=n[Y],d=u.options.onMove,c;return window.CustomEvent&&!V&&!Ce?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=e,s.from=n,s.dragged=t,s.draggedRect=o,s.related=i||e,s.relatedRect=r||C(e),s.willInsertAfter=l,s.originalEvent=a,n.dispatchEvent(s),d&&(c=d.call(u,s,a)),c}function Ve(n){n.draggable=!1}function Vt(){Je=!1}function Kt(n,e,t){var o=C(de(t.el,0,t.options,!0)),i=10;return e?n.clientX<o.left-i||n.clientY<o.top&&n.clientX<o.right:n.clientY<o.top-i||n.clientY<o.bottom&&n.clientX<o.left}function Zt(n,e,t){var o=C(ot(t.el,t.options.draggable)),i=10;return e?n.clientX>o.right+i||n.clientX<=o.right&&n.clientY>o.bottom&&n.clientX>=o.left:n.clientX>o.right&&n.clientY>o.top||n.clientX<=o.right&&n.clientY>o.bottom+i}function Qt(n,e,t,o,i,r,a,l){var s=o?n.clientY:n.clientX,u=o?t.height:t.width,d=o?t.top:t.left,c=o?t.bottom:t.right,m=!1;if(!a){if(l&&Xe<u*i){if(!Te&&(Se===1?s>d+u*r/2:s<c-u*r/2)&&(Te=!0),Te)m=!0;else if(Se===1?s<d+Xe:s>c-Xe)return-Se}else if(s>d+u*(1-i)/2&&s<c-u*(1-i)/2)return Jt(e)}return m=m||a,m&&(s<d+u*r/2||s>c-u*r/2)?s>d+u/2?1:-1:0}function Jt(n){return k(f)<k(n)?1:-1}function en(n){for(var e=n.tagName+n.className+n.src+n.href+n.textContent,t=e.length,o=0;t--;)o+=e.charCodeAt(t);return o.toString(36)}function tn(n){We.length=0;for(var e=n.getElementsByTagName("input"),t=e.length;t--;){var o=e[t];o.checked&&We.push(o)}}function Ye(n){return setTimeout(n,0)}function et(n){return clearTimeout(n)}Le&&E(document,"touchmove",function(n){(p.active||ue)&&n.cancelable&&n.preventDefault()});p.utils={on:E,off:b,css:h,find:Et,is:function(e,t){return!!G(e,t,e,!1)},extend:kt,throttle:wt,closest:G,toggleClass:F,clone:_t,index:k,nextTick:Ye,cancelNextTick:et,detectDirection:St,getChild:de};p.get=function(n){return n[Y]};p.mount=function(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(p.utils=z(z({},p.utils),o.utils)),Oe.mount(o)})};p.create=function(n,e){return new p(n,e)};p.version=Ft;var T=[],Ee,tt,nt=!1,Ke,Ze,Ge,we;function nn(){function n(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return n.prototype={dragStarted:function(t){var o=t.originalEvent;this.sortable.nativeDraggable?E(document,"dragover",this._handleAutoScroll):this.options.supportPointer?E(document,"pointermove",this._handleFallbackAutoScroll):o.touches?E(document,"touchmove",this._handleFallbackAutoScroll):E(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var o=t.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):(b(document,"pointermove",this._handleFallbackAutoScroll),b(document,"touchmove",this._handleFallbackAutoScroll),b(document,"mousemove",this._handleFallbackAutoScroll)),gt(),ke(),Rt()},nulling:function(){Ge=tt=Ee=nt=we=Ke=Ze=null,T.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,o){var i=this,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=document.elementFromPoint(r,a);if(Ge=t,o||this.options.forceAutoScrollFallback||Ce||V||ye){Qe(t,this.options,l,o);var s=ee(l,!0);nt&&(!we||r!==Ke||a!==Ze)&&(we&&gt(),we=setInterval(function(){var u=ee(document.elementFromPoint(r,a),!0);u!==s&&(s=u,ke()),Qe(t,i.options,u,o)},10),Ke=r,Ze=a)}else{if(!this.options.bubbleScroll||ee(l,!0)===L()){ke();return}Qe(t,this.options,ee(l,!1),!1)}}},$(n,{pluginName:"scroll",initializeByDefault:!0})}function ke(){T.forEach(function(n){clearInterval(n.pid)}),T=[]}function gt(){clearInterval(we)}var Qe=wt(function(n,e,t,o){if(e.scroll){var i=(n.touches?n.touches[0]:n).clientX,r=(n.touches?n.touches[0]:n).clientY,a=e.scrollSensitivity,l=e.scrollSpeed,s=L(),u=!1,d;tt!==t&&(tt=t,ke(),Ee=e.scroll,d=e.scrollFn,Ee===!0&&(Ee=ee(t,!0)));var c=0,m=Ee;do{var y=m,v=C(y),w=v.top,R=v.bottom,j=v.left,A=v.right,B=v.width,x=v.height,te=void 0,W=void 0,ne=y.scrollWidth,he=y.scrollHeight,M=h(y),pe=y.scrollLeft,K=y.scrollTop;y===s?(te=B<ne&&(M.overflowX==="auto"||M.overflowX==="scroll"||M.overflowX==="visible"),W=x<he&&(M.overflowY==="auto"||M.overflowY==="scroll"||M.overflowY==="visible")):(te=B<ne&&(M.overflowX==="auto"||M.overflowX==="scroll"),W=x<he&&(M.overflowY==="auto"||M.overflowY==="scroll"));var ge=te&&(Math.abs(A-i)<=a&&pe+B<ne)-(Math.abs(j-i)<=a&&!!pe),U=W&&(Math.abs(R-r)<=a&&K+x<he)-(Math.abs(w-r)<=a&&!!K);if(!T[c])for(var oe=0;oe<=c;oe++)T[oe]||(T[oe]={});(T[c].vx!=ge||T[c].vy!=U||T[c].el!==y)&&(T[c].el=y,T[c].vx=ge,T[c].vy=U,clearInterval(T[c].pid),(ge!=0||U!=0)&&(u=!0,T[c].pid=setInterval(function(){o&&this.layer===0&&p.active._onTouchMove(Ge);var me=T[this.layer].vy?T[this.layer].vy*l:0,Z=T[this.layer].vx?T[this.layer].vx*l:0;typeof d=="function"&&d.call(p.dragged.parentNode[Y],Z,me,n,Ge,T[this.layer].el)!=="continue"||yt(T[this.layer].el,Z,me)}.bind({layer:c}),24))),c++}while(e.bubbleScroll&&m!==s&&(m=ee(m,!1)));nt=u}},30),It=function(e){var t=e.originalEvent,o=e.putSortable,i=e.dragEl,r=e.activeSortable,a=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(t){var u=o||r;l();var d=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,c=document.elementFromPoint(d.clientX,d.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:i,putSortable:o}))}};function it(){}it.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var i=de(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(t,i):this.sortable.el.appendChild(t),this.sortable.animateAll(),o&&o.animateAll()},drop:It};$(it,{pluginName:"revertOnSpill"});function rt(){}rt.prototype={onSpill:function(e){var t=e.dragEl,o=e.putSortable,i=o||this.sortable;i.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),i.animateAll()},drop:It};$(rt,{pluginName:"removeOnSpill"});p.mount(new nn);p.mount(rt,it);export{p as S};
