import{d as ne,G as oe,j as le,r as y,l as ce,bZ as P,aN as re,b0 as de,h as o,s as M,w as l,e as i,a as r,u as m,c as h,B as ue,W as Q,F as K,t as T,C as L,y as ie,K as me,bG as _e,cZ as he,U as pe,O as fe,A as ve,f as xe,cY as ye}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  */import"./el-menu-item-4ed993c7.js";import{_ as ge}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";/* empty css                 *//* empty css                        */import{_ as ke}from"./icon-addon-339e16d0.js";import{_ as we}from"./menu-item.vue_vue_type_style_index_0_lang-de936ad8.js";import{g as be,a as Ee}from"./site-0a26f9e1.js";const Me={class:"w-[64px] bg-[#282c34] h-screen one-menu"},Ce={key:0,class:"logo flex items-center m-auto h-[64px]"},Se=r("div",{class:"flex justify-center items-center w-full h-[40px]"},[r("img",{class:"max-w-[40px]",src:ke,alt:"","object-fit":"contain"})],-1),je={key:1,class:"logo flex items-center justify-center h-[64px]"},Re=r("i",{class:"text-3xl iconfont iconyunkongjian"},null,-1),Be=[Re],Ke={key:0,class:"w-[16px] h-[16px] relative flex justify-center"},Le={class:"relative"},Ne={class:"ml-[10px] text-[15px]"},Ae=r("div",{class:"h-[48px]"},null,-1),Ve={class:"w-[190px] h-[64px] flex items-center justify-center text-[16px] border-0 border-b-[1px] border-solid border-[#eee]"},De=r("div",{class:"h-[48px]"},null,-1),Qe=ne({__name:"side",setup(Fe){const C=oe(),p=le(),a=ie(),X=xe(),t=p.siteInfo,I=p.routers,ee=p.addonIndexRoute,f=y([]),c=y([]),g={},ae=ce(()=>p.siteInfo.icon?p.siteInfo.icon:C.website.icon);if(I.forEach(e=>{e.original_name=e.name,e.meta.addon==""?e.meta.attr==""&&(e.children&&e.children.length&&(e.name=P(e.children)),f.value.push(e)):e.meta.addon!=""&&(t==null?void 0:t.apps.length)<=1&&(t==null?void 0:t.apps[0].key)==e.meta.addon&&e.meta.show?e.children?(e.children.forEach(s=>{s.original_name=s.name,s.children&&s.children.length&&(s.name=P(s.children))}),f.value.unshift(...e.children)):f.value.unshift(e):g[e.meta.addon]=e}),(t==null?void 0:t.apps.length)>1){const e=[];t==null||t.apps.forEach(s=>{g[s.key]&&(g[s.key].name=ee[s.key],e.push(g[s.key]))}),f.value.unshift(...e)}const d=y(a.matched[1].name),S=y(null),N=y(null),te=async()=>{const e=await be();S.value=e.data},se=async()=>{const e=await Ee();N.value=e.data};return re(async()=>{await te(),await se()}),de(()=>{var w,v,x,b,E,n,_,B,A,V,D,F,U,$,G,Z,q,H,O,W,Y,z,J;const e=((x=(v=(w=S.value)==null?void 0:w.addon)==null?void 0:v.list)==null?void 0:x.map(u=>u.key))??[],s=((n=(E=(b=S.value)==null?void 0:b.tool)==null?void 0:E.list)==null?void 0:n.map(u=>u.key))??[],j=[...e,...s],R=((A=(B=(_=N.value)==null?void 0:_.marketing)==null?void 0:B.list)==null?void 0:A.map(u=>u.key))??[],k=(V=a.matched[1])==null?void 0:V.name;if(j.includes(k))d.value="addon",c.value=((D=a.matched[1])==null?void 0:D.children)??[];else if(R.includes(k))d.value="active",c.value=((F=a.matched[1])==null?void 0:F.children)??[];else if(a.meta.attr!=="")d.value=(U=a.matched[2])==null?void 0:U.name,c.value=(($=a.matched[1])==null?void 0:$.children)??[];else if((t==null?void 0:t.apps.length)>1)c.value=(G=a.matched[1])==null?void 0:G.children,d.value=(Z=a.matched[1])==null?void 0:Z.name;else{const u=a.matched[1];u.meta.addon===""?(d.value=(q=a.matched[1])==null?void 0:q.name,c.value=((H=a.matched[1])==null?void 0:H.children)??[]):u.meta.addon===((O=t==null?void 0:t.apps[0])==null?void 0:O.key)?(d.value=(W=a.matched[2])==null?void 0:W.name,c.value=((Y=a.matched[2])==null?void 0:Y.children)??[]):(d.value=(z=a.matched[1])==null?void 0:z.name,c.value=((J=a.matched[1])==null?void 0:J.children)??[])}}),(e,s)=>{const j=me,R=_e,k=ge,w=ye,v=he,x=pe,b=fe,E=ve;return o(),M(E,{class:"w-100 h-screen"},{default:l(()=>[i(b,{class:"p-0 flex"},{default:l(()=>[r("div",Me,[i(R,{class:"logo-wrap"},{default:l(()=>[m(C).menuIsCollapse?(o(),h("div",je,Be)):(o(),h("div",Ce,[i(j,{style:{width:"40px",height:"40px"},src:m(ue)(m(ae)),fit:"contain"},{error:l(()=>[Se]),_:1},8,["src"])]))]),_:1}),i(x,{class:"h-[calc( 100vh - 64px )]"},{default:l(()=>[i(v,{"default-active":d.value,router:!0,class:"aside-menu","unique-opened":!0},{default:l(()=>[(o(!0),h(K,null,Q(f.value,(n,_)=>(o(),h(K,{key:_},[n.meta.show?(o(),M(w,{key:0,index:n.original_name,onClick:B=>m(X).push({name:n.name})},{title:l(()=>[r("div",Le,[r("span",Ne,T(n.meta.short_title||n.meta.title),1)])]),default:l(()=>[n.meta.icon?(o(),h("div",Ke,[i(k,{name:n.meta.icon,class:"absolute top-[50%] -translate-y-[50%]"},null,8,["name"])])):L("",!0)]),_:2},1032,["index","onClick"])):L("",!0)],64))),128))]),_:1},8,["default-active"]),Ae]),_:1})]),c.value.length?(o(),M(x,{key:0,class:"two-menu w-[190px]"},{default:l(()=>[r("div",Ve,T(m(a).matched[1].meta.title),1),i(v,{"default-active":m(a).name,router:!0,class:"aside-menu",collapse:m(C).menuIsCollapse},{default:l(()=>[(o(!0),h(K,null,Q(c.value,(n,_)=>(o(),M(we,{routes:n,key:_},null,8,["routes"]))),128))]),_:1},8,["default-active","collapse"]),De]),_:1})):L("",!0)]),_:1})]),_:1})}}});export{Qe as _};
