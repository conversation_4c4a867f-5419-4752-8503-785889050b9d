const e="存储方式",c="阿里云存储",t="本地存储",n="七牛云存储",s="腾讯云存储",i="是否启用",o="设置",l="存储空间",a="AccessKeyID",r="AccessKeySecret",y="Endpoint",u="空间域名",d="请输入空间域名",K="请输入存储空间",S="请输入AccessKeyID",k="请输入AccessKeySecret",h="请输入Endpoint",p="存储空间与阿里云OSS开通对象名称一致",A="填写阿里云Access Key管理的(ID)。",P="Access Key Secret是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。(填写完Access Key ID 和 Access Key Secret 后请选择bucket)",E="Bucket地域endpoint",B="域名格式：http://xx.xxxx.com/（不可绑定当前网站域名，建议新开二级域名）",T="存储空间",g="Accesskey",q="Secretkey",m="请保证bucket为可公共读取的",I="请输入存储空间",D="请输入Accesskey",x="请输入Secretkey",R="存储空间",_="SECRET_ID",C="SECRET_KEY",b="REGION",f="请输入REGION",O="请保证bucket为可公共读取的",G="请输入存储空间",N="请输入SECRET_ID",U="请输入SECRET_KEY",Y={name:e,aliStorage:c,localStorage:t,qiniuStorage:n,tencentStorage:s,isUse:i,config:o,aliBucket:l,aliAccessKey:a,aliSecretKey:r,aliEndpoint:y,domain:u,domainPlaceholder:d,aliBucketPlaceholder:K,aliAccessKeyPlaceholder:S,aliSecretKeyPlaceholder:k,aliEndpointPlaceholder:h,aliBucketTips:p,aliAccessKeyTips:A,aliSecretKeyTips:P,aliEndpointTips:E,aliDomainTips:B,qiniuBucket:T,qiniuAccessKey:g,qiniuSecretKey:q,qiniuBucketTips:m,qiniuBucketPlaceholder:I,qiniuAccessKeyPlaceholder:D,qiniuSecretKeyPlaceholder:x,tencentBucket:R,tencentAccessKey:_,tencentSecretKey:C,region:b,regionPlaceholder:f,tencentBucketTips:O,tencentBucketPlaceholder:G,tencentAccessKeyPlaceholder:N,tencentSecretKeyPlaceholder:U};export{a as aliAccessKey,S as aliAccessKeyPlaceholder,A as aliAccessKeyTips,l as aliBucket,K as aliBucketPlaceholder,p as aliBucketTips,B as aliDomainTips,y as aliEndpoint,h as aliEndpointPlaceholder,E as aliEndpointTips,r as aliSecretKey,k as aliSecretKeyPlaceholder,P as aliSecretKeyTips,c as aliStorage,o as config,Y as default,u as domain,d as domainPlaceholder,i as isUse,t as localStorage,e as name,g as qiniuAccessKey,D as qiniuAccessKeyPlaceholder,T as qiniuBucket,I as qiniuBucketPlaceholder,m as qiniuBucketTips,q as qiniuSecretKey,x as qiniuSecretKeyPlaceholder,n as qiniuStorage,b as region,f as regionPlaceholder,_ as tencentAccessKey,N as tencentAccessKeyPlaceholder,R as tencentBucket,G as tencentBucketPlaceholder,O as tencentBucketTips,C as tencentSecretKey,U as tencentSecretKeyPlaceholder,s as tencentStorage};
