import{d as M,r as g,n as T,l as I,q as l,h as f,s as y,w as r,a as w,e as i,i as k,t as b,u as o,Z as S,c as j,F as O,C as v,a6 as R,I as _,aW as W,M as G,L,N as $,E as Z,V as z,a3 as A}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import H from"./index-e3ceb692.js";/* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                  *//* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";const J={class:"text-[12px] text-[#999] leading-[20px]"},K={class:"dialog-footer"},Fe=M({__name:"pay-friendspay",emits:["complete"],setup(Q,{expose:E,emit:V}){const u=g(!1),c=g(!0),m=g(null),h={type:"friendspay",app_id:"",config:{pay_explain_switch:0,pay_info_switch:1,pay_explain_title:"",pay_explain_content:"",pay_type_name:"",pay_page_name:"",pay_button_name:"",pay_leave_message:"",pay_wechat_share_image:"",pay_weapp_share_image:""},channel:"",status:0,is_default:0},e=T({...h}),x=g(),U=I(()=>({"config.pay_explain_title":[{required:!0,message:l("friendsPayTitlePlaceholder"),trigger:"blur"},{validator:(n,a,s)=>{e.config.pay_explain_switch==1&&a===""&&s(new Error(l("friendsPayTitlePlaceholder"))),s()},trigger:"blur"}],"config.pay_explain_content":[{required:!0,message:l("desContentPlaceholder"),trigger:"blur"},{validator:(n,a,s)=>{e.config.pay_explain_switch==1&&a===""&&s(new Error(l("desContentPlaceholder"))),s()},trigger:"blur"}],"config.pay_type_name":[{required:!0,message:l("friendsPayNamePlaceholder"),trigger:"blur"}],"config.pay_page_name":[{required:!0,message:l("helpNamePlaceholder"),trigger:"blur"}],"config.pay_button_name":[{required:!0,message:l("helpBtnPlaceholder"),trigger:"blur"}],"config.pay_leave_message":[{required:!0,message:l("remarkPlaceholder"),trigger:"blur"}]})),C=async n=>{c.value||!n||await n.validate(async a=>{a&&(V("complete",e),u.value=!1)})},D=()=>{Object.assign(e,h),m.value&&(Object.keys(e).forEach(n=>{m.value[n]!=null&&(e[n]=m.value[n])}),e.channel=m.value.redio_key.split("_")[0],e.status=Number(e.status)),V("complete",e),u.value=!1};return E({showDialog:u,setFormData:async(n=null)=>{m.value=R(n),c.value=!0,Object.assign(e,h),n&&(Object.keys(e).forEach(a=>{n[a]!=null&&(e[a]=n[a])}),e.channel=n.redio_key.split("_")[0],e.status=Number(e.status)),c.value=!1},enableVerify:()=>{let n=!0;return(e.config.pay_explain_switch==1&&_.empty(e.config.pay_explain_title)||e.config.pay_explain_switch==1&&_.empty(e.config.pay_explain_content)||_.empty(e.config.pay_type_name)||_.empty(e.config.pay_page_name)||_.empty(e.config.pay_button_name)||_.empty(e.config.pay_leave_message))&&(n=!1),n}}),(n,a)=>{const s=W,p=G,d=L,P=H,B=$,N=Z,F=z,q=A;return f(),y(F,{modelValue:u.value,"onUpdate:modelValue":a[12]||(a[12]=t=>u.value=t),title:e.config.pay_type_name?e.config.pay_type_name:o(l)("updateFriendsPay"),width:"550px","destroy-on-close":!0},{footer:r(()=>[w("span",K,[i(N,{onClick:a[10]||(a[10]=t=>D())},{default:r(()=>[k(b(o(l)("cancel")),1)]),_:1}),i(N,{type:"primary",loading:c.value,onClick:a[11]||(a[11]=t=>C(x.value))},{default:r(()=>[k(b(o(l)("confirm")),1)]),_:1},8,["loading"])])]),default:r(()=>[S((f(),y(B,{model:e,"label-width":"110px",ref_key:"formRef",ref:x,rules:o(U),class:"page-form"},{default:r(()=>[i(p,{label:o(l)("friendsPaySwitch")},{default:r(()=>[i(s,{modelValue:e.config.pay_explain_switch,"onUpdate:modelValue":a[0]||(a[0]=t=>e.config.pay_explain_switch=t),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1},8,["label"]),e.config.pay_explain_switch==1?(f(),j(O,{key:0},[i(p,{label:o(l)("friendsPayTitle"),prop:"config.pay_explain_title"},{default:r(()=>[i(d,{modelValue:e.config.pay_explain_title,"onUpdate:modelValue":a[1]||(a[1]=t=>e.config.pay_explain_title=t),modelModifiers:{trim:!0},placeholder:o(l)("friendsPayTitlePlaceholder"),class:"input-width",maxlength:"10","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i(p,{label:o(l)("desContent"),prop:"config.pay_explain_content"},{default:r(()=>[i(d,{modelValue:e.config.pay_explain_content,"onUpdate:modelValue":a[2]||(a[2]=t=>e.config.pay_explain_content=t),modelModifiers:{trim:!0},placeholder:o(l)("desContentPlaceholder"),class:"input-width",type:"textarea",rows:"4",maxlength:"120","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])],64)):v("",!0),i(p,{label:o(l)("friendsPayGoodsSwitch")},{default:r(()=>[w("div",null,[i(s,{modelValue:e.config.pay_info_switch,"onUpdate:modelValue":a[3]||(a[3]=t=>e.config.pay_info_switch=t),"active-value":1,"inactive-value":0},null,8,["modelValue"]),w("div",J,b(o(l)("friendsPayGoodsSwitchTips")),1)])]),_:1},8,["label"]),i(p,{label:o(l)("friendsPayName"),prop:"config.pay_type_name"},{default:r(()=>[i(d,{modelValue:e.config.pay_type_name,"onUpdate:modelValue":a[4]||(a[4]=t=>e.config.pay_type_name=t),modelModifiers:{trim:!0},placeholder:o(l)("friendsPayNamePlaceholder"),class:"input-width",maxlength:"10","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i(p,{label:o(l)("helpName"),prop:"config.pay_page_name"},{default:r(()=>[i(d,{modelValue:e.config.pay_page_name,"onUpdate:modelValue":a[5]||(a[5]=t=>e.config.pay_page_name=t),modelModifiers:{trim:!0},placeholder:o(l)("helpNamePlaceholder"),class:"input-width",maxlength:"10","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i(p,{label:o(l)("helpBtn"),prop:"config.pay_button_name"},{default:r(()=>[i(d,{modelValue:e.config.pay_button_name,"onUpdate:modelValue":a[6]||(a[6]=t=>e.config.pay_button_name=t),modelModifiers:{trim:!0},placeholder:o(l)("helpBtnPlaceholder"),class:"input-width",maxlength:"10","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),i(p,{label:o(l)("remark"),prop:"config.pay_leave_message"},{default:r(()=>[i(d,{modelValue:e.config.pay_leave_message,"onUpdate:modelValue":a[7]||(a[7]=t=>e.config.pay_leave_message=t),modelModifiers:{trim:!0},placeholder:o(l)("remarkPlaceholder"),class:"input-width",type:"textarea",rows:"4",maxlength:"20","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m.value.redio_key=="wechat_friendspay"?(f(),y(p,{key:1,label:o(l)("payWechatImage"),prop:"config.pay_wechat_share_image"},{default:r(()=>[i(P,{modelValue:e.config.pay_wechat_share_image,"onUpdate:modelValue":a[8]||(a[8]=t=>e.config.pay_wechat_share_image=t),limit:1},null,8,["modelValue"])]),_:1},8,["label"])):v("",!0),m.value.redio_key=="weapp_friendspay"?(f(),y(p,{key:2,label:o(l)("payWeappImage"),prop:"config.pay_weapp_share_image"},{default:r(()=>[i(P,{modelValue:e.config.pay_weapp_share_image,"onUpdate:modelValue":a[9]||(a[9]=t=>e.config.pay_weapp_share_image=t),limit:1},null,8,["modelValue"])]),_:1},8,["label"])):v("",!0)]),_:1},8,["model","rules"])),[[q,c.value]])]),_:1},8,["modelValue","title"])}}});export{Fe as default};
