const e="通用设置",s="普通注册方式",i="账号密码登录",o="开启之后可以使用账号+密码进行注册和登录",t="手机验证码登录",n="开启之后可以使用手机+验证码进行注册和登录或者快捷登录/注册",c="强制绑定手机",r="开启之后，会员注册时会强制绑定手机号，并且在相关页面也会引导会员强制绑定手机账号，否则将影响功能正常使用，方便会员在不同端口统一账号，也方便商家进行管理，已注册会员不受影响",l="政策协议",g="注册时服务协议和隐私协议是否进行展示",a="第三方设置",p="自动注册会员",m="开启之后，微信公众号、小程序等第三方平台可以自动注册会员或者快捷登录/注册会员，方便会员自动登录",d="强制获取用户信息",b="开启之后，将强制获取用户头像、昵称等信息，需要用户同意后，才能注册成功",U="普通注册方式至少需启用一种",T="界面设置",M="背景图",h="建议图片尺寸：750*669像素；图片格式：jpg、png、jpeg",A="描述",P="请输入描述",S={commonSetting:e,logonMode:s,isUsername:i,isUsernameTip:o,isMobile:t,isMobileTip:n,isBindMobile:c,isBindMobileTip:r,agreement:l,agreementTips:g,tripartiteSetting:a,isAuthRegister:p,isAuthRegisterTip:m,isForceAccessUserInfo:d,isForceAccessUserInfoTip:b,mobileOrUsernameNoEmpty:U,loginPageSet:T,bgUrl:M,bgUrlPlaceholder:h,desc:A,descPlaceholder:P};export{l as agreement,g as agreementTips,M as bgUrl,h as bgUrlPlaceholder,e as commonSetting,S as default,A as desc,P as descPlaceholder,p as isAuthRegister,m as isAuthRegisterTip,c as isBindMobile,r as isBindMobileTip,d as isForceAccessUserInfo,b as isForceAccessUserInfoTip,t as isMobile,n as isMobileTip,i as isUsername,o as isUsernameTip,T as loginPageSet,s as logonMode,U as mobileOrUsernameNoEmpty,a as tripartiteSetting};
