import{d as $,y as F,f as V,n as D,r as L,bI as R,h as b,c as U,e as t,w as n,u as o,aT as j,a as u,q as a,i as m,t as p,Z as I,s as M,bJ as q,af as H,aU as J,a9 as Z,L as A,M as G,E as K,N as O,ag as Q,ah as W,a2 as X,a3 as Y}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{y as ee,z as te}from"./delivery-ef62b210.js";const ae={class:"main-container"},le={class:"flex justify-between items-center"},oe={class:"mt-[10px]"},ne={class:"mt-[16px] flex justify-end"},Ee=$({__name:"template",setup(re){const v=F(),g=V(),y=v.meta.title,e=D({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{template_name:""}}),f=L(),s=(i=1)=>{e.loading=!0,e.page=i,ee({page:e.page,limit:e.limit,...e.searchParam}).then(l=>{e.loading=!1,e.data=l.data.data,e.total=l.data.total,q(e.page,e.limit,e.searchParam)}).catch(()=>{e.loading=!1})};s(R(e.searchParam).page);const x=()=>{g.push({path:"/shop/order/shipping/template_edit"})},T=i=>{g.push({path:"/shop/order/shipping/template_edit",query:{id:i.template_id}})},k=i=>{H.confirm(a("templateDeleteTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{te(i).then(()=>{s()}).catch(()=>{})})},w=i=>{i&&(i.resetFields(),s())};return(i,l)=>{const C=J,_=Z,E=A,h=G,d=K,P=O,c=Q,B=W,N=X,z=Y;return b(),U("div",ae,[t(_,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[t(C,{content:o(y),icon:o(j),onBack:l[0]||(l[0]=r=>i.$router.back())},null,8,["content","icon"])]),_:1}),t(_,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:n(()=>[t(_,{class:"box-card !border-none my-[10px] pt-0 table-search-wrap",shadow:"never"},{default:n(()=>[u("div",le,[t(P,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:f},{default:n(()=>[t(h,{label:o(a)("templateName"),prop:"template_name"},{default:n(()=>[t(E,{modelValue:e.searchParam.template_name,"onUpdate:modelValue":l[1]||(l[1]=r=>e.searchParam.template_name=r),modelModifiers:{trim:!0},placeholder:o(a)("templateNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(h,null,{default:n(()=>[t(d,{type:"primary",onClick:l[2]||(l[2]=r=>s())},{default:n(()=>[m(p(o(a)("search")),1)]),_:1}),t(d,{onClick:l[3]||(l[3]=r=>w(f.value))},{default:n(()=>[m(p(o(a)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),t(d,{type:"primary",onClick:x},{default:n(()=>[m(p(o(a)("addTemplate")),1)]),_:1})])]),_:1}),u("div",oe,[I((b(),M(B,{data:e.data,size:"large"},{empty:n(()=>[u("span",null,p(e.loading?"":o(a)("emptyData")),1)]),default:n(()=>[t(c,{prop:"template_name",label:o(a)("templateName"),"min-width":"120"},null,8,["label"]),t(c,{prop:"fee_type_name",label:o(a)("feeTypeName"),"min-width":"120"},null,8,["label"]),t(c,{label:o(a)("freeShipping"),"min-width":"120",align:"center"},{default:n(({row:r})=>[m(p(r.is_free_shipping?o(a)("open"):o(a)("close")),1)]),_:1},8,["label"]),t(c,{prop:"create_time",label:o(a)("createTime"),"min-width":"120"},null,8,["label"]),t(c,{label:o(a)("operation"),fixed:"right","min-width":"120",align:"right"},{default:n(({row:r})=>[t(d,{type:"primary",link:"",onClick:S=>T(r)},{default:n(()=>[m(p(o(a)("edit")),1)]),_:2},1032,["onClick"]),t(d,{type:"primary",link:"",onClick:S=>k(r.template_id)},{default:n(()=>[m(p(o(a)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[z,e.loading]]),u("div",ne,[t(N,{"current-page":e.page,"onUpdate:current-page":l[4]||(l[4]=r=>e.page=r),"page-size":e.limit,"onUpdate:page-size":l[5]||(l[5]=r=>e.limit=r),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:l[6]||(l[6]=r=>s()),onCurrentChange:s},null,8,["current-page","page-size","total"])])])]),_:1})])}}});export{Ee as default};
