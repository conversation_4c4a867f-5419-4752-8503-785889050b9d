import{d as w,r as c,l as V,R as f,h as _,s as C,w as t,e as l,u as a,q as o,a as m,t as n,c as d,ag as E,ah as B,M as D,N as O}from"./index-30109030.js";/* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                        *//* empty css               */import{_ as j}from"./_plugin-vue_export-helper-c27b6911.js";const N={class:"coupon_list"},R={key:0},T={key:1},F={key:0},I={key:1},q=w({__name:"recharge-detail-coupon",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(h,{emit:b}){const v=h,s=c({coupon_id:[],value:[]}),y=c(null),u=V({get(){return v.modelValue},set(p){b("update:modelValue",p)}});return f(()=>u.value,(p,r)=>{(!r||!Object.keys(r).length)&&Object.keys(p).length&&(s.value=u.value)},{immediate:!0}),f(()=>s.value,()=>{u.value=s.value},{deep:!0}),(p,r)=>{const i=E,g=B,k=D,x=O;return _(),C(x,{ref_key:"formRef",ref:y,"label-width":"120px",model:s.value,"label-position":"left"},{default:t(()=>[l(k,{class:"mt-[15px]",label:a(o)("coupon")},{default:t(()=>[m("div",N,[l(g,{data:s.value.value,size:"large","max-height":"600"},{default:t(()=>[l(i,{prop:"title",label:a(o)("name"),"min-width":"120"},{default:t(({row:e})=>[m("div",null,n(e.title),1)]),_:1},8,["label"]),l(i,{prop:"type_name",label:a(o)("type"),"min-width":"120"},{default:t(({row:e})=>[m("div",null,n(e.type_name),1)]),_:1},8,["label"]),l(i,{prop:"price",label:a(o)("couponPrice"),"min-width":"120"},{default:t(({row:e})=>[m("div",null,"￥"+n(e.price),1)]),_:1},8,["label"]),l(i,{label:a(o)("useThreshold"),"min-width":"130"},{default:t(({row:e})=>[e.min_condition_money=="0.00"?(_(),d("span",R,"无门槛")):(_(),d("span",T,"满"+n(e.min_condition_money)+"元可用",1))]),_:1},8,["label"]),l(i,{label:a(o)("termOfValidity"),"min-width":"210"},{default:t(({row:e})=>[e.valid_type==1?(_(),d("span",F," 领取之日起"+n(e.length||"")+" 天内有效",1)):(_(),d("span",I," 使用截止时间至"+n(e.valid_end_time||""),1))]),_:1},8,["label"]),l(i,{prop:"num",label:a(o)("num"),"min-width":"120"},{default:t(({row:e})=>[m("div",null,n(e.num),1)]),_:1},8,["label"])]),_:1},8,["data"])])]),_:1},8,["label"])]),_:1},8,["model"])}}});const U=j(q,[["__scopeId","data-v-f54b0832"]]);export{U as default};
