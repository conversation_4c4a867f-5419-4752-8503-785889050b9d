import{d as J,j as K,r as d,l as Q,q as o,I as M,h as f,s as c,w as t,a as g,e as s,i as h,t as v,u,Z as X,v as B,b1 as S,c as Y,F as ee,W as le,C as ae,L as oe,M as te,a0 as re,aL as se,aZ as ue,ag as ie,ah as ne,N as de,U as me,E as pe,V as _e,a3 as fe}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                     *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                        *//* empty css               *//* empty css                          */import ve from"./index-e3ceb692.js";import"./el-form-item-4ed993c7.js";/* empty css                 */import{o as ce}from"./site-0a26f9e1.js";import{d as ge,f as we,h as be}from"./user-045b9bcd.js";import{_ as Ve}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                  *//* empty css                  *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./sortable.esm-be94e56d.js";const he={class:"max-h-[60vh]"},ye={class:"w-full"},Ce={class:"dialog-footer"},Pe=J({__name:"user-edit",emits:["complete"],setup(xe,{expose:L,emit:$}){const y=K(),p=d(!1),m=d(!0),a=d({uid:0,username:"",password:"",head_img:"",real_name:"",confirm_password:"",create_site_limit:[],group_ids:[]}),w=d({}),I=d(),D=Q(()=>({username:[{required:!0,message:o("usernamePlaceholder"),trigger:"blur"}],password:[{required:y.userInfo&&y.userInfo.is_super_admin==!0,message:o("passwordPlaceholder"),trigger:"blur"}],real_name:[{required:!0,message:o("userRealNamePlaceholder"),trigger:"blur"}],confirm_password:[{required:y.userInfo&&y.userInfo.is_super_admin==!0,message:o("confirmPasswordPlaceholder"),trigger:"blur"},{validator:(i,e,r)=>{e!=a.value.password?r(new Error(o("confirmPasswordError"))):r()},trigger:"blur"}],create_site_limit:[{validator:(i,e,r)=>{a.value.uid&&r();let n=!0;for(let V=0;V<a.value.create_site_limit.length;V++){const _=a.value.create_site_limit[V];if(M.empty(_.num)){r(o("siteNumPlaceholder")),n=!1;break}if(_.num<1){r(o("siteNumCannotLtOne")),n=!1;break}if(M.empty(_.month)){r(o("siteMonthPlaceholder")),n=!1;break}if(_.month<0){r(o("siteMonthCannotLtOne")),n=!1;break}}n&&r()}}]}));ce().then(({data:i})=>{const e={};i.forEach(r=>{e[r.group_id]=r}),w.value=e});const R=(i=0)=>{i?ge(i).then(({data:e})=>{a.value.uid=e.uid,a.value.username=e.username,a.value.real_name=e.real_name,a.value.head_img=e.head_img,m.value=!1,p.value=!0}):(a.value={uid:0,username:"",password:"",head_img:"",real_name:"",confirm_password:"",create_site_limit:[],group_ids:[]},m.value=!1,p.value=!0)},q=i=>{let e=[];i.forEach(r=>{e.push({group_id:r,num:1,month:1})}),a.value.create_site_limit=e},F=async i=>{m.value||!i||await i.validate(async e=>{e&&(m.value=!0,(a.value.uid?we:be)(a.value).then(()=>{m.value=!1,p.value=!1,$("complete")}).catch(()=>{m.value=!1}))})},b=d(!0),x=d(!0),E=d(!0),C=d("password"),G=()=>{C.value=C.value==="password"?"text":"password"},P=d("password"),T=()=>{P.value=P.value==="password"?"text":"password"};return L({showDialog:p,setFormData:R}),(i,e)=>{const r=oe,n=te,V=ve,_=re,O=se,j=ue,U=ie,z=ne,H=de,Z=me,N=pe,A=_e,W=fe;return f(),c(A,{modelValue:p.value,"onUpdate:modelValue":e[16]||(e[16]=l=>p.value=l),title:a.value.uid?u(o)("updateUser"):u(o)("addUser"),width:"750px","destroy-on-close":!0},{footer:t(()=>[g("span",Ce,[s(N,{onClick:e[14]||(e[14]=l=>p.value=!1)},{default:t(()=>[h(v(u(o)("cancel")),1)]),_:1}),s(N,{type:"primary",loading:m.value,onClick:e[15]||(e[15]=l=>F(I.value))},{default:t(()=>[h(v(u(o)("confirm")),1)]),_:1},8,["loading"])])]),default:t(()=>[s(Z,null,{default:t(()=>[g("div",he,[X((f(),c(H,{model:a.value,"label-width":"120px",ref_key:"formRef",ref:I,rules:u(D),class:"page-form",autocomplete:"off"},{default:t(()=>[s(n,{label:u(o)("username"),prop:"username"},{default:t(()=>[s(r,{modelValue:a.value.username,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value.username=l),modelModifiers:{trim:!0},clearable:"",placeholder:u(o)("usernamePlaceholder"),class:"input-width",readonly:a.value.uid,disabled:a.value.uid,onClick:e[1]||(e[1]=l=>b.value=!1),onBlur:e[2]||(e[2]=l=>b.value=!0)},null,8,["modelValue","placeholder","readonly","disabled"])]),_:1},8,["label"]),s(n,{label:u(o)("headImg")},{default:t(()=>[s(V,{modelValue:a.value.head_img,"onUpdate:modelValue":e[3]||(e[3]=l=>a.value.head_img=l)},null,8,["modelValue"])]),_:1},8,["label"]),s(n,{label:u(o)("userRealName"),prop:"real_name"},{default:t(()=>[s(r,{modelValue:a.value.real_name,"onUpdate:modelValue":e[4]||(e[4]=l=>a.value.real_name=l),modelModifiers:{trim:!0},placeholder:u(o)("userRealNamePlaceholder"),readonly:b.value,onClick:e[5]||(e[5]=l=>b.value=!1),onBlur:e[6]||(e[6]=l=>b.value=!0),clearable:"",class:"input-width",maxlength:"10","show-word-limit":""},null,8,["modelValue","placeholder","readonly"])]),_:1},8,["label"]),s(n,{label:u(o)("password"),prop:"password"},{default:t(()=>[s(r,{modelValue:a.value.password,"onUpdate:modelValue":e[7]||(e[7]=l=>a.value.password=l),modelModifiers:{trim:!0},class:B([C.value=="text"?"":"displayPass","input-width"]),clearable:"",placeholder:u(o)("passwordPlaceholder"),readonly:x.value,onClick:e[8]||(e[8]=l=>x.value=!1),onBlur:e[9]||(e[9]=l=>x.value=!0)},{suffix:t(()=>[s(_,{onClick:G,class:"cursor-pointer"},{default:t(()=>[(f(),c(S(C.value==="password"?"Hide":"View")))]),_:1})]),_:1},8,["modelValue","class","placeholder","readonly"])]),_:1},8,["label"]),s(n,{label:u(o)("confirmPassword"),prop:"confirm_password"},{default:t(()=>[s(r,{modelValue:a.value.confirm_password,"onUpdate:modelValue":e[10]||(e[10]=l=>a.value.confirm_password=l),modelModifiers:{trim:!0},class:B([P.value=="text"?"":"displayPass","input-width"]),placeholder:u(o)("confirmPasswordPlaceholder"),clearable:"",readonly:E.value,onClick:e[11]||(e[11]=l=>E.value=!1),onBlur:e[12]||(e[12]=l=>E.value=!0)},{suffix:t(()=>[s(_,{onClick:T,class:"cursor-pointer"},{default:t(()=>[(f(),c(S(P.value==="password"?"Hide":"View")))]),_:1})]),_:1},8,["modelValue","class","placeholder","readonly"])]),_:1},8,["label"]),!a.value.uid&&Object.keys(w.value).length?(f(),c(n,{key:0,label:u(o)("userCreateSiteLimit"),prop:"create_site_limit"},{default:t(()=>[g("div",null,[g("div",null,v(u(o)("siteGroup")),1),s(j,{modelValue:a.value.group_ids,"onUpdate:modelValue":e[13]||(e[13]=l=>a.value.group_ids=l),onChange:q},{default:t(()=>[(f(!0),Y(ee,null,le(w.value,l=>(f(),c(O,{label:l.group_id},{default:t(()=>[h(v(l.group_name),1)]),_:2},1032,["label"]))),256))]),_:1},8,["modelValue"])]),g("div",ye,[g("div",null,v(u(o)("userCreateSiteLimit")),1),s(z,{data:a.value.create_site_limit,size:"large",class:"w-full"},{default:t(()=>[s(U,{label:u(o)("siteGroup"),"show-overflow-tooltip":!0},{default:t(({row:l})=>[h(v(w.value[l.group_id]?w.value[l.group_id].group_name:""),1)]),_:1},8,["label"]),s(U,{label:u(o)("createSiteNum")},{default:t(({$index:l})=>[s(r,{modelValue:a.value.create_site_limit[l].num,"onUpdate:modelValue":k=>a.value.create_site_limit[l].num=k,modelModifiers:{number:!0,trim:!0}},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),s(U,{label:u(o)("siteMonth")},{default:t(({$index:l})=>[s(r,{modelValue:a.value.create_site_limit[l].month,"onUpdate:modelValue":k=>a.value.create_site_limit[l].month=k,modelModifiers:{number:!0,trim:!0}},{append:t(()=>[h(v(u(o)("month")),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"])]),_:1},8,["data"])])]),_:1},8,["label"])):ae("",!0)]),_:1},8,["model","rules"])),[[W,m.value]])])]),_:1})]),_:1},8,["modelValue","title"])}}});const il=Ve(Pe,[["__scopeId","data-v-845cf3ed"]]);export{il as default};
