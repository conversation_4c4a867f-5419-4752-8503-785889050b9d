import{d as ie,r as D,l as ne,c8 as ue,aN as pe,b4 as me,a5 as _e,h as n,c as p,e as s,w as r,u as e,q as o,aT as ce,s as y,F as g,W as V,v as $,a as d,t as i,C as x,i as m,bO as C,Z as A,_ as H,c_ as fe,aU as be,a9 as ve,M as ge,L as ye,bH as he,a4 as Ve,a1 as xe,aL as ke,aZ as De,au as Ce,av as we,bL as Ue,N as Se,ap as Te,a0 as Pe,E as Ee,aW as Fe,aY as Ge,aq as Re,p as Me,g as Oe}from"./index-30109030.js";/* empty css                    */import{_ as Ie}from"./index.vue_vue_type_script_setup_true_lang-880b20ce.js";import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                          */import{_ as Ne}from"./index.vue_vue_type_style_index_0_lang-146159e0.js";import Be from"./index-e3ceb692.js";import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{as as Ae,at as He,au as Le}from"./goods-e07707eb.js";import{u as $e}from"./useGoodsEdit-71282c25.js";import{_ as Ke}from"./_plugin-vue_export-helper-c27b6911.js";import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";/* empty css                  *//* empty css                   */import"./attachment-bca8f41b.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                        *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./sortable.esm-be94e56d.js";import"./poster-65e59011.js";import"./diy_form-59d7f2cd.js";import"./range-77a5ce89.js";const w=M=>(Me("data-v-11711a43"),M=M(),Oe(),M),qe={class:"main-container"},Ye={class:"goods-type-name"},ze={class:"goods-type-desc"},We=w(()=>d("div",{class:"triangle"},null,-1)),je=w(()=>d("div",{class:"selected"},"✓",-1)),Ze=["onClick"],Je={class:"goods-type-name"},Qe={class:"goods-type-desc"},Xe=w(()=>d("div",{class:"triangle"},null,-1)),el=w(()=>d("div",{class:"selected"},"✓",-1)),ll={class:"flex flex-col"},tl={class:"flex flex-col mt-[10px]"},al={class:"text-[12px] text-[#999] leading-[20px]"},ol={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},dl={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},sl={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},rl={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},il={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},nl={class:"ml-[10px]"},ul={class:"ml-[10px]"},pl={class:"ml-[10px]"},ml={class:"ml-[120px] mb-[10px] text-[12px] text-[#999] leading-[20px]"},_l={class:"ml-[10px]"},cl={class:"ml-[10px]"},fl={class:"ml-[10px]"},bl={class:"ml-[10px]"},vl={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},gl={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},yl={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},hl={key:0,class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Vl={class:"el-form-item asterisk-left"},xl={class:"el-form-item__label w-[120px]"},kl={class:"spec-wrap"},Dl={class:"spec-edit-list"},Cl={class:"spec-name-wrap"},wl={class:"spec-value-wrap"},Ul=["onClick"],Sl=w(()=>d("div",{class:"box"},null,-1)),Tl={class:"add-spec"},Pl={class:"batch-operation-sku"},El={class:"sku-table"},Fl={class:"el-table--fit el-table--default el-table",style:{width:"100%"}},Gl={class:"el-table__inner-wrapper"},Rl={class:"el-table__header-wrapper"},Ml={class:"el-table__header",border:"0",cellpadding:"0",cellspacing:"0",style:{width:"100%"}},Ol={class:"el-table__cell w-[50px]"},Il={class:"cell"},Nl={key:0,class:"el-table__cell"},Bl={class:"cell"},Al={class:"el-table__cell"},Hl={class:"cell"},Ll={class:"el-table__cell"},$l={class:"cell"},Kl={class:"el-table__cell"},ql={class:"cell"},Yl={class:"el-table__cell"},zl={class:"cell"},Wl={class:"el-table__cell"},jl={class:"cell"},Zl={class:"el-table__cell"},Jl={class:"cell"},Ql={class:"el-table__cell"},Xl={class:"cell"},et={class:"el-table__body-wrapper text-[14px]"},lt={class:"el-scrollbar"},tt={class:"el-scrollbar__wrap el-scrollbar__wrap--hidden-default"},at={class:"el-scrollbar__view",style:{display:"inline-block","vertical-align":"middle"}},ot={class:"el-table__body",cellspacing:"0",cellpadding:"0",border:"0",style:{"table-layout":"fixed",width:"100%"}},dt={tabindex:"-1"},st={class:"el-table__cell w-[50px]"},rt={class:"cell"},it=["rowspan"],nt={class:"cell"},ut={class:"el-table__cell"},pt={class:"cell"},mt={class:"el-table__cell"},_t={class:"cell"},ct={class:"el-table__cell"},ft={class:"cell"},bt={class:"el-table__cell"},vt={class:"cell"},gt={class:"el-table__cell"},yt={class:"cell"},ht={class:"el-table__cell"},Vt={class:"cell"},xt={class:"el-table__cell"},kt={class:"cell"},Dt={key:0,class:"text-[12px] text-[#999] leading-[20px]"},Ct={key:1,class:"text-[12px] text-[#999] leading-[20px]"},wt={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Ut={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},St={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Tt={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Pt={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Et={class:"attr-table mt-[10px]"},Ft=w(()=>d("colgroup",null,[d("col",{width:"30%"}),d("col",{width:"40%"}),d("col",{width:"20%"}),d("col",{width:"10%"})],-1)),Gt={class:"bg-[#f7f7f7]"},Rt={class:"prompt-block"},Mt={class:"flex items-center"},Ot=w(()=>d("span",{class:"iconfont iconwenhao cursor-pointer ml-[3px] mt-[3px] !text-[14px]"},null,-1)),It={class:"attr-new"},Nt={key:0},Bt={key:1},At={key:1},Ht={key:2},Lt=["onClick"],$t={key:1},Kt={colspan:"4",class:"text-center"},qt={class:"fixed-footer-wrap"},Yt={class:"fixed-footer"},zt=ie({__name:"virtual_edit",setup(M){const K=D(),q=D(),Y=D(),z=D(),W=D(),j=D(),Z=D(),l=$e({getFormRef(){return{basicFormRef:K.value,priceStockFormRef:q.value,goodsArgumentsFormRef:Y.value,detailFormRef:z.value,skuFormRef:W.value,specValueRef:Z.value,priceStockCommonFormRef:j.value}},addApi:He,editApi:Le,formData:{goods_type:"virtual"},appendFormData:{virtual_auto_delivery:"0",virtual_receive_type:"auto",virtual_verify_type:"0",virtual_indate:"0",virtual_indate_day:"0"},getFormRules(v,a){return{virtual_indate_day:[{required:!0,message:o("virtualIndatePlaceholder"),trigger:"blur"},{trigger:"blur",validator:(U,S,u)=>{v.virtual_receive_type=="verify"&&v.virtual_verify_type==1&&S<1&&u(new Error(o("virtualIndateErrorHint"))),u()}}],virtual_indate:[{required:!0,message:o("virtualIndatePlaceholder"),trigger:"blur"},{trigger:"blur",validator:(U,S,u)=>{if(v.virtual_receive_type=="verify"&&v.virtual_verify_type==2){const O=new Date(S).getTime(),L=new Date().getTime();O<=L&&u(new Error(o("virtualIndateErrorOneHint")))}u()}}]}}}),J=D(0),P=ne(()=>l.formData.virtual_receive_type=="verify"&&J.value>0);Ae({goods_id:l.formData.goods_id}).then(v=>{const a=v.data;a&&(l.handleGoodsInit(a),l.formData.goods_id&&a.goods_info&&(J.value=a.goods_info.order_goods_count,l.formData.virtual_auto_delivery=String(a.goods_info.virtual_auto_delivery),l.formData.virtual_receive_type=a.goods_info.virtual_receive_type,l.formData.virtual_verify_type=String(a.goods_info.virtual_verify_type),a.goods_info.virtual_receive_type=="verify"&&a.goods_info.virtual_verify_type==2?l.formData.virtual_indate=ue(a.goods_info.virtual_indate):a.goods_info.virtual_receive_type=="verify"&&a.goods_info.virtual_verify_type==1&&(l.formData.virtual_indate_day=a.goods_info.virtual_indate)))});const te=()=>{l.save(v=>{if(v.virtual_receive_type=="verify"&&v.virtual_verify_type==2){const a=new Date(v.virtual_indate);v.virtual_indate=Math.floor(a.getTime()/1e3)}v.virtual_receive_type=="verify"&&v.virtual_verify_type==1&&(v.virtual_indate=v.virtual_indate_day),v.virtual_receive_type=="verify"&&v.virtual_verify_type==0&&(v.virtual_indate=0)})},ae=v=>v.valueOf()<Date.now();return pe(()=>{me(()=>{document.addEventListener("click",v=>{const a=v.target.closest(".el-cascader-node__label");if(a){const U=a.parentNode.querySelector(".el-checkbox");U&&U.click()}})})}),(v,a)=>{const U=be,S=ve,u=ge,c=ye,O=Be,Q=Ne,L=he,E=Ve,F=xe,G=ke,I=De,h=Ce,k=we,oe=Ue,T=Se,N=Te,X=_e("CircleCloseFilled"),ee=Pe,R=Ee,le=Fe,de=Ge,se=Ie,re=Re;return n(),p("div",qe,[s(S,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[s(U,{content:e(l).formData.goods_id?e(o)("updateGoods"):e(o)("addGoods"),icon:e(ce),onBack:a[0]||(a[0]=t=>e(l).back())},null,8,["content","icon"])]),_:1}),s(S,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:r(()=>[s(re,{modelValue:e(l).activeName,"onUpdate:modelValue":a[69]||(a[69]=t=>e(l).activeName=t),onTabClick:e(l).tabHandleClick},{default:r(()=>[s(N,{label:e(o)("basicInfoTab"),name:"basic"},{default:r(()=>[s(T,{model:e(l).formData,"label-width":"120px",ref_key:"basicFormRef",ref:K,rules:e(l).formRules,class:"page-form"},{default:r(()=>[e(l).formData.goods_id?(n(),y(u,{key:0,label:e(o)("goodsType")},{default:r(()=>[(n(!0),p(g,null,V(e(l).goodsType,t=>(n(),p("div",{key:t.type,class:$(["goods-type-wrap",[e(l).formData.goods_type==t.type?"selected":"disabled"]])},[d("div",Ye,i(t.name),1),d("div",ze,"("+i(t.desc)+")",1),e(l).formData.goods_type==t.type?(n(),p(g,{key:0},[We,je],64)):x("",!0)],2))),128))]),_:1},8,["label"])):(n(),y(u,{key:1,label:e(o)("goodsType")},{default:r(()=>[(n(!0),p(g,null,V(e(l).goodsType,t=>(n(),p("div",{class:$(["goods-type-wrap",{selected:e(l).formData.goods_type==t.type}]),key:t.type,onClick:b=>e(l).changeGoodsType(t)},[d("div",Je,i(t.name),1),d("div",Qe,"("+i(t.desc)+")",1),e(l).formData.goods_type==t.type?(n(),p(g,{key:0},[Xe,el],64)):x("",!0)],10,Ze))),128))]),_:1},8,["label"])),s(u,{label:e(o)("goodsName"),prop:"goods_name"},{default:r(()=>[s(c,{modelValue:e(l).formData.goods_name,"onUpdate:modelValue":a[1]||(a[1]=t=>e(l).formData.goods_name=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("goodsNamePlaceholder"),class:"input-width",maxlength:"60","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(u,{label:e(o)("subTitle"),prop:"sub_title"},{default:r(()=>[s(c,{modelValue:e(l).formData.sub_title,"onUpdate:modelValue":a[2]||(a[2]=t=>e(l).formData.sub_title=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("subTitlePlaceholder"),class:"input-width",maxlength:"30","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(u,{label:e(o)("goodsImage"),prop:"goods_image"},{default:r(()=>[s(O,{modelValue:e(l).formData.goods_image,"onUpdate:modelValue":a[3]||(a[3]=t=>e(l).formData.goods_image=t),limit:10},null,8,["modelValue"])]),_:1},8,["label"]),s(u,{label:e(o)("goodsVideo")},{default:r(()=>[d("div",ll,[s(Q,{modelValue:e(l).formData.goods_video,"onUpdate:modelValue":a[4]||(a[4]=t=>e(l).formData.goods_video=t),limit:1},null,8,["modelValue"]),s(c,{modelValue:e(l).formData.goods_video,"onUpdate:modelValue":a[5]||(a[5]=t=>e(l).formData.goods_video=t),clearable:"",placeholder:e(o)("在此输入外链视频地址"),class:"input-width mt-[10px]"},null,8,["modelValue","placeholder"]),d("div",tl,[d("div",al,i(e(o)("goodsVideoTipTile")),1),d("div",ol,i(e(o)("goodsVideoTipOne")),1),d("div",dl,i(e(o)("goodsVideoTipTwo")),1),d("div",sl,i(e(o)("goodsVideoTipThree")),1),d("div",rl,i(e(o)("goodsVideoTipFour")),1),d("div",il,i(e(o)("goodsVideoTipFive")),1)])])]),_:1},8,["label"]),s(u,{label:e(o)("goodsCategory"),prop:"goods_category"},{default:r(()=>[s(L,{modelValue:e(l).formData.goods_category,"onUpdate:modelValue":a[6]||(a[6]=t=>e(l).formData.goods_category=t),options:e(l).goodsCategoryOptions,props:e(l).goodsCategoryProps,clearable:"",filterable:"",onChange:e(l).categoryHandleChange},null,8,["modelValue","options","props","onChange"]),d("div",nl,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[7]||(a[7]=t=>e(l).refreshGoodsCategory(!0))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[8]||(a[8]=(...t)=>e(l).toGoodsCategoryEvent&&e(l).toGoodsCategoryEvent(...t))},i(e(o)("addGoodsCategory")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("brand")},{default:r(()=>[s(F,{modelValue:e(l).formData.brand_id,"onUpdate:modelValue":a[9]||(a[9]=t=>e(l).formData.brand_id=t),placeholder:e(o)("brandPlaceholder"),clearable:""},{default:r(()=>[(n(!0),p(g,null,V(e(l).brandOptions,t=>(n(),y(E,{key:t.brand_id,label:t.brand_name,value:t.brand_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),d("div",ul,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[10]||(a[10]=t=>e(l).refreshGoodsBrand(!0))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[11]||(a[11]=(...t)=>e(l).toGoodsBrandEvent&&e(l).toGoodsBrandEvent(...t))},i(e(o)("addGoodsBrand")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("poster")},{default:r(()=>[s(F,{modelValue:e(l).formData.poster_id,"onUpdate:modelValue":a[12]||(a[12]=t=>e(l).formData.poster_id=t),placeholder:e(o)("posterPlaceholder"),clearable:""},{default:r(()=>[(n(!0),p(g,null,V(e(l).posterOptions,t=>(n(),y(E,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),d("div",pl,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[13]||(a[13]=t=>e(l).refreshGoodsPoster(!0))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[14]||(a[14]=(...t)=>e(l).toPosterEvent&&e(l).toPosterEvent(...t))},i(e(o)("addGoodsPoster")),1)])]),_:1},8,["label"]),d("div",ml,i(e(o)("posterTips")),1),s(u,{label:e(o)("diyForm")},{default:r(()=>[s(F,{modelValue:e(l).formData.form_id,"onUpdate:modelValue":a[15]||(a[15]=t=>e(l).formData.form_id=t),placeholder:e(o)("diyFormPlaceholder"),clearable:""},{default:r(()=>[(n(!0),p(g,null,V(e(l).diyFormOptions,t=>(n(),y(E,{key:t.form_id,label:t.page_title,value:t.form_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),d("div",_l,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[16]||(a[16]=t=>e(l).refreshDiyForm(!0))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[17]||(a[17]=(...t)=>e(l).toDiyFormEvent&&e(l).toDiyFormEvent(...t))},i(e(o)("addDiyForm")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("label")},{default:r(()=>[s(I,{modelValue:e(l).formData.label_ids,"onUpdate:modelValue":a[18]||(a[18]=t=>e(l).formData.label_ids=t)},{default:r(()=>[(n(!0),p(g,null,V(e(l).labelOptions,(t,b)=>(n(),y(G,{label:t.label_id,key:b},{default:r(()=>[m(i(t.label_name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),d("div",cl,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[19]||(a[19]=(...t)=>e(l).refreshGoodsLabel&&e(l).refreshGoodsLabel(...t))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[20]||(a[20]=(...t)=>e(l).toGoodsLabelEvent&&e(l).toGoodsLabelEvent(...t))},i(e(o)("addGoodsLabel")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("goodsService")},{default:r(()=>[s(I,{modelValue:e(l).formData.service_ids,"onUpdate:modelValue":a[21]||(a[21]=t=>e(l).formData.service_ids=t)},{default:r(()=>[(n(!0),p(g,null,V(e(l).serviceOptions,(t,b)=>(n(),y(G,{label:t.service_id,key:b},{default:r(()=>[m(i(t.service_name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),d("div",fl,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[22]||(a[22]=(...t)=>e(l).refreshGoodsService&&e(l).refreshGoodsService(...t))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[23]||(a[23]=(...t)=>e(l).toGoodsServiceEvent&&e(l).toGoodsServiceEvent(...t))},i(e(o)("addGoodsService")),1)])]),_:1},8,["label"]),e(l).formData.addon_shop_supplier&&e(l).formData.addon_shop_supplier.status==1?(n(),y(u,{key:2,label:e(o)("supplier")},{default:r(()=>[s(F,{modelValue:e(l).formData.supplier_id,"onUpdate:modelValue":a[24]||(a[24]=t=>e(l).formData.supplier_id=t),placeholder:e(o)("supplierPlaceholder"),clearable:""},{default:r(()=>[(n(!0),p(g,null,V(e(l).supplierOptions,t=>(n(),y(E,{key:t.supplier_id,label:t.supplier_name,value:t.supplier_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),d("div",bl,[d("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:a[25]||(a[25]=(...t)=>e(l).refreshSupplier&&e(l).refreshSupplier(...t))},i(e(o)("refresh")),1),d("span",{class:"cursor-pointer text-primary",onClick:a[26]||(a[26]=(...t)=>e(l).toSupplierEvent&&e(l).toSupplierEvent(...t))},i(e(o)("addSupplier")),1)])]),_:1},8,["label"])):x("",!0),s(u,{label:e(o)("status")},{default:r(()=>[s(k,{modelValue:e(l).formData.status,"onUpdate:modelValue":a[27]||(a[27]=t=>e(l).formData.status=t)},{default:r(()=>[s(h,{label:"1"},{default:r(()=>[m(i(e(o)("statusOn")),1)]),_:1}),s(h,{label:"0"},{default:r(()=>[m(i(e(o)("statusOff")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(u,{label:e(o)("isGive")},{default:r(()=>[d("div",null,[s(k,{modelValue:e(l).formData.is_gift,"onUpdate:modelValue":a[28]||(a[28]=t=>e(l).formData.is_gift=t)},{default:r(()=>[s(h,{label:1},{default:r(()=>[m(i(e(o)("yes")),1)]),_:1}),s(h,{label:0},{default:r(()=>[m(i(e(o)("no")),1)]),_:1})]),_:1},8,["modelValue"]),d("div",vl,i(e(o)("giftTips")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("unit"),prop:"unit"},{default:r(()=>[s(c,{modelValue:e(l).formData.unit,"onUpdate:modelValue":a[29]||(a[29]=t=>e(l).formData.unit=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("unitPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"6"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(u,{label:e(o)("virtualSaleNum"),prop:"virtual_sale_num"},{default:r(()=>[d("div",null,[s(c,{modelValue:e(l).formData.virtual_sale_num,"onUpdate:modelValue":a[30]||(a[30]=t=>e(l).formData.virtual_sale_num=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("virtualSaleNumPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"8",onKeyup:a[31]||(a[31]=t=>e(C)(t)),onBlur:a[32]||(a[32]=t=>e(l).formData.virtual_sale_num=t.target.value)},{append:r(()=>[m(i(e(l).formData.unit?e(l).formData.unit:"件"),1)]),_:1},8,["modelValue","placeholder"]),d("div",gl,i(e(o)("virtualSaleNumDesc")),1)])]),_:1},8,["label"]),s(u,{label:e(o)("setDeliverGoods")},{default:r(()=>[s(k,{modelValue:e(l).formData.virtual_auto_delivery,"onUpdate:modelValue":a[33]||(a[33]=t=>e(l).formData.virtual_auto_delivery=t)},{default:r(()=>[s(h,{label:"1",disabled:e(P)},{default:r(()=>[m(i(e(o)("autoDeliverGoods")),1)]),_:1},8,["disabled"]),s(h,{label:"0",disabled:e(P)},{default:r(()=>[m(i(e(o)("handDeliverGoods")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(u,{label:e(o)("setTakeGoods")},{default:r(()=>[d("div",null,[s(k,{modelValue:e(l).formData.virtual_receive_type,"onUpdate:modelValue":a[34]||(a[34]=t=>e(l).formData.virtual_receive_type=t)},{default:r(()=>[s(h,{label:"auto",disabled:e(P)},{default:r(()=>[m(i(e(o)("autoTakeGoods")),1)]),_:1},8,["disabled"]),s(h,{label:"artificial",disabled:e(P)},{default:r(()=>[m(i(e(o)("handTakeGoods")),1)]),_:1},8,["disabled"]),s(h,{label:"verify",disabled:e(P)},{default:r(()=>[m(i(e(o)("verifyTakeGoods")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),A(d("div",{class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},i(e(o)("virtualSetTips")),513),[[H,e(l).formData.virtual_receive_type=="verify"]])])]),_:1},8,["label"]),e(l).formData.virtual_receive_type=="verify"?(n(),y(u,{key:3,label:e(o)("verifyVirtualIndate")},{default:r(()=>[s(k,{modelValue:e(l).formData.virtual_verify_type,"onUpdate:modelValue":a[35]||(a[35]=t=>e(l).formData.virtual_verify_type=t)},{default:r(()=>[s(h,{label:"0"},{default:r(()=>[m(i(e(o)("verifyVirtualIndateOne")),1)]),_:1}),s(h,{label:"1"},{default:r(()=>[m(i(e(o)("verifyVirtualIndateTwo")),1)]),_:1}),s(h,{label:"2"},{default:r(()=>[m(i(e(o)("verifyVirtualIndateThree")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])):x("",!0),e(l).formData.virtual_receive_type=="verify"&&e(l).formData.virtual_verify_type==1?(n(),y(u,{key:4,label:e(o)("virtualIndate"),prop:"virtual_indate_day"},{default:r(()=>[s(c,{modelValue:e(l).formData.virtual_indate_day,"onUpdate:modelValue":a[36]||(a[36]=t=>e(l).formData.virtual_indate_day=t),modelModifiers:{trim:!0},clearable:"",class:"input-width","show-word-limit":"",maxlength:"5",onKeyup:a[37]||(a[37]=t=>e(C)(t))},{append:r(()=>[m(i(e(o)("sky")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"])):x("",!0),e(l).formData.virtual_receive_type=="verify"&&e(l).formData.virtual_verify_type==2?(n(),y(u,{key:5,label:e(o)("virtualIndate"),prop:"virtual_indate"},{default:r(()=>[d("div",null,[s(oe,{class:"input-width","value-format":"YYYY-MM-DD HH:mm:ss",modelValue:e(l).formData.virtual_indate,"onUpdate:modelValue":a[38]||(a[38]=t=>e(l).formData.virtual_indate=t),type:"datetime","disabled-date":ae},null,8,["modelValue"]),d("div",yl,i(e(o)("verifyHint")),1)])]),_:1},8,["label"])):x("",!0),s(u,{label:e(o)("sort"),prop:"sort"},{default:r(()=>[s(c,{modelValue:e(l).formData.sort,"onUpdate:modelValue":a[39]||(a[39]=t=>e(l).formData.sort=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("sortPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"8",onKeyup:a[40]||(a[40]=t=>e(C)(t))},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),s(N,{label:e(o)("priceStockTab"),name:"price_stock"},{default:r(()=>[s(T,{model:e(l).formData,"label-width":"120px",ref_key:"priceStockFormRef",ref:q,rules:e(l).formRules,class:"page-form"},{default:r(()=>[s(u,{label:e(o)("specType"),prop:"spec_type"},{default:r(()=>[d("div",null,[s(k,{modelValue:e(l).formData.spec_type,"onUpdate:modelValue":a[41]||(a[41]=t=>e(l).formData.spec_type=t)},{default:r(()=>[s(h,{label:"single",disabled:e(l).isDisabledPrice()},{default:r(()=>[m(i(e(o)("singleSpec")),1)]),_:1},8,["disabled"]),s(h,{label:"multi",disabled:e(l).isDisabledPrice()},{default:r(()=>[m(i(e(o)("multiSpec")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),e(l).isDisabledPrice()?(n(),p("div",hl,i(e(o)("participateInActiveDisableTips")),1)):x("",!0)])]),_:1},8,["label"]),e(l).formData.spec_type=="single"?(n(),p(g,{key:0},[s(u,{label:e(o)("price"),prop:"price"},{default:r(()=>[s(c,{modelValue:e(l).formData.price,"onUpdate:modelValue":a[42]||(a[42]=t=>e(l).formData.price=t),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8",disabled:e(l).isDisabledPrice()},{append:r(()=>[m(i(e(o)("yuan")),1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["label"]),s(u,{label:e(o)("marketPrice"),prop:"market_price"},{default:r(()=>[s(c,{modelValue:e(l).formData.market_price,"onUpdate:modelValue":a[43]||(a[43]=t=>e(l).formData.market_price=t),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8"},{append:r(()=>[m(i(e(o)("yuan")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(u,{label:e(o)("costPrice"),prop:"cost_price"},{default:r(()=>[s(c,{modelValue:e(l).formData.cost_price,"onUpdate:modelValue":a[44]||(a[44]=t=>e(l).formData.cost_price=t),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8"},{append:r(()=>[m(i(e(o)("yuan")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(u,{label:e(o)("goodsStock"),prop:"stock"},{default:r(()=>[s(c,{modelValue:e(l).formData.stock,"onUpdate:modelValue":a[45]||(a[45]=t=>e(l).formData.stock=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("goodsStockPlaceholder"),class:"input-width",maxlength:"8",onKeyup:a[46]||(a[46]=t=>e(C)(t)),disabled:e(l).isDisabledPrice()},{append:r(()=>[m(i(e(l).formData.unit?e(l).formData.unit:e(o)("defaultUnit")),1)]),_:1},8,["modelValue","placeholder","disabled"])]),_:1},8,["label"]),s(u,{label:e(o)("skuNo")},{default:r(()=>[s(c,{modelValue:e(l).formData.sku_no,"onUpdate:modelValue":a[47]||(a[47]=t=>e(l).formData.sku_no=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("skuNoPlaceholder"),class:"input-width",maxlength:"50",onKeyup:a[48]||(a[48]=t=>e(l).filterSpecial(t)),onBlur:a[49]||(a[49]=t=>e(l).goodsVerifyFn(t))},null,8,["modelValue","placeholder"])]),_:1},8,["label"])],64)):x("",!0)]),_:1},8,["model","rules"]),s(T,{model:e(l).goodsSkuData,"label-width":"120px",ref_key:"skuFormRef",ref:W,class:"page-form"},{default:r(()=>[A(d("div",Vl,[d("div",xl,i(e(o)("goodsSku")),1),d("div",kl,[d("div",Dl,[(n(!0),p(g,null,V(e(l).goodsSpecFormat,(t,b)=>(n(),p("div",{class:"spec-item",key:t.id},[d("div",Cl,[s(c,{modelValue:t.spec_name,"onUpdate:modelValue":_=>t.spec_name=_,modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("specNamePlaceholder"),class:"input-width",maxlength:"30"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),d("div",wl,[d("ul",{ref_for:!0,ref_key:"specValueRef",ref:Z},[(n(!0),p(g,null,V(t.values,(_,f)=>(n(),p("li",{class:$("draggable-element"+b),key:_.id},[s(c,{modelValue:_.spec_value_name,"onUpdate:modelValue":B=>_.spec_value_name=B,modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("specValueNamePlaceholder"),class:"input-width","suffix-icon":e(fe),maxlength:"30",onInput:e(l).specValueNameInputListener},null,8,["modelValue","onUpdate:modelValue","placeholder","suffix-icon","onInput"]),s(ee,{class:"icon",size:20,color:"#7b7b7b",onClick:B=>e(l).deleteSpecValue(b,f)},{default:r(()=>[s(X)]),_:2},1032,["onClick"])],2))),128))],512),d("span",{class:"text-primary text-[14px] add-spec-value",onClick:_=>e(l).addSpecValue(b)},i(e(o)("addSpecValue")),9,Ul),Sl]),s(ee,{class:"del-spec",size:20,color:"#7b7b7b",onClick:_=>e(l).deleteSpec(b)},{default:r(()=>[s(X)]),_:2},1032,["onClick"])]))),128))]),d("div",Tl,[s(R,{type:"primary",onClick:e(l).addSpec},{default:r(()=>[m(i(e(o)("addSpec")),1)]),_:1},8,["onClick"])]),A(d("div",Pl,[d("label",null,i(e(o)("batchOperationSku")),1),e(l).isDisabledPrice()?x("",!0):(n(),y(c,{key:0,modelValue:e(l).batchOperation.price,"onUpdate:modelValue":a[50]||(a[50]=t=>e(l).batchOperation.price=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("price"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"])),s(c,{modelValue:e(l).batchOperation.market_price,"onUpdate:modelValue":a[51]||(a[51]=t=>e(l).batchOperation.market_price=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("marketPrice"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"]),s(c,{modelValue:e(l).batchOperation.cost_price,"onUpdate:modelValue":a[52]||(a[52]=t=>e(l).batchOperation.cost_price=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("costPrice"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"]),e(l).isDisabledPrice()?x("",!0):(n(),y(c,{key:1,modelValue:e(l).batchOperation.stock,"onUpdate:modelValue":a[53]||(a[53]=t=>e(l).batchOperation.stock=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("stock"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"])),s(c,{modelValue:e(l).batchOperation.sku_no,"onUpdate:modelValue":a[54]||(a[54]=t=>e(l).batchOperation.sku_no=t),modelModifiers:{trim:!0},clearable:"",maxlength:"50",placeholder:e(o)("skuNo"),class:"set-input",onBlur:a[55]||(a[55]=t=>e(l).goodsVerifyFn(t))},null,8,["modelValue","placeholder"]),s(R,{type:"primary",onClick:e(l).saveBatch},{default:r(()=>[m(i(e(o)("confirm")),1)]),_:1},8,["onClick"])],512),[[H,Object.keys(e(l).goodsSkuData).length]]),A(d("div",El,[d("div",Fl,[d("div",Gl,[d("div",Rl,[d("table",Ml,[d("thead",null,[d("tr",null,[d("th",Ol,[d("div",Il,[s(G,{modelValue:e(l).formData.skuCheckAll,"onUpdate:modelValue":a[56]||(a[56]=t=>e(l).formData.skuCheckAll=t),indeterminate:e(l).formData.skuIsIndeterminate,onChange:e(l).skuHandleCheckAllChange},null,8,["modelValue","indeterminate","onChange"])])]),(n(!0),p(g,null,V(e(l).goodsSpecFormat,(t,b)=>(n(),p(g,{key:b},[t.spec_name?(n(),p("th",Nl,[d("div",Bl,i(t.spec_name),1)])):x("",!0)],64))),128)),d("th",Al,[d("div",Hl,i(e(o)("image")),1)]),d("th",Ll,[d("div",$l,i(e(o)("price")),1)]),d("th",Kl,[d("div",ql,i(e(o)("marketPrice")),1)]),d("th",Yl,[d("div",zl,i(e(o)("costPrice")),1)]),d("th",Wl,[d("div",jl,i(e(o)("stock")),1)]),d("th",Zl,[d("div",Jl,i(e(o)("skuNo")),1)]),d("th",Ql,[d("div",Xl,i(e(o)("defaultSku")),1)])])])])]),s(I,{modelValue:e(l).formData.skuCheckedCities,"onUpdate:modelValue":a[58]||(a[58]=t=>e(l).formData.skuCheckedCities=t),onChange:e(l).handleCheckedCitiesChange},{default:r(()=>[d("div",et,[d("div",lt,[d("div",tt,[d("div",at,[d("table",ot,[d("tbody",dt,[(n(!0),p(g,null,V(e(l).goodsSkuData,(t,b,_)=>(n(),p("tr",{class:"el-table__row",key:b},[d("td",st,[d("div",rt,[(n(),y(G,{label:b,key:b},{default:r(()=>[m(i(""))]),_:2},1032,["label"]))])]),(n(!0),p(g,null,V(e(l).specData,(f,B)=>(n(),p(g,{key:B},[f.index==_?(n(),p("td",{key:0,class:"el-table__cell",rowspan:f.rowSpan},[d("div",nt,i(f.spec_value_name),1)],8,it)):x("",!0)],64))),128)),d("td",ut,[d("div",pt,[s(O,{modelValue:t.sku_image,"onUpdate:modelValue":f=>t.sku_image=f,limit:1,width:"50px",height:"50px"},null,8,["modelValue","onUpdate:modelValue"])])]),d("td",mt,[d("div",_t,[s(u,{prop:b+".price",rules:e(l).skuPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[s(c,{modelValue:t.price,"onUpdate:modelValue":f=>t.price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8",disabled:e(l).isDisabledPrice()},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])])]),d("td",ct,[d("div",ft,[s(u,{prop:b+".market_price",rules:e(l).skuMarketPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[s(c,{modelValue:t.market_price,"onUpdate:modelValue":f=>t.market_price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),d("td",bt,[d("div",vt,[s(u,{prop:b+".cost_price",rules:e(l).skuCostPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[s(c,{modelValue:t.cost_price,"onUpdate:modelValue":f=>t.cost_price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),d("td",gt,[d("div",yt,[s(u,{prop:b+".stock",rules:e(l).skuStockRules(),class:"sku-form-item-wrap"},{default:r(()=>[s(c,{modelValue:t.stock,"onUpdate:modelValue":f=>t.stock=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0",onInput:e(l).specStockSum,maxlength:"8",disabled:e(l).isDisabledPrice()},null,8,["modelValue","onUpdate:modelValue","onInput","disabled"])]),_:2},1032,["prop","rules"])])]),d("td",ht,[d("div",Vt,[s(c,{modelValue:t.sku_no,"onUpdate:modelValue":f=>t.sku_no=f,modelModifiers:{trim:!0},clearable:"",maxlength:"50",onBlur:a[57]||(a[57]=f=>e(l).goodsVerifyFn(f))},null,8,["modelValue","onUpdate:modelValue"])])]),d("td",xt,[d("div",kt,[s(le,{modelValue:t.is_default,"onUpdate:modelValue":f=>t.is_default=f,"active-value":1,"inactive-value":0,onChange:f=>e(l).specValueIsDefaultChangeListener(f,b)},null,8,["modelValue","onUpdate:modelValue","onChange"])])])]))),128))])])])])])])]),_:1},8,["modelValue","onChange"])])])],512),[[H,Object.keys(e(l).goodsSkuData).length]])])],512),[[H,e(l).formData.spec_type=="multi"]]),s(u,{label:e(o)("memberDiscount")},{default:r(()=>[d("div",null,[s(k,{modelValue:e(l).formData.member_discount,"onUpdate:modelValue":a[59]||(a[59]=t=>e(l).formData.member_discount=t)},{default:r(()=>[s(h,{label:""},{default:r(()=>[m(i(e(o)("nonparticipation")),1)]),_:1}),s(h,{label:"discount"},{default:r(()=>[m(i(e(o)("discount")),1)]),_:1}),s(h,{label:"fixed_price"},{default:r(()=>[m(i(e(o)("fixedPrice")),1)]),_:1})]),_:1},8,["modelValue"]),e(l).formData.member_discount=="discount"?(n(),p("div",Dt,i(e(o)("discountHint")),1)):x("",!0),e(l).formData.member_discount=="fixed_price"?(n(),p("div",Ct,i(e(o)("fixedPriceHint")),1)):x("",!0)])]),_:1},8,["label"])]),_:1},8,["model"]),s(T,{model:e(l).formData,"label-width":"120px",ref_key:"priceStockCommonFormRef",ref:j,rules:e(l).formRules,class:"page-form"},{default:r(()=>[s(u,{label:e(o)("isLimit")},{default:r(()=>[d("div",null,[s(le,{modelValue:e(l).formData.is_limit,"onUpdate:modelValue":a[60]||(a[60]=t=>e(l).formData.is_limit=t),"active-value":1,"inactive-value":0},null,8,["modelValue"]),d("div",wt,i(e(o)("isLimitTips")),1)])]),_:1},8,["label"]),e(l).formData.is_limit=="1"?(n(),y(u,{key:0,label:e(o)("limitType"),prop:"limit_type"},{default:r(()=>[d("div",null,[s(k,{modelValue:e(l).formData.limit_type,"onUpdate:modelValue":a[61]||(a[61]=t=>e(l).formData.limit_type=t)},{default:r(()=>[s(h,{label:1},{default:r(()=>[m(i(e(o)("singleTime")),1)]),_:1}),s(h,{label:2},{default:r(()=>[m(i(e(o)("singlePerson")),1)]),_:1})]),_:1},8,["modelValue"]),d("div",Ut,i(e(o)("limitTypeTips")),1)])]),_:1},8,["label"])):x("",!0),e(l).formData.is_limit=="1"?(n(),y(u,{key:1,label:e(o)("maxBuy"),prop:"max_buy"},{default:r(()=>[d("div",null,[s(c,{modelValue:e(l).formData.max_buy,"onUpdate:modelValue":a[62]||(a[62]=t=>e(l).formData.max_buy=t),modelModifiers:{trim:!0},clearable:"",placeholder:e(o)("maxBuyPlaceholder"),class:"input-width",maxlength:"8",onKeyup:a[63]||(a[63]=t=>e(C)(t))},{append:r(()=>[m(i(e(l).formData.unit?e(l).formData.unit:e(o)("defaultUnit")),1)]),_:1},8,["modelValue","placeholder"]),d("div",St,i(e(o)("maxBuyWarnTips")),1)])]),_:1},8,["label"])):x("",!0),s(u,{label:e(o)("minBuy"),prop:"min_buy"},{default:r(()=>[d("div",null,[s(c,{modelValue:e(l).formData.min_buy,"onUpdate:modelValue":a[64]||(a[64]=t=>e(l).formData.min_buy=t),modelModifiers:{trim:!0},clearable:"",class:"input-width",maxlength:"8",onKeyup:a[65]||(a[65]=t=>e(C)(t))},{append:r(()=>[m(i(e(l).formData.unit?e(l).formData.unit:e(o)("defaultUnit")),1)]),_:1},8,["modelValue"]),d("div",Tt,i(e(o)("minBuyTips")),1)])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),s(N,{label:e(o)("goodsArguments"),name:"goods_arguments"},{default:r(()=>[s(T,{model:e(l).formData,"label-width":"120px",ref_key:"goodsArgumentsFormRef",ref:Y,rules:e(l).formRules,class:"page-form"},{default:r(()=>[s(u,{label:e(o)("goodsArgumentsTemp")},{default:r(()=>[d("div",null,[s(F,{modelValue:e(l).formData.attr_ids,"onUpdate:modelValue":a[66]||(a[66]=t=>e(l).formData.attr_ids=t),placeholder:e(o)("goodsArgumentsTempPlaceholder"),clearable:"",multiple:"",onChange:e(l).attrChange,onClear:e(l).attrChange},{default:r(()=>[(n(!0),p(g,null,V(e(l).attrOptions,t=>(n(),y(E,{key:t.attr_id,label:t.attr_name,value:t.attr_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onClear"]),d("div",Pt,i(e(o)("goodsArgumentsTempHint")),1),d("table",Et,[Ft,d("thead",Gt,[d("tr",null,[d("th",null,i(e(o)("argumentsName")),1),d("th",null,i(e(o)("argumentsValue")),1),d("th",Rt,[d("div",Mt,[d("span",null,i(e(o)("sort")),1),s(de,{class:"box-item",effect:"dark",content:e(o)("argumentsSortHint"),placement:"right"},{default:r(()=>[Ot]),_:1},8,["content"])])]),d("th",null,i(e(o)("operation")),1)])]),d("tbody",It,[e(l).attrTableData.length?(n(!0),p(g,{key:0},V(e(l).attrTableData,(t,b)=>(n(),p("tr",{class:"goods-attr-tr goods-new-attr-tr",key:b},[t.attr_value_id>0?(n(),p("td",Nt,i(t.attr_value_name),1)):(n(),p("td",Bt,[s(c,{maxlength:"30","show-word-limit":"",modelValue:t.attr_value_name,"onUpdate:modelValue":_=>t.attr_value_name=_,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),d("td",null,[t.type=="text"?(n(),y(c,{key:0,maxlength:"30","show-word-limit":"",modelValue:t.select_child_val,"onUpdate:modelValue":_=>t.select_child_val=_,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])):t.type=="radio"?(n(),p("div",At,[s(k,{modelValue:t.select_child_name,"onUpdate:modelValue":_=>t.select_child_name=_,onChange:_=>e(l).attrRadioChange(b,_)},{default:r(()=>[(n(!0),p(g,null,V(t.child,(_,f)=>(n(),y(h,{key:f,label:_.id},{default:r(()=>[m(i(_.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])):t.type=="checkbox"?(n(),p("div",Ht,[s(I,{modelValue:t.select_child_name,"onUpdate:modelValue":_=>t.select_child_name=_,onChange:_=>e(l).attrCheckboxChange(b,_)},{default:r(()=>[(n(!0),p(g,null,V(t.child,(_,f)=>(n(),y(G,{key:f,label:_.id},{default:r(()=>[m(i(_.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])):x("",!0)]),d("td",null,[s(c,{maxlength:"6","show-word-limit":"",modelValue:t.sort,"onUpdate:modelValue":_=>t.sort=_,modelModifiers:{trim:!0},clearable:"",onKeyup:a[67]||(a[67]=_=>e(C)(_))},null,8,["modelValue","onUpdate:modelValue"])]),d("td",null,[d("span",{class:"cursor-pointer text-[var(--el-color-primary)]",onClick:_=>e(l).delAttr(b)},i(e(o)("delAttr")),9,Lt)])]))),128)):(n(),p("tr",$t,[d("td",Kt,i(e(o)("noData")),1)]))])]),s(R,{class:"mt-[15px]",onClick:e(l).addAttr},{default:r(()=>[m(i(e(o)("addGoodsArguments")),1)]),_:1},8,["onClick"])])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),s(N,{label:e(o)("goodsDesc"),name:"detail"},{default:r(()=>[s(T,{model:e(l).formData,"label-width":"120px",ref_key:"detailFormRef",ref:z,rules:e(l).formRules,class:"page-form"},{default:r(()=>[s(u,{label:e(o)("goodsDesc"),prop:"goods_desc"},{default:r(()=>[s(se,{modelValue:e(l).formData.goods_desc,"onUpdate:modelValue":a[68]||(a[68]=t=>e(l).formData.goods_desc=t),height:600,class:"editor-width",onHandleBlur:e(l).handleBlur},null,8,["modelValue","onHandleBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"])]),_:1},8,["modelValue","onTabClick"])]),_:1}),d("div",qt,[d("div",Yt,[s(R,{type:"primary",onClick:a[70]||(a[70]=t=>te())},{default:r(()=>[m(i(e(o)("save")),1)]),_:1}),s(R,{onClick:a[71]||(a[71]=t=>e(l).back())},{default:r(()=>[m(i(e(o)("back")),1)]),_:1})])])])}}});const Ha=Ke(zt,[["__scopeId","data-v-11711a43"]]);export{Ha as default};
