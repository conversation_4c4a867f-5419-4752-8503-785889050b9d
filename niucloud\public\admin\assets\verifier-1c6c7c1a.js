import{d as X,y as Y,f as ee,r as f,n as T,q as a,h as d,c,e as s,w as i,a as m,t as p,u as r,i as x,Z as N,s as C,B as te,C as ae,F as E,W as w,b4 as le,af as ie,E as re,ag as oe,ah as ne,a2 as se,a9 as de,a4 as me,a1 as pe,M as ce,N as ue,V as fe,a3 as _e}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                *//* empty css                      *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                  */import{_ as ve}from"./member_head-d9fd7b2c.js";import{a as ye,b as be,d as ge,e as he,c as xe,f as ke}from"./verify-1f1231ea.js";import{p as Ve}from"./member-a1692c0d.js";const Ce={class:"main-container"},Te={class:"flex justify-between items-center"},Ee={class:"text-page-title"},we={class:"mt-[20px]"},Le=["onClick"],Be=["src"],De={key:1,class:"w-[50px] h-[50px] mr-[10px] rounded-full",src:ve},$e={class:"flex flex-col"},ze={class:"flex flex-col"},Fe={class:"mt-[16px] flex justify-end"},Me={class:"dialog-footer"},rt=X({__name:"verifier",setup(Ne){const P=Y(),R=ee(),U=P.meta.title,_=f(!1),v=f(!1),l=T({member_id:"",verify_type:""}),I=T({member_id:[{required:!0,message:a("memberIdPlaceholder"),trigger:"blur"}],verify_type:[{required:!0,message:a("verifyTypePlaceholder"),trigger:"blur"}]}),L=f(),o=T({page:1,limit:10,total:0,loading:!0,data:[]}),g=(n=1)=>{o.loading=!0,o.page=n,ye({page:o.page,limit:o.limit,...o.searchParam}).then(t=>{o.loading=!1,o.data=t.data.data,o.total=t.data.total}).catch(()=>{o.loading=!1})};g();const k=f(!1),S=()=>{k.value=!1,l.member_id="",l.id="",l.verify_type="",_.value=!0},j=async n=>{k.value=!0,l.member_id="",l.verify_type="",y.value=[];try{const t=await be(n.id);y.value=[{member_id:t.data.member.member_id,nickname:t.data.member.nickname}],le(()=>{l.member_id=t.data.member.member_id,l.verify_type=t.data.verify_type,l.id=n.id,_.value=!0})}catch{}},q=()=>{k.value&&l.member_id&&y.value.length===0&&B("")},O=n=>{ie.confirm(a("verifierDeleteTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{ge(n).then(()=>{g()})})},W=async n=>{v.value||!n||await n.validate(async t=>{t&&(v.value=!0,(l.id?he:xe)(l).then(h=>{v.value=!1,_.value=!1,l.member_id="",l.verify_type="",g()}).catch(()=>{v.value=!1}))})},y=f([]),V=f(!1),B=n=>{n?(V.value=!0,Ve({keyword:n}).then(t=>{y.value=t.data.data,V.value=!1})):(y.value=[],V.value=!1)},D=f([]);(()=>{ke().then(n=>{D.value=n.data})})();const Z=n=>{R.push(`/member/detail?id=${n}`)};return(n,t)=>{const b=re,h=oe,A=ne,G=se,H=de,$=me,z=pe,F=ce,J=ue,K=fe,M=_e;return d(),c("div",Ce,[s(H,{class:"box-card !border-none",shadow:"never"},{default:i(()=>[m("div",Te,[m("span",Ee,p(r(U)),1),s(b,{type:"primary",onClick:S},{default:i(()=>[x(p(r(a)("addVerifier")),1)]),_:1})]),m("div",we,[N((d(),C(A,{data:o.data,size:"large"},{empty:i(()=>[m("span",null,p(o.loading?"":r(a)("emptyData")),1)]),default:i(()=>[s(h,{label:r(a)("memberInfo"),"min-width":"120"},{default:i(({row:e})=>[e.member?(d(),c("div",{key:0,class:"flex items-center cursor-pointer",onClick:u=>Z(e.member.member_id)},[e.member.headimg?(d(),c("img",{key:0,class:"w-[50px] h-[50px] mr-[10px]",src:r(te)(e.member.headimg)},null,8,Be)):(d(),c("img",De)),m("div",$e,[m("span",null,p(e.member.nickname||""),1),m("span",null,p(e.member.mobile||""),1)])],8,Le)):ae("",!0)]),_:1},8,["label"]),s(h,{label:r(a)("verifyType"),"min-width":"120"},{default:i(({row:e})=>[m("div",ze,[(d(!0),c(E,null,w(e.verify_type_array,(u,Q)=>(d(),c("div",{class:"my-[3px]",key:Q},p(u.verify_type_name),1))),128))])]),_:1},8,["label"]),s(h,{label:r(a)("createTime"),prop:"create_time","min-width":"120"},null,8,["label"]),s(h,{label:r(a)("operation"),fixed:"right",align:"right",width:"120"},{default:i(({row:e})=>[s(b,{type:"primary",link:"",onClick:u=>j(e)},{default:i(()=>[x(p(r(a)("edit")),1)]),_:2},1032,["onClick"]),s(b,{type:"primary",link:"",onClick:u=>O(e.id)},{default:i(()=>[x(p(r(a)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[M,o.loading]]),m("div",Fe,[s(G,{"current-page":o.page,"onUpdate:current-page":t[0]||(t[0]=e=>o.page=e),"page-size":o.limit,"onUpdate:page-size":t[1]||(t[1]=e=>o.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:o.total,onSizeChange:t[2]||(t[2]=e=>g()),onCurrentChange:g},null,8,["current-page","page-size","total"])])])]),_:1}),s(K,{modelValue:_.value,"onUpdate:modelValue":t[7]||(t[7]=e=>_.value=e),title:l.id?r(a)("editVerifier"):r(a)("addVerifier"),width:"500px","destroy-on-close":!0},{footer:i(()=>[m("span",Me,[s(b,{onClick:t[5]||(t[5]=e=>_.value=!1)},{default:i(()=>[x(p(r(a)("cancel")),1)]),_:1}),s(b,{type:"primary",loading:v.value,onClick:t[6]||(t[6]=e=>W(L.value))},{default:i(()=>[x(p(r(a)("confirm")),1)]),_:1},8,["loading"])])]),default:i(()=>[N((d(),C(J,{model:l,"label-width":"90px",ref_key:"formRef",ref:L,rules:I,class:"page-form"},{default:i(()=>[s(F,{label:r(a)("member"),prop:"member_id"},{default:i(()=>[s(z,{modelValue:l.member_id,"onUpdate:modelValue":t[3]||(t[3]=e=>l.member_id=e),filterable:"",remote:"","reserve-keyword":"",clearable:"",onFocus:q,disabled:k.value,placeholder:r(a)("searchPlaceholder"),"remote-method":B,loading:V.value,class:"input-width"},{default:i(()=>[(d(!0),c(E,null,w(y.value,e=>(d(),C($,{key:e.member_id,label:e.nickname,value:e.member_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","loading"])]),_:1},8,["label"]),s(F,{label:r(a)("verifyType"),prop:"verify_type"},{default:i(()=>[s(z,{modelValue:l.verify_type,"onUpdate:modelValue":t[4]||(t[4]=e=>l.verify_type=e),multiple:"",clearable:"",placeholder:r(a)("verifyTypePlaceholder"),class:"input-width"},{default:i(()=>[(d(!0),c(E,null,w(D.value,(e,u)=>(d(),C($,{key:u,label:e.name,value:u},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[M,v.value]])]),_:1},8,["modelValue","title"])])}}});export{rt as default};
