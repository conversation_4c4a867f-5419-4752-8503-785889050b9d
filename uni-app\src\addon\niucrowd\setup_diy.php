<?php
/**
 * 众筹应用DIY页面模板设置脚本
 * 用于修复众筹首页装修界面灰色无法使用的问题
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use app\service\admin\diy\DiyService;
use think\facade\Db;

try {
    echo "开始设置众筹应用DIY页面模板...\n";
    
    $diy_service = new DiyService();

    // 设置众筹首页模板
    echo "设置众筹首页模板...\n";
    $result1 = $diy_service->changeTemplate([
        'type' => 'DIY_NIUCROWD_INDEX',
        'name' => 'NIUCROWD_INDEX',
        'parent' => 'NIUCROWD_LINK',
        'page' => '/addon/niucrowd/pages/index',
        'title' => '众筹首页',
        'action' => 'decorate'
    ]);
    
    if ($result1) {
        echo "✓ 众筹首页模板设置成功\n";
    } else {
        echo "✗ 众筹首页模板设置失败\n";
    }

    // 设置众筹个人中心模板
    echo "设置众筹个人中心模板...\n";
    $result2 = $diy_service->changeTemplate([
        'type' => 'DIY_NIUCROWD_MEMBER_INDEX',
        'name' => 'NIUCROWD_MEMBER_CENTER',
        'parent' => 'NIUCROWD_LINK',
        'page' => '/addon/niucrowd/pages/member/center',
        'title' => '众筹个人中心',
        'action' => 'decorate'
    ]);
    
    if ($result2) {
        echo "✓ 众筹个人中心模板设置成功\n";
    } else {
        echo "✗ 众筹个人中心模板设置失败\n";
    }

    echo "\n众筹应用DIY页面模板设置完成！\n";
    echo "现在可以正常使用众筹首页装修功能了。\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "请检查数据库连接和配置是否正确。\n";
}
