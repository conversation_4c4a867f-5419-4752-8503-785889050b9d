import{P as r}from"./index-30109030.js";function t(e){return r.get("verify/verify/record",{params:e})}function f(e){return r.get(`verify/verify/${e}`)}function s(e){return r.get("verify/verifier",{params:e})}function n(){return r.get("verify/verifier/select")}function u(){return r.get("verify/verifier/type")}function c(e){return r.post("verify/verifier",e,{showSuccessMessage:!0})}function o(e){return r.delete(`verify/verifier/${e}`,{showSuccessMessage:!0})}function v(e){return r.get(`verify/verifier/${e}`)}function y(e){return r.post(`verify/verifier/${e.id}`,e,{showSuccessMessage:!0})}export{s as a,v as b,c,o as d,y as e,u as f,f as g,t as h,n as i};
