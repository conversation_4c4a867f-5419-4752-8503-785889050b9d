import{d as B,G as C,j as R,r as F,l as K,aN as N,bZ as g,h as r,s as y,w as n,e as l,u as c,c as m,B as U,W as V,F as G,a as _,y as H,H as Z,K as q,bG as A,cZ as D,U as I,O as L,A as O}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                 *//* empty css                        */import{_ as W}from"./icon-addon-339e16d0.js";import{_ as $}from"./menu-item.vue_vue_type_style_index_0_lang-d8684af8.js";import{a as z}from"./site-0a26f9e1.js";const J={key:0,class:"logo flex items-center m-auto h-[64px]"},P=_("div",{class:"flex justify-center items-center w-full h-[40px]"},[_("img",{class:"max-w-[40px]",src:W,alt:"","object-fit":"contain"})],-1),Q={key:1,class:"logo flex items-center justify-center h-[64px]"},T=_("i",{class:"text-3xl iconfont iconyunkongjian"},null,-1),X=[T],Y=_("div",{class:"h-[48px]"},null,-1),de=B({__name:"side",setup(ee){const p=C(),o=R(),x=H(),a=o.siteInfo,k=o.routers,v=o.addonIndexRoute,t=F([]),u={},w=K(()=>o.siteInfo.icon?o.siteInfo.icon:p.website.icon),E=async()=>{var i,d;const s=(await z()).data,f=((d=(i=s==null?void 0:s.marketing)==null?void 0:i.list)==null?void 0:d.map(h=>h.key))??[];Z.set({key:"defaultMarketingKeys",data:f})};if(N(()=>{E()}),k.forEach(e=>{e.original_name=e.name,e.meta.addon==""?e.meta.attr==""&&e.name!="sign"&&e.name!="verify"&&(e.children&&e.children.length&&(e.name=g(e.children)),t.value.push(e)):e.meta.addon!=""&&(a==null?void 0:a.apps.length)==1&&(a==null?void 0:a.apps[0].key)==e.meta.addon&&e.meta.show?e.children?(e.children.forEach(s=>{s.original_name=s.name,s.children&&s.children.length&&(s.name=g(s.children))}),t.value.unshift(...e.children)):t.value.unshift(e):u[e.meta.addon]=e}),(a==null?void 0:a.apps.length)>1){const e=[];a==null||a.apps.forEach(s=>{u[s.key]&&(u[s.key].name=v[s.key],e.push(u[s.key]))}),t.value.unshift(...e)}return(e,s)=>{const f=q,i=A,d=D,h=I,S=L,b=O;return r(),y(b,{class:"w-[200px] h-screen layout-aside flex flex-col"},{default:n(()=>[l(i,{class:"logo-wrap flex items-center justify-center h-[64px]"},{default:n(()=>[c(p).menuIsCollapse?(r(),m("div",Q,X)):(r(),m("div",J,[l(f,{style:{width:"40px",height:"40px"},src:c(U)(c(w)),fit:"contain"},{error:n(()=>[P]),_:1},8,["src"])]))]),_:1}),l(S,{class:"menu-wrap"},{default:n(()=>[l(h,null,{default:n(()=>[l(d,{"default-active":c(x).name,router:!0,class:"aside-menu h-full","unique-opened":!0,collapse:c(p).menuIsCollapse},{default:n(()=>[(r(!0),m(G,null,V(t.value,(j,M)=>(r(),y($,{routes:j,key:M},null,8,["routes"]))),128))]),_:1},8,["default-active","collapse"]),Y]),_:1})]),_:1})]),_:1})}}});export{de as _};
