import{d as m,r as u,l as d,T as i,H as f,$ as g,h as _,s as h,w as y,b as k,be as b,u as v,bn as R}from"./index-30109030.js";/* empty css                  *//* empty css                    */const x=m({__name:"upload-media",props:{type:{type:String,default:"image"}},emits:["success"],setup(r,{emit:c}){const o=r,s=u(null),l=d(()=>{const e={};return e.token=i(),e["site-id"]=f.get("siteId")||0,{action:`/adminapi//wechat/media/${o.type}`,multiple:!0,headers:e,accept:o.type=="image"?".bmp,.png,.jpeg,.jpg,.gif":".mp4",onSuccess:(t,a,$)=>{var p,n;t.code>=1?(c("success",t.data),(p=s.value)==null||p.handleRemove(a)):(a.status="fail",(n=s.value)==null||n.handleRemove(a),g({message:t.msg,type:"error"}))}}});return(e,t)=>{const a=R;return _(),h(a,b(v(l),{ref_key:"uploadRef",ref:s}),{default:y(()=>[k(e.$slots,"default")]),_:3},16)}}});export{x as _};
