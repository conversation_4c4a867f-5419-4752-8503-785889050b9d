import{d as x,r as d,n as E,l as k,h as p,s as v,w as a,a as l,e as i,i as F,t as n,u as s,q as o,Z as B,M as N,N as C,E as R,V as T,a3 as j}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-4ed993c7.js";const I={class:"input-width"},O={class:"input-width"},q={class:"input-width"},K={class:"input-width"},L={class:"dialog-footer"},J=x({__name:"sms-records-info",setup(M,{expose:g}){const r=d(!1),_=d(!0),u={create_time:0,message_data:"",message_key:"",message_type:"",name:"",nickname:"",mobile:"",sms_type_name:""},e=E({...u}),b=d(),h=k(()=>({}));return g({showDialog:r,setFormData:async(m=null)=>{_.value=!0,Object.assign(e,u),m&&Object.keys(e).forEach(t=>{m[t]!=null&&(e[t]=m[t])}),_.value=!1}}),(m,t)=>{const c=N,y=C,w=R,D=T,V=j;return p(),v(D,{modelValue:r.value,"onUpdate:modelValue":t[1]||(t[1]=f=>r.value=f),title:s(o)("messageInfo"),width:"550px","destroy-on-close":!0},{footer:a(()=>[l("span",L,[i(w,{type:"primary",onClick:t[0]||(t[0]=f=>r.value=!1)},{default:a(()=>[F(n(s(o)("confirm")),1)]),_:1})])]),default:a(()=>[B((p(),v(y,{model:e,"label-width":"110px",ref_key:"formRef",ref:b,rules:s(h),class:"page-form"},{default:a(()=>[i(c,{label:s(o)("messageKey")},{default:a(()=>[l("div",I,n(e.name),1)]),_:1},8,["label"]),i(c,{label:s(o)("smsType")},{default:a(()=>[l("div",O,n(e.sms_type_name),1)]),_:1},8,["label"]),i(c,{label:s(o)("receiver")},{default:a(()=>[l("div",q,n(e.mobile),1)]),_:1},8,["label"]),i(c,{label:s(o)("createTime")},{default:a(()=>[l("div",K,n(e.create_time),1)]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[V,_.value]])]),_:1},8,["modelValue","title"])}}});export{J as _};
