import{d as C,y as k,f as x,n as w,h as g,c as N,e as i,w as l,a as r,t as m,u as o,Z as B,s as E,q as n,i as z,a6 as D,ag as L,E as T,ah as j,a2 as V,a9 as R,a3 as S}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                        */import{y as U}from"./diy-aea57979.js";const $={class:"main-container"},q={class:"flex justify-between items-center"},G={class:"text-page-title"},M={class:"mt-[16px] flex justify-end"},lt=C({__name:"tabbar",setup(P){const _=k(),b=x(),f=_.meta.title,t=w({page:1,limit:10,total:0,loading:!0,data:[]});((s=1)=>{t.loading=!0,t.page=s,U({}).then(a=>{t.loading=!1;const p=Math.ceil(a.data.length/t.limit),u=D(a.data),c=[];for(let d=0;d<p;d++)c[d]=u.splice(0,t.limit);t.data=c[t.page-1],t.total=a.data.length}).catch(()=>{t.loading=!1})})();const h=s=>{b.push("/diy/tabbar_edit?key="+s.key)};return(s,a)=>{const p=L,u=T,c=j,d=V,y=R,v=S;return g(),N("div",$,[i(y,{class:"box-card !border-none",shadow:"never"},{default:l(()=>[r("div",q,[r("span",G,m(o(f)),1)]),B((g(),E(c,{class:"mt-[20px]",data:t.data,size:"large"},{empty:l(()=>[r("span",null,m(t.loading?"":o(n)("emptyData")),1)]),default:l(()=>[i(p,{prop:"title",label:o(n)("title"),"min-width":"120"},{default:l(({row:e})=>[r("span",null,m(e.info.title),1)]),_:1},8,["label"]),i(p,{prop:"key",label:o(n)("key"),"min-width":"120"},null,8,["label"]),i(p,{label:o(n)("type"),"min-width":"120"},{default:l(({row:e})=>[r("span",null,m(e.info.type==="app"?o(n)("app"):o(n)("addon")),1)]),_:1},8,["label"]),i(p,{label:o(n)("operation"),fixed:"right",align:"right","min-width":"160"},{default:l(({row:e})=>[i(u,{type:"primary",link:"",onClick:A=>h(e)},{default:l(()=>[z(m(o(n)("edit")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[v,t.loading]]),r("div",M,[i(d,{"current-page":t.page,"onUpdate:current-page":a[0]||(a[0]=e=>t.page=e),"page-size":t.limit,"onUpdate:page-size":a[1]||(a[1]=e=>t.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:a[2]||(a[2]=e=>s.loadbottomNavList()),onCurrentChange:s.loadbottomNavList},null,8,["current-page","page-size","total","onCurrentChange"])])]),_:1})])}}});export{lt as default};
