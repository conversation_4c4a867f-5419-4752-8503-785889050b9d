import{d as de,y as ue,r as f,n as $,q as t,h as r,c as m,Z as W,s as b,w as l,e as d,a as u,t as s,u as n,bO as ce,C as h,F as Z,W as z,i as x,a6 as g,aW as _e,M as pe,L as me,ag as fe,E as ge,ah as ve,a9 as ye,N as we,V as be,a3 as he,p as xe,g as Se}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                  */import{l as ke,n as G,s as Ce}from"./member-a1692c0d.js";import Ve from"./sign-day-a83fbb0b.js";import Ee from"./sign-continue-dceb63d7.js";import{_ as Ae}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                 */const K=O=>(xe("data-v-ec1b1152"),O=O(),Se(),O),De={class:"main-container"},Te={class:"text-page-title"},$e=K(()=>u("span",{class:"ml-[10px]"},"天",-1)),Oe={key:0},Pe={key:1,class:"flex ml-[5px]"},Ne=K(()=>u("span",{class:"ml-[5px] mr-[5px]"},"|",-1)),Re={class:"form-tip"},Be={class:"form-tip"},Fe={class:"mt-[10px]"},Me={key:0},Ue={key:0},Ie={key:1},Le={class:"flex mt-[10px]"},je={class:"form-tip"},qe={class:"flex"},We={class:"dialog-footer"},Ze={class:"dialog-footer"},ze={class:"fixed-footer-wrap"},Ge={class:"fixed-footer"},Ke=de({__name:"sign_config",setup(O){ue().meta.title,f("signSet");const A=f(!0),D=f(!1),k=f(!1),B=f(),c=f({});let P=!1,N=0;const H={required:/[\S]+/,number:/^\d{0,10}$/,digit:/^\d{0,10}(.?\d{0,2})$/,special:/^\d{0,10}(.?\d{0,3})$/},J=$({day_award:[{required:!0,trigger:"change",validator:(o,e,p)=>{let _=!1;T.value.forEach(C=>{C.is_use&&(_=!0)}),_?p():p(t("daySignAwardPlaceholder"))}}],sign_period:[{required:!0,trigger:"blur",validator:(o,e,p)=>{e===null||e===""?p(t("signPeriodTip")):isNaN(e)||!H.number.test(e)?p(t("signPeriodLimitTips")):e<2||e>365?p(t("signPeriodMustZeroTips")):p()}}]}),v=$({gifts:[]}),R=f([]),a=$({is_use:0,sign_period:30,day_award:"",continue_award:[],rule_explain:""}),S=$({receive_num:"",continue_sign:"",receive_limit:"",continue_award:[]}),y=$({loading:!1,data:[]});(async()=>{const o=await(await ke()).data;Object.keys(a).forEach(e=>{o[e]!=null&&(a[e]=o[e])}),a.day_award&&(v.gifts=a.day_award,F()),a.continue_award&&a.continue_award.forEach((e,p)=>{y.data.push(g(e)),v.gifts=[];const _=g(e);delete _.continue_sign,delete _.continue_tag,delete _.receive_limit,delete _.receive_num,v.gifts=_,M(v,e,p)}),A.value=!1})();const T=f([]),F=async()=>{const o=await(await G(v)).data;T.value=[],Object.values(o).forEach(e=>{T.value.push(e)})},M=async(o,e,p=0,_=0)=>{const C=await(await G(o)).data;R.value=[],Object.values(C).forEach(V=>{R.value.push(V)}),S.receive_num=e.receive_num,S.continue_sign=e.continue_sign,S.receive_limit=e.receive_limit,S.continue_award=R.value,P?y.data.splice(N,1,g(S)):_==0?y.data.splice(p,1,g(S)):y.data.push(g(S)),P=!1,N=0},Q=async o=>{A.value||!o||await o.validate(e=>{if(e){const p=g(a);Ce(p).then(()=>{A.value=!1}).catch(()=>{A.value=!1})}})},U=()=>{D.value=!0},I=f(null),X=async()=>{var o;await((o=I.value)==null?void 0:o.verify())&&(D.value=!1,!a.day_award.hasOwnProperty("balance")&&!a.day_award.hasOwnProperty("point")&&a.day_award.shop_coupon.is_use==0&&(a.day_award=""),a.day_award.hasOwnProperty("balance")&&a.day_award.balance.is_use==1&&(a.day_award.balance.money=Number(a.day_award.balance.money)),v.gifts=a.day_award,F())},Y=()=>{a.day_award="",T.value=[]},ee=()=>{c.value="",k.value=!0},ae=(o,e)=>{P=o,N=e,c.value=a.continue_award[e],k.value=!0},L=f(null),te=async()=>{var o;if(await((o=L.value)==null?void 0:o.verify())&&(k.value=!1,!c.value.hasOwnProperty("balance")&&!c.value.hasOwnProperty("point")&&c.value.shop_coupon.is_use==0&&(c.value=""),c.value.hasOwnProperty("balance")&&c.value.balance.is_use==1&&(c.value.balance.money=Number(c.value.balance.money)),Object.keys(c.value).length>0)){const e=g(c.value);delete e.continue_sign,delete e.continue_tag,delete e.receive_limit,delete e.receive_num,v.gifts=e,a.continue_award.length>0&&a.continue_award.length-1,M(v,c.value,0,1),P?a.continue_award.splice(N,1,g(c.value)):a.continue_award.push(g(c.value))}},ne=o=>{y.data.splice(o,1),a.continue_award.splice(o,1)},ie=()=>{a.rule_explain=t("ruleExplainDefault")};return(o,e)=>{const p=_e,_=pe,C=me,V=fe,w=ge,le=ve,oe=ye,se=we,j=be,q=he;return r(),m("div",De,[W((r(),b(se,{class:"page-form",model:a,"label-width":"150px",ref_key:"ruleFormRef",ref:B,rules:J},{default:l(()=>[d(oe,{class:"box-card !border-none",shadow:"never"},{default:l(()=>[u("h3",Te,s(n(t)("signRule")),1),d(_,{label:n(t)("isUse")},{default:l(()=>[d(p,{modelValue:a.is_use,"onUpdate:modelValue":e[0]||(e[0]=i=>a.is_use=i)},null,8,["modelValue"])]),_:1},8,["label"]),a.is_use?(r(),b(_,{key:0,label:n(t)("signPeriod"),prop:"sign_period"},{default:l(()=>[d(C,{modelValue:a.sign_period,"onUpdate:modelValue":e[1]||(e[1]=i=>a.sign_period=i),modelModifiers:{trim:!0},onKeyup:e[2]||(e[2]=i=>n(ce)(i)),maxlength:"3",clearable:"",class:"input-width"},null,8,["modelValue"]),$e]),_:1},8,["label"])):h("",!0),a.is_use?(r(),b(_,{key:1,label:n(t)("daySignAward"),prop:"day_award"},{default:l(()=>[(r(!0),m(Z,null,z(T.value,(i,E)=>(r(),m("div",{key:E},[i.is_use=="1"?(r(),m("span",Oe,s(i.content)+"  ",1)):h("",!0)]))),128)),a.day_award==""?(r(),m("span",{key:0,class:"cursor-pointer tutorial-btn ml-[5px]",onClick:U},s(n(t)("set")),1)):(r(),m("div",Pe,[u("span",{class:"cursor-pointer tutorial-btn",onClick:U},s(n(t)("modify")),1),Ne,u("span",{class:"cursor-pointer tutorial-btn",onClick:Y},s(n(t)("delete")),1)])),u("div",Re,s(n(t)("daySignAwardTip")),1)]),_:1},8,["label"])):h("",!0),a.is_use?(r(),b(_,{key:2,label:n(t)("continueSignAward"),prop:"continue_award"},{default:l(()=>[u("div",null,[u("div",Be,s(n(t)("continueSignAwardTipTop")),1),u("div",Fe,[W((r(),b(le,{data:y.data,size:"large"},{empty:l(()=>[u("span",null,s(y.loading?"":n(t)("emptyData")),1)]),default:l(()=>[d(V,{prop:"continue_sign",label:n(t)("continueSign"),"min-width":"120"},null,8,["label"]),d(V,{label:n(t)("continueSignAward"),"min-width":"300"},{default:l(({row:i})=>[(r(!0),m(Z,null,z(i.continue_award,(E,re)=>(r(),m("div",{key:re},[E.is_use=="1"?(r(),m("span",Me,s(E.content),1)):h("",!0)]))),128))]),_:1},8,["label"]),d(V,{label:n(t)("receiveLimit"),"min-width":"120"},{default:l(({row:i})=>[i.receive_limit==1?(r(),m("span",Ue,s(n(t)("noLimit")),1)):(r(),m("span",Ie,s(n(t)("everyOneLimit"))+s(i.receive_num)+s(n(t)("time")),1))]),_:1},8,["label"]),d(V,{label:n(t)("operation"),align:"right",fixed:"right",width:"130"},{default:l(i=>[d(w,{type:"primary",link:"",onClick:E=>ae(!0,i.$index)},{default:l(()=>[x(s(n(t)("modify")),1)]),_:2},1032,["onClick"]),d(w,{type:"primary",link:"",onClick:E=>ne(i.$index)},{default:l(()=>[x(s(n(t)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[q,y.loading]])]),u("div",Le,[u("span",{class:"cursor-pointer tutorial-btn",onClick:ee},s(n(t)("add")),1)]),u("div",je,s(n(t)("continueSignAwardTipBottom")),1)])]),_:1},8,["label"])):h("",!0),a.is_use?(r(),b(_,{key:3,label:n(t)("ruleExplain"),prop:"rule_explain"},{default:l(()=>[u("div",qe,[d(C,{modelValue:a.rule_explain,"onUpdate:modelValue":e[3]||(e[3]=i=>a.rule_explain=i),modelModifiers:{trim:!0},placeholder:n(t)("ruleExplainTip"),type:"textarea",maxlength:"500","show-word-limit":"",rows:"5",class:"textarea-width",clearable:""},null,8,["modelValue","placeholder"]),d(w,{class:"ml-[20px]",type:"primary",onClick:e[4]||(e[4]=i=>ie()),plain:""},{default:l(()=>[x(s(n(t)("useDefaultExplain")),1)]),_:1})])]),_:1},8,["label"])):h("",!0)]),_:1})]),_:1},8,["model","rules"])),[[q,A.value]]),a.is_use?(r(),b(j,{key:0,modelValue:D.value,"onUpdate:modelValue":e[8]||(e[8]=i=>D.value=i),title:n(t)("daySignTitle"),width:"1000px","destroy-on-close":!0},{footer:l(()=>[u("span",We,[d(w,{onClick:e[6]||(e[6]=i=>D.value=!1)},{default:l(()=>[x(s(n(t)("cancel")),1)]),_:1}),d(w,{type:"primary",onClick:e[7]||(e[7]=i=>X())},{default:l(()=>[x(s(n(t)("confirm")),1)]),_:1})])]),default:l(()=>[d(Ve,{ref_key:"benefitsRef",ref:I,modelValue:a.day_award,"onUpdate:modelValue":e[5]||(e[5]=i=>a.day_award=i)},null,8,["modelValue"])]),_:1},8,["modelValue","title"])):h("",!0),a.is_use?(r(),b(j,{key:1,modelValue:k.value,"onUpdate:modelValue":e[12]||(e[12]=i=>k.value=i),title:n(t)("continueSignTitle"),width:"1200px","destroy-on-close":!0},{footer:l(()=>[u("span",Ze,[d(w,{onClick:e[10]||(e[10]=i=>k.value=!1)},{default:l(()=>[x(s(n(t)("cancel")),1)]),_:1}),d(w,{type:"primary",onClick:e[11]||(e[11]=i=>te())},{default:l(()=>[x(s(n(t)("confirm")),1)]),_:1})])]),default:l(()=>[d(Ee,{ref_key:"continueRef",ref:L,modelValue:c.value,"onUpdate:modelValue":e[9]||(e[9]=i=>c.value=i),sign_period:a.sign_period},null,8,["modelValue","sign_period"])]),_:1},8,["modelValue","title"])):h("",!0),u("div",ze,[u("div",Ge,[d(w,{type:"primary",onClick:e[13]||(e[13]=i=>Q(B.value))},{default:l(()=>[x(s(n(t)("save")),1)]),_:1})])])])}}});const wa=Ae(Ke,[["__scopeId","data-v-ec1b1152"]]);export{wa as default};
