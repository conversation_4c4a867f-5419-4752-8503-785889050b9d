import{d as ce,f as fe,r as w,q as a,n as oe,aN as be,b4 as ge,a5 as he,h as p,c as u,e as d,w as r,u as e,aT as ve,s as y,F as h,W as x,v as K,a as s,t as i,C as D,i as _,bO as M,Z as T,c_ as ye,_ as S,aU as xe,a9 as Ve,M as ke,L as De,bH as we,a4 as Ce,a1 as Ue,aL as Te,aZ as Se,au as Ee,av as Fe,N as Pe,ap as Re,a0 as Me,E as Ne,aW as Oe,aY as Ge,aq as Be,p as Ie,g as Ae}from"./index-30109030.js";/* empty css                    */import{_ as Le}from"./index.vue_vue_type_script_setup_true_lang-880b20ce.js";import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                          *//* empty css                 */import{_ as $e}from"./index.vue_vue_type_style_index_0_lang-146159e0.js";import He from"./index-e3ceb692.js";import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{ac as Ke,ad as Ze,ae as We}from"./goods-e07707eb.js";import{i as qe,D as ze}from"./delivery-ef62b210.js";import{u as je}from"./useGoodsEdit-71282c25.js";import{_ as Ye}from"./_plugin-vue_export-helper-c27b6911.js";import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";/* empty css                  *//* empty css                   */import"./attachment-bca8f41b.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                        *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./sortable.esm-be94e56d.js";import"./poster-65e59011.js";import"./diy_form-59d7f2cd.js";import"./range-77a5ce89.js";const E=B=>(Ie("data-v-f9cd1166"),B=B(),Ae(),B),Je={class:"main-container"},Qe={class:"goods-type-name"},Xe={class:"goods-type-desc"},el=E(()=>s("div",{class:"triangle"},null,-1)),ll=E(()=>s("div",{class:"selected"},"✓",-1)),ol=["onClick"],tl={class:"goods-type-name"},al={class:"goods-type-desc"},sl=E(()=>s("div",{class:"triangle"},null,-1)),dl=E(()=>s("div",{class:"selected"},"✓",-1)),rl={class:"flex flex-col"},il={class:"flex flex-col mt-[10px]"},nl={class:"text-[12px] text-[#999] leading-[20px]"},pl={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},ul={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},ml={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},_l={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},cl={class:"mt-[5px] text-[12px] text-[var(--el-color-primary)] leading-[20px]"},fl={class:"ml-[10px]"},bl={class:"ml-[10px]"},gl={class:"ml-[10px]"},hl={class:"ml-[120px] mb-[10px] text-[12px] text-[#999] leading-[20px]"},vl={class:"ml-[10px]"},yl={class:"ml-[10px]"},xl={class:"ml-[10px]"},Vl={class:"ml-[10px]"},kl={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Dl={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},wl={key:0,class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Cl={class:"el-form-item asterisk-left"},Ul={class:"el-form-item__label w-[120px]"},Tl={class:"spec-wrap"},Sl={class:"spec-edit-list"},El={class:"spec-name-wrap"},Fl={class:"spec-value-wrap"},Pl=["onClick"],Rl=E(()=>s("div",{class:"box"},null,-1)),Ml={class:"add-spec"},Nl={class:"batch-operation-sku"},Ol={class:"sku-table"},Gl={class:"el-table--fit el-table--default el-table",style:{width:"100%"}},Bl={class:"el-table__inner-wrapper"},Il={class:"el-table__header-wrapper"},Al={class:"el-table__header",border:"0",cellpadding:"0",cellspacing:"0",style:{width:"100%"}},Ll={class:"el-table__cell w-[50px]"},$l={class:"cell"},Hl={key:0,class:"el-table__cell"},Kl={class:"cell"},Zl={class:"el-table__cell"},Wl={class:"cell"},ql={class:"el-table__cell"},zl={class:"cell"},jl={class:"el-table__cell"},Yl={class:"cell"},Jl={class:"el-table__cell"},Ql={class:"cell"},Xl={class:"el-table__cell"},eo={class:"cell"},lo={class:"el-table__cell"},oo={class:"cell"},to={class:"el-table__cell"},ao={class:"cell"},so={class:"el-table__cell"},ro={class:"cell"},io={class:"el-table__cell"},no={class:"cell"},po={class:"el-table__body-wrapper text-[14px]"},uo={class:"el-scrollbar"},mo={class:"el-scrollbar__wrap el-scrollbar__wrap--hidden-default"},_o={class:"el-scrollbar__view",style:{display:"inline-block","vertical-align":"middle"}},co={class:"el-table__body",cellspacing:"0",cellpadding:"0",border:"0",style:{"table-layout":"fixed",width:"100%"}},fo={tabindex:"-1"},bo={class:"el-table__cell w-[50px]"},go={class:"cell"},ho=["rowspan"],vo={class:"cell"},yo={class:"el-table__cell"},xo={class:"cell"},Vo={class:"el-table__cell"},ko={class:"cell"},Do={class:"el-table__cell"},wo={class:"cell"},Co={class:"el-table__cell"},Uo={class:"cell"},To={class:"el-table__cell"},So={class:"cell"},Eo={class:"el-table__cell"},Fo={class:"cell"},Po={class:"el-table__cell"},Ro={class:"cell"},Mo={class:"el-table__cell"},No={class:"cell"},Oo={class:"el-table__cell"},Go={class:"cell"},Bo={key:0,class:"text-[12px] text-[#999] leading-[20px]"},Io={key:1,class:"text-[12px] text-[#999] leading-[20px]"},Ao={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Lo={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},$o={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Ho={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},Ko={key:0,class:"text-[12px] text-[#999] leading-[20px]"},Zo={class:"ml-[10px]"},Wo={class:"mt-[10px] text-[12px] text-[#999] leading-[20px]"},qo={class:"attr-table mt-[10px]"},zo=E(()=>s("colgroup",null,[s("col",{width:"30%"}),s("col",{width:"40%"}),s("col",{width:"20%"}),s("col",{width:"10%"})],-1)),jo={class:"bg-[#f7f7f7]"},Yo={class:"prompt-block"},Jo={class:"flex items-center"},Qo=E(()=>s("span",{class:"iconfont iconwenhao cursor-pointer ml-[3px] mt-[3px] !text-[14px]"},null,-1)),Xo={class:"attr-new"},et={key:0},lt={key:1},ot={key:1},tt={key:2},at=["onClick"],st={key:1},dt={colspan:"4",class:"text-center"},rt={class:"fixed-footer-wrap"},it={class:"fixed-footer"},nt=ce({__name:"real_edit",setup(B){const te=fe(),Z=w(),W=w(),A=w(),q=w(),z=w(),j=w(),Y=w(),J=w(),l=je({getFormRef(){return{basicFormRef:Z.value,priceStockFormRef:W.value,deliveryFormRef:A.value,goodsArgumentsFormRef:q.value,detailFormRef:z.value,skuFormRef:j.value,specValueRef:J.value,priceStockCommonFormRef:Y.value}},addApi:Ke,editApi:Ze,formData:{goods_type:"real"},appendFormData:{delivery_type:[],is_free_shipping:1,fee_type:"template",delivery_money:"",delivery_template_id:""},appendRefreshGoodsSkuData:{weight:{value:"",regExp:"special",message:a("weightTips")},volume:{value:"",regExp:"special",message:a("volumeTips")}},appendSingleGoodsData(g){return{weight:g.weight,volume:g.volume}},getFormRules(g,t){return{weight:[{trigger:"blur",validator:(V,v,n)=>{g.spec_type=="single"&&v?isNaN(v)||!t.special.test(v)?n(new Error(a("weightTips"))):v<0?n(new Error(a("weightNotZeroTips"))):n():n()}}],volume:[{trigger:"blur",validator:(V,v,n)=>{g.spec_type=="single"&&v?isNaN(v)||!t.special.test(v)?n(new Error(a("volumeTips"))):v<0?n(new Error(a("volumeNotZeroTips"))):n():n()}}],delivery_type:[{required:!0,message:a("deliveryTypePlaceholder"),trigger:"blur"}],delivery_money:[{trigger:"blur",validator:(V,v,n)=>{g.delivery_type.indexOf("express")!=-1&&g.is_free_shipping==0&&g.fee_type=="fixed"?g.delivery_template_id.length==0&&v===""?n(new Error(a("deliveryMoneyPlaceholder"))):isNaN(v)||!t.digit.test(v)?n(new Error(a("deliveryMoneyTips"))):v<0?n(new Error(a("deliveryMoneyNotZeroTips"))):n():n()}}],delivery_template_id:[{trigger:"blur",validator:(V,v,n)=>{g.delivery_type.indexOf("express")!=-1&&g.is_free_shipping==0&&g.fee_type=="template"&&g.delivery_money.length==0&&v===""?n(new Error(a("deliveryTemplateIdPlaceholder"))):n()}}]}},getVerify(){return[{key:"delivery",verify:!1,ref:A.value}]}}),L=oe([]),$=oe([]),ae=w(!1);qe().then(g=>{const t=g.data;t&&L.splice(0,L.length,...t)});const se=()=>{const g=te.resolve({path:"/shop/order/shipping/template"});window.open(g.href)},Q=()=>{ze({}).then(g=>{const t=g.data;t&&$.splice(0,$.length,...t)})};Q(),We({goods_id:l.formData.goods_id}).then(g=>{const t=g.data;t&&(l.handleGoodsInit(t),l.formData.goods_id&&t.goods_info&&(l.formData.delivery_type=t.goods_info.delivery_type,l.formData.is_free_shipping=t.goods_info.is_free_shipping,l.formData.fee_type=t.goods_info.fee_type,l.formData.delivery_money=t.goods_info.delivery_money,l.formData.delivery_template_id=t.goods_info.delivery_template_id))});const de=()=>[{trigger:"blur",validator:(g,t,V)=>{l.formData.spec_type=="multi"?t.length>0&&(isNaN(t)||!l.regExp.special.test(t))?V(a("weightTips")):t<0?V(a("weightNotZeroTips")):V():V()}}],re=()=>[{trigger:"blur",validator:(g,t,V)=>{l.formData.spec_type=="multi"?t.length>0&&(isNaN(t)||!l.regExp.special.test(t))?V(a("volumeTips")):t<0?V(a("volumeNotZeroTips")):V():V()}}],ie=()=>{l.save()};return be(()=>{ge(()=>{document.addEventListener("click",g=>{const t=g.target.closest(".el-cascader-node__label");if(t){const V=t.parentNode.querySelector(".el-checkbox");V&&V.click()}})})}),(g,t)=>{const V=xe,v=Ve,n=ke,m=De,X=He,ne=$e,pe=we,F=Ce,P=Ue,R=Te,N=Se,k=Ee,C=Fe,U=Pe,O=Re,ee=he("CircleCloseFilled"),le=Me,G=Ne,H=Oe,ue=Ge,me=Le,_e=Be;return p(),u("div",Je,[d(v,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[d(V,{content:e(l).formData.goods_id?e(a)("updateGoods"):e(a)("addGoods"),icon:e(ve),onBack:t[0]||(t[0]=o=>e(l).back())},null,8,["content","icon"])]),_:1}),d(v,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:r(()=>[d(_e,{modelValue:e(l).activeName,"onUpdate:modelValue":t[73]||(t[73]=o=>e(l).activeName=o),onTabClick:e(l).tabHandleClick},{default:r(()=>[d(O,{label:e(a)("basicInfoTab"),name:"basic"},{default:r(()=>[d(U,{model:e(l).formData,"label-width":"120px",ref_key:"basicFormRef",ref:Z,rules:e(l).formRules,class:"page-form"},{default:r(()=>[e(l).formData.goods_id?(p(),y(n,{key:0,label:e(a)("goodsType")},{default:r(()=>[(p(!0),u(h,null,x(e(l).goodsType,o=>(p(),u("div",{key:o.type,class:K(["goods-type-wrap",[e(l).formData.goods_type==o.type?"selected":"disabled"]])},[s("div",Qe,i(o.name),1),s("div",Xe,"("+i(o.desc)+")",1),e(l).formData.goods_type==o.type?(p(),u(h,{key:0},[el,ll],64)):D("",!0)],2))),128))]),_:1},8,["label"])):(p(),y(n,{key:1,label:e(a)("goodsType")},{default:r(()=>[(p(!0),u(h,null,x(e(l).goodsType,o=>(p(),u("div",{class:K(["goods-type-wrap",{selected:e(l).formData.goods_type==o.type}]),key:o.type,onClick:b=>e(l).changeGoodsType(o)},[s("div",tl,i(o.name),1),s("div",al,"("+i(o.desc)+")",1),e(l).formData.goods_type==o.type?(p(),u(h,{key:0},[sl,dl],64)):D("",!0)],10,ol))),128))]),_:1},8,["label"])),d(n,{label:e(a)("goodsName"),prop:"goods_name"},{default:r(()=>[d(m,{modelValue:e(l).formData.goods_name,"onUpdate:modelValue":t[1]||(t[1]=o=>e(l).formData.goods_name=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("goodsNamePlaceholder"),class:"input-width",maxlength:"60","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:e(a)("subTitle"),prop:"sub_title"},{default:r(()=>[d(m,{modelValue:e(l).formData.sub_title,"onUpdate:modelValue":t[2]||(t[2]=o=>e(l).formData.sub_title=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("subTitlePlaceholder"),class:"input-width",maxlength:"30","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:e(a)("goodsImage"),prop:"goods_image"},{default:r(()=>[d(X,{modelValue:e(l).formData.goods_image,"onUpdate:modelValue":t[3]||(t[3]=o=>e(l).formData.goods_image=o),limit:10},null,8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("goodsVideo")},{default:r(()=>[s("div",rl,[d(ne,{modelValue:e(l).formData.goods_video,"onUpdate:modelValue":t[4]||(t[4]=o=>e(l).formData.goods_video=o),limit:1},null,8,["modelValue"]),d(m,{modelValue:e(l).formData.goods_video,"onUpdate:modelValue":t[5]||(t[5]=o=>e(l).formData.goods_video=o),clearable:"",placeholder:e(a)("在此输入外链视频地址"),class:"input-width mt-[10px]"},null,8,["modelValue","placeholder"]),s("div",il,[s("div",nl,i(e(a)("goodsVideoTipTile")),1),s("div",pl,i(e(a)("goodsVideoTipOne")),1),s("div",ul,i(e(a)("goodsVideoTipTwo")),1),s("div",ml,i(e(a)("goodsVideoTipThree")),1),s("div",_l,i(e(a)("goodsVideoTipFour")),1),s("div",cl,i(e(a)("goodsVideoTipFive")),1)])])]),_:1},8,["label"]),d(n,{label:e(a)("goodsCategory"),prop:"goods_category"},{default:r(()=>[d(pe,{modelValue:e(l).formData.goods_category,"onUpdate:modelValue":t[6]||(t[6]=o=>e(l).formData.goods_category=o),options:e(l).goodsCategoryOptions,props:e(l).goodsCategoryProps,clearable:"",filterable:"",onChange:e(l).categoryHandleChange,"popper-class":"choice"},null,8,["modelValue","options","props","onChange"]),s("div",fl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[7]||(t[7]=o=>e(l).refreshGoodsCategory(!0))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[8]||(t[8]=(...o)=>e(l).toGoodsCategoryEvent&&e(l).toGoodsCategoryEvent(...o))},i(e(a)("addGoodsCategory")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("brand")},{default:r(()=>[d(P,{modelValue:e(l).formData.brand_id,"onUpdate:modelValue":t[9]||(t[9]=o=>e(l).formData.brand_id=o),placeholder:e(a)("brandPlaceholder"),clearable:""},{default:r(()=>[(p(!0),u(h,null,x(e(l).brandOptions,o=>(p(),y(F,{key:o.brand_id,label:o.brand_name,value:o.brand_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),s("div",bl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[10]||(t[10]=o=>e(l).refreshGoodsBrand(!0))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[11]||(t[11]=(...o)=>e(l).toGoodsBrandEvent&&e(l).toGoodsBrandEvent(...o))},i(e(a)("addGoodsBrand")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("poster")},{default:r(()=>[d(P,{modelValue:e(l).formData.poster_id,"onUpdate:modelValue":t[12]||(t[12]=o=>e(l).formData.poster_id=o),placeholder:e(a)("posterPlaceholder"),clearable:""},{default:r(()=>[(p(!0),u(h,null,x(e(l).posterOptions,o=>(p(),y(F,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),s("div",gl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[13]||(t[13]=o=>e(l).refreshGoodsPoster(!0))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[14]||(t[14]=(...o)=>e(l).toPosterEvent&&e(l).toPosterEvent(...o))},i(e(a)("addGoodsPoster")),1)])]),_:1},8,["label"]),s("div",hl,i(e(a)("posterTips")),1),d(n,{label:e(a)("diyForm")},{default:r(()=>[d(P,{modelValue:e(l).formData.form_id,"onUpdate:modelValue":t[15]||(t[15]=o=>e(l).formData.form_id=o),placeholder:e(a)("diyFormPlaceholder"),clearable:""},{default:r(()=>[(p(!0),u(h,null,x(e(l).diyFormOptions,o=>(p(),y(F,{key:o.form_id,label:o.page_title,value:o.form_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),s("div",vl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[16]||(t[16]=o=>e(l).refreshDiyForm(!0))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[17]||(t[17]=(...o)=>e(l).toDiyFormEvent&&e(l).toDiyFormEvent(...o))},i(e(a)("addDiyForm")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("label")},{default:r(()=>[d(N,{modelValue:e(l).formData.label_ids,"onUpdate:modelValue":t[18]||(t[18]=o=>e(l).formData.label_ids=o)},{default:r(()=>[(p(!0),u(h,null,x(e(l).labelOptions,(o,b)=>(p(),y(R,{label:o.label_id,key:b},{default:r(()=>[_(i(o.label_name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),s("div",yl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[19]||(t[19]=(...o)=>e(l).refreshGoodsLabel&&e(l).refreshGoodsLabel(...o))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[20]||(t[20]=(...o)=>e(l).toGoodsLabelEvent&&e(l).toGoodsLabelEvent(...o))},i(e(a)("addGoodsLabel")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("goodsService")},{default:r(()=>[d(N,{modelValue:e(l).formData.service_ids,"onUpdate:modelValue":t[21]||(t[21]=o=>e(l).formData.service_ids=o)},{default:r(()=>[(p(!0),u(h,null,x(e(l).serviceOptions,(o,b)=>(p(),y(R,{label:o.service_id,key:b},{default:r(()=>[_(i(o.service_name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),s("div",xl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[22]||(t[22]=(...o)=>e(l).refreshGoodsService&&e(l).refreshGoodsService(...o))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[23]||(t[23]=(...o)=>e(l).toGoodsServiceEvent&&e(l).toGoodsServiceEvent(...o))},i(e(a)("addGoodsService")),1)])]),_:1},8,["label"]),e(l).formData.addon_shop_supplier&&e(l).formData.addon_shop_supplier.status==1?(p(),y(n,{key:2,label:e(a)("supplier")},{default:r(()=>[d(P,{modelValue:e(l).formData.supplier_id,"onUpdate:modelValue":t[24]||(t[24]=o=>e(l).formData.supplier_id=o),placeholder:e(a)("supplierPlaceholder"),clearable:""},{default:r(()=>[(p(!0),u(h,null,x(e(l).supplierOptions,o=>(p(),y(F,{key:o.supplier_id,label:o.supplier_name,value:o.supplier_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),s("div",Vl,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:t[25]||(t[25]=(...o)=>e(l).refreshSupplier&&e(l).refreshSupplier(...o))},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:t[26]||(t[26]=(...o)=>e(l).toSupplierEvent&&e(l).toSupplierEvent(...o))},i(e(a)("addSupplier")),1)])]),_:1},8,["label"])):D("",!0),d(n,{label:e(a)("status")},{default:r(()=>[d(C,{modelValue:e(l).formData.status,"onUpdate:modelValue":t[27]||(t[27]=o=>e(l).formData.status=o)},{default:r(()=>[d(k,{label:"1"},{default:r(()=>[_(i(e(a)("statusOn")),1)]),_:1}),d(k,{label:"0"},{default:r(()=>[_(i(e(a)("statusOff")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("isGive")},{default:r(()=>[s("div",null,[d(C,{modelValue:e(l).formData.is_gift,"onUpdate:modelValue":t[28]||(t[28]=o=>e(l).formData.is_gift=o)},{default:r(()=>[d(k,{label:1},{default:r(()=>[_(i(e(a)("yes")),1)]),_:1}),d(k,{label:0},{default:r(()=>[_(i(e(a)("no")),1)]),_:1})]),_:1},8,["modelValue"]),s("div",kl,i(e(a)("giftTips")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("unit"),prop:"unit"},{default:r(()=>[d(m,{modelValue:e(l).formData.unit,"onUpdate:modelValue":t[29]||(t[29]=o=>e(l).formData.unit=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("unitPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"6"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:e(a)("virtualSaleNum"),prop:"virtual_sale_num"},{default:r(()=>[s("div",null,[d(m,{modelValue:e(l).formData.virtual_sale_num,"onUpdate:modelValue":t[30]||(t[30]=o=>e(l).formData.virtual_sale_num=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("virtualSaleNumPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"8",onKeyup:t[31]||(t[31]=o=>e(M)(o)),onBlur:t[32]||(t[32]=o=>e(l).formData.virtual_sale_num=o.target.value)},{append:r(()=>[_(i(e(l).formData.unit?e(l).formData.unit:"件"),1)]),_:1},8,["modelValue","placeholder"]),s("div",Dl,i(e(a)("virtualSaleNumDesc")),1)])]),_:1},8,["label"]),d(n,{label:e(a)("sort"),prop:"sort"},{default:r(()=>[d(m,{modelValue:e(l).formData.sort,"onUpdate:modelValue":t[33]||(t[33]=o=>e(l).formData.sort=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("sortPlaceholder"),class:"input-width","show-word-limit":"",maxlength:"8",onKeyup:t[34]||(t[34]=o=>e(M)(o)),onBlur:t[35]||(t[35]=o=>e(l).formData.sort=o.target.value)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),d(O,{label:e(a)("priceStockTab"),name:"price_stock"},{default:r(()=>[d(U,{model:e(l).formData,"label-width":"120px",ref_key:"priceStockFormRef",ref:W,rules:e(l).formRules,class:"page-form"},{default:r(()=>[d(n,{label:e(a)("specType"),prop:"spec_type"},{default:r(()=>[s("div",null,[d(C,{modelValue:e(l).formData.spec_type,"onUpdate:modelValue":t[36]||(t[36]=o=>e(l).formData.spec_type=o)},{default:r(()=>[d(k,{label:"single",disabled:e(l).isDisabledPrice()},{default:r(()=>[_(i(e(a)("singleSpec")),1)]),_:1},8,["disabled"]),d(k,{label:"multi",disabled:e(l).isDisabledPrice()},{default:r(()=>[_(i(e(a)("multiSpec")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),e(l).isDisabledPrice()?(p(),u("div",wl,i(e(a)("participateInActiveDisableTips")),1)):D("",!0)])]),_:1},8,["label"]),e(l).formData.spec_type=="single"?(p(),u(h,{key:0},[d(n,{label:e(a)("price"),prop:"price"},{default:r(()=>[d(m,{modelValue:e(l).formData.price,"onUpdate:modelValue":t[37]||(t[37]=o=>e(l).formData.price=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8",disabled:e(l).isDisabledPrice()},{append:r(()=>[_(i(e(a)("yuan")),1)]),_:1},8,["modelValue","disabled"])]),_:1},8,["label"]),d(n,{label:e(a)("marketPrice"),prop:"market_price"},{default:r(()=>[d(m,{modelValue:e(l).formData.market_price,"onUpdate:modelValue":t[38]||(t[38]=o=>e(l).formData.market_price=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8"},{append:r(()=>[_(i(e(a)("yuan")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("costPrice"),prop:"cost_price"},{default:r(()=>[d(m,{modelValue:e(l).formData.cost_price,"onUpdate:modelValue":t[39]||(t[39]=o=>e(l).formData.cost_price=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8"},{append:r(()=>[_(i(e(a)("yuan")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("weight"),prop:"weight"},{default:r(()=>[d(m,{modelValue:e(l).formData.weight,"onUpdate:modelValue":t[40]||(t[40]=o=>e(l).formData.weight=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"6"},{append:r(()=>[_("kg")]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("volume"),prop:"volume"},{default:r(()=>[d(m,{modelValue:e(l).formData.volume,"onUpdate:modelValue":t[41]||(t[41]=o=>e(l).formData.volume=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"6"},{append:r(()=>[_("m³")]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(n,{label:e(a)("goodsStock"),prop:"stock"},{default:r(()=>[d(m,{modelValue:e(l).formData.stock,"onUpdate:modelValue":t[42]||(t[42]=o=>e(l).formData.stock=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("goodsStockPlaceholder"),class:"input-width",maxlength:"8",onKeyup:t[43]||(t[43]=o=>e(M)(o))},{append:r(()=>[_(i(e(l).formData.unit?e(l).formData.unit:e(a)("defaultUnit")),1)]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:e(a)("skuNo")},{default:r(()=>[d(m,{modelValue:e(l).formData.sku_no,"onUpdate:modelValue":t[44]||(t[44]=o=>e(l).formData.sku_no=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("skuNoPlaceholder"),class:"input-width",maxlength:"50",onKeyup:t[45]||(t[45]=o=>e(l).filterSpecial(o)),onBlur:t[46]||(t[46]=o=>e(l).goodsVerifyFn(o))},null,8,["modelValue","placeholder"])]),_:1},8,["label"])],64)):D("",!0)]),_:1},8,["model","rules"]),d(U,{model:e(l).goodsSkuData,"label-width":"120px",ref_key:"skuFormRef",ref:j,class:"page-form"},{default:r(()=>[T(s("div",Cl,[s("div",Ul,i(e(a)("goodsSku")),1),s("div",Tl,[s("div",Sl,[(p(!0),u(h,null,x(e(l).goodsSpecFormat,(o,b)=>(p(),u("div",{class:"spec-item",key:o.id},[s("div",El,[d(m,{modelValue:o.spec_name,"onUpdate:modelValue":c=>o.spec_name=c,modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("specNamePlaceholder"),class:"input-width",maxlength:"30"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),s("div",Fl,[s("ul",{ref_for:!0,ref_key:"specValueRef",ref:J},[(p(!0),u(h,null,x(o.values,(c,f)=>(p(),u("li",{class:K("draggable-element"+b),key:c.id},[d(m,{modelValue:c.spec_value_name,"onUpdate:modelValue":I=>c.spec_value_name=I,modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("specValueNamePlaceholder"),class:"input-width","suffix-icon":e(ye),maxlength:"30",onInput:e(l).specValueNameInputListener},null,8,["modelValue","onUpdate:modelValue","placeholder","suffix-icon","onInput"]),d(le,{class:"icon",size:20,color:"#7b7b7b",onClick:I=>e(l).deleteSpecValue(b,f)},{default:r(()=>[d(ee)]),_:2},1032,["onClick"])],2))),128))],512),s("span",{class:"text-primary text-[14px] add-spec-value",onClick:c=>e(l).addSpecValue(b)},i(e(a)("addSpecValue")),9,Pl),Rl]),d(le,{class:"del-spec",size:20,color:"#7b7b7b",onClick:c=>e(l).deleteSpec(b)},{default:r(()=>[d(ee)]),_:2},1032,["onClick"])]))),128))]),s("div",Ml,[d(G,{type:"primary",onClick:e(l).addSpec},{default:r(()=>[_(i(e(a)("addSpec")),1)]),_:1},8,["onClick"])]),T(s("div",Nl,[s("label",null,i(e(a)("batchOperationSku")),1),e(l).isDisabledPrice()?D("",!0):(p(),y(m,{key:0,modelValue:e(l).batchOperation.price,"onUpdate:modelValue":t[47]||(t[47]=o=>e(l).batchOperation.price=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("price"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"])),d(m,{modelValue:e(l).batchOperation.market_price,"onUpdate:modelValue":t[48]||(t[48]=o=>e(l).batchOperation.market_price=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("marketPrice"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"]),d(m,{modelValue:e(l).batchOperation.cost_price,"onUpdate:modelValue":t[49]||(t[49]=o=>e(l).batchOperation.cost_price=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("costPrice"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"]),d(m,{modelValue:e(l).batchOperation.stock,"onUpdate:modelValue":t[50]||(t[50]=o=>e(l).batchOperation.stock=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("stock"),class:"set-input",maxlength:"8"},null,8,["modelValue","placeholder"]),d(m,{modelValue:e(l).batchOperation.weight,"onUpdate:modelValue":t[51]||(t[51]=o=>e(l).batchOperation.weight=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("skuWeight"),class:"set-input",maxlength:"6"},null,8,["modelValue","placeholder"]),d(m,{modelValue:e(l).batchOperation.volume,"onUpdate:modelValue":t[52]||(t[52]=o=>e(l).batchOperation.volume=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("skuVolume"),class:"set-input",maxlength:"6"},null,8,["modelValue","placeholder"]),d(m,{modelValue:e(l).batchOperation.sku_no,"onUpdate:modelValue":t[53]||(t[53]=o=>e(l).batchOperation.sku_no=o),modelModifiers:{trim:!0},clearable:"",maxlength:"50",placeholder:e(a)("skuNo"),class:"set-input",onBlur:t[54]||(t[54]=o=>e(l).goodsVerifyFn(o))},null,8,["modelValue","placeholder"]),d(G,{type:"primary",onClick:e(l).saveBatch},{default:r(()=>[_(i(e(a)("confirm")),1)]),_:1},8,["onClick"])],512),[[S,Object.keys(e(l).goodsSkuData).length]]),T(s("div",Ol,[s("div",Gl,[s("div",Bl,[s("div",Il,[s("table",Al,[s("thead",null,[s("tr",null,[s("th",Ll,[s("div",$l,[d(R,{modelValue:e(l).formData.skuCheckAll,"onUpdate:modelValue":t[55]||(t[55]=o=>e(l).formData.skuCheckAll=o),indeterminate:e(l).formData.skuIsIndeterminate,onChange:e(l).skuHandleCheckAllChange},null,8,["modelValue","indeterminate","onChange"])])]),(p(!0),u(h,null,x(e(l).goodsSpecFormat,(o,b)=>(p(),u(h,{key:b},[o.spec_name?(p(),u("th",Hl,[s("div",Kl,i(o.spec_name),1)])):D("",!0)],64))),128)),s("th",Zl,[s("div",Wl,i(e(a)("image")),1)]),s("th",ql,[s("div",zl,i(e(a)("price")),1)]),s("th",jl,[s("div",Yl,i(e(a)("marketPrice")),1)]),s("th",Jl,[s("div",Ql,i(e(a)("costPrice")),1)]),s("th",Xl,[s("div",eo,i(e(a)("stock")),1)]),s("th",lo,[s("div",oo,i(e(a)("skuWeight")),1)]),s("th",to,[s("div",ao,i(e(a)("skuVolume")),1)]),s("th",so,[s("div",ro,i(e(a)("skuNo")),1)]),s("th",io,[s("div",no,i(e(a)("defaultSku")),1)])])])])]),d(N,{modelValue:e(l).formData.skuCheckedCities,"onUpdate:modelValue":t[57]||(t[57]=o=>e(l).formData.skuCheckedCities=o),onChange:e(l).handleCheckedCitiesChange},{default:r(()=>[s("div",po,[s("div",uo,[s("div",mo,[s("div",_o,[s("table",co,[s("tbody",fo,[(p(!0),u(h,null,x(e(l).goodsSkuData,(o,b,c)=>(p(),u("tr",{class:"el-table__row",key:b},[s("td",bo,[s("div",go,[(p(),y(R,{label:b,key:b},{default:r(()=>[_(i(""))]),_:2},1032,["label"]))])]),(p(!0),u(h,null,x(e(l).specData,(f,I)=>(p(),u(h,{key:I},[f.index==c?(p(),u("td",{key:0,class:"el-table__cell",rowspan:f.rowSpan},[s("div",vo,i(f.spec_value_name),1)],8,ho)):D("",!0)],64))),128)),s("td",yo,[s("div",xo,[d(X,{modelValue:o.sku_image,"onUpdate:modelValue":f=>o.sku_image=f,limit:1,width:"50px",height:"50px"},null,8,["modelValue","onUpdate:modelValue"])])]),s("td",Vo,[s("div",ko,[d(n,{prop:b+".price",rules:e(l).skuPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.price,"onUpdate:modelValue":f=>o.price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8",disabled:e(l).isDisabledPrice()},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])])]),s("td",Do,[s("div",wo,[d(n,{prop:b+".market_price",rules:e(l).skuMarketPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.market_price,"onUpdate:modelValue":f=>o.market_price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),s("td",Co,[s("div",Uo,[d(n,{prop:b+".cost_price",rules:e(l).skuCostPriceRules(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.cost_price,"onUpdate:modelValue":f=>o.cost_price=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),s("td",To,[s("div",So,[d(n,{prop:b+".stock",rules:e(l).skuStockRules(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.stock,"onUpdate:modelValue":f=>o.stock=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0",onInput:e(l).specStockSum,maxlength:"8"},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1032,["prop","rules"])])]),s("td",Eo,[s("div",Fo,[d(n,{prop:b+".weight",rules:de(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.weight,"onUpdate:modelValue":f=>o.weight=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"6"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),s("td",Po,[s("div",Ro,[d(n,{prop:b+".volume",rules:re(),class:"sku-form-item-wrap"},{default:r(()=>[d(m,{modelValue:o.volume,"onUpdate:modelValue":f=>o.volume=f,modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",maxlength:"6"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])])]),s("td",Mo,[s("div",No,[d(m,{modelValue:o.sku_no,"onUpdate:modelValue":f=>o.sku_no=f,modelModifiers:{trim:!0},clearable:"",maxlength:"50",onBlur:t[56]||(t[56]=f=>e(l).goodsVerifyFn(f))},null,8,["modelValue","onUpdate:modelValue"])])]),s("td",Oo,[s("div",Go,[d(H,{modelValue:o.is_default,"onUpdate:modelValue":f=>o.is_default=f,"active-value":1,"inactive-value":0,onChange:f=>e(l).specValueIsDefaultChangeListener(f,b)},null,8,["modelValue","onUpdate:modelValue","onChange"])])])]))),128))])])])])])])]),_:1},8,["modelValue","onChange"])])])],512),[[S,Object.keys(e(l).goodsSkuData).length]])])],512),[[S,e(l).formData.spec_type=="multi"]]),d(n,{label:e(a)("memberDiscount")},{default:r(()=>[s("div",null,[d(C,{modelValue:e(l).formData.member_discount,"onUpdate:modelValue":t[58]||(t[58]=o=>e(l).formData.member_discount=o)},{default:r(()=>[d(k,{label:""},{default:r(()=>[_(i(e(a)("nonparticipation")),1)]),_:1}),d(k,{label:"discount"},{default:r(()=>[_(i(e(a)("discount")),1)]),_:1}),d(k,{label:"fixed_price"},{default:r(()=>[_(i(e(a)("fixedPrice")),1)]),_:1})]),_:1},8,["modelValue"]),e(l).formData.member_discount=="discount"?(p(),u("div",Bo,i(e(a)("discountHint")),1)):D("",!0),e(l).formData.member_discount=="fixed_price"?(p(),u("div",Io,i(e(a)("fixedPriceHint")),1)):D("",!0)])]),_:1},8,["label"])]),_:1},8,["model"]),d(U,{model:e(l).formData,"label-width":"120px",ref_key:"priceStockCommonFormRef",ref:Y,rules:e(l).formRules,class:"page-form"},{default:r(()=>[d(n,{label:e(a)("isLimit")},{default:r(()=>[s("div",null,[d(H,{modelValue:e(l).formData.is_limit,"onUpdate:modelValue":t[59]||(t[59]=o=>e(l).formData.is_limit=o),"active-value":1,"inactive-value":0},null,8,["modelValue"]),s("div",Ao,i(e(a)("isLimitTips")),1)])]),_:1},8,["label"]),e(l).formData.is_limit=="1"?(p(),y(n,{key:0,label:e(a)("limitType"),prop:"limit_type"},{default:r(()=>[s("div",null,[d(C,{modelValue:e(l).formData.limit_type,"onUpdate:modelValue":t[60]||(t[60]=o=>e(l).formData.limit_type=o)},{default:r(()=>[d(k,{label:1},{default:r(()=>[_(i(e(a)("singleTime")),1)]),_:1}),d(k,{label:2},{default:r(()=>[_(i(e(a)("singlePerson")),1)]),_:1})]),_:1},8,["modelValue"]),s("div",Lo,i(e(a)("limitTypeTips")),1)])]),_:1},8,["label"])):D("",!0),e(l).formData.is_limit=="1"?(p(),y(n,{key:1,label:e(a)("maxBuy"),prop:"max_buy"},{default:r(()=>[s("div",null,[d(m,{modelValue:e(l).formData.max_buy,"onUpdate:modelValue":t[61]||(t[61]=o=>e(l).formData.max_buy=o),modelModifiers:{trim:!0},clearable:"",placeholder:e(a)("maxBuyPlaceholder"),class:"input-width",maxlength:"8",onKeyup:t[62]||(t[62]=o=>e(M)(o))},{append:r(()=>[_(i(e(l).formData.unit?e(l).formData.unit:e(a)("defaultUnit")),1)]),_:1},8,["modelValue","placeholder"]),s("div",$o,i(e(a)("maxBuyWarnTips")),1)])]),_:1},8,["label"])):D("",!0),d(n,{label:e(a)("minBuy"),prop:"min_buy"},{default:r(()=>[s("div",null,[d(m,{modelValue:e(l).formData.min_buy,"onUpdate:modelValue":t[63]||(t[63]=o=>e(l).formData.min_buy=o),modelModifiers:{trim:!0},clearable:"",class:"input-width",maxlength:"8",onKeyup:t[64]||(t[64]=o=>e(M)(o))},{append:r(()=>[_(i(e(l).formData.unit?e(l).formData.unit:e(a)("defaultUnit")),1)]),_:1},8,["modelValue"]),s("div",Ho,i(e(a)("minBuyTips")),1)])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),d(O,{label:e(a)("deliveryTab"),name:"delivery"},{default:r(()=>[d(U,{model:e(l).formData,"label-width":"120px",ref_key:"deliveryFormRef",ref:A,rules:e(l).formRules,class:"page-form"},{default:r(()=>[d(n,{label:e(a)("deliveryType"),prop:"delivery_type"},{default:r(()=>[s("div",null,[d(N,{modelValue:e(l).formData.delivery_type,"onUpdate:modelValue":t[65]||(t[65]=o=>e(l).formData.delivery_type=o)},{default:r(()=>[(p(!0),u(h,null,x(L,(o,b)=>(p(),y(R,{key:b,label:o.key},{default:r(()=>[_(i(o.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),ae.value?(p(),u("div",Ko,"请先在配置设置中设置配送方式")):D("",!0)])]),_:1},8,["label"]),T(d(n,{label:e(a)("isFreeShipping")},{default:r(()=>[d(H,{modelValue:e(l).formData.is_free_shipping,"onUpdate:modelValue":t[66]||(t[66]=o=>e(l).formData.is_free_shipping=o),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1},8,["label"]),[[S,e(l).formData.delivery_type.indexOf("express")!=-1]]),T(d(n,{label:e(a)("feeType"),prop:"fee_type"},{default:r(()=>[d(C,{modelValue:e(l).formData.fee_type,"onUpdate:modelValue":t[67]||(t[67]=o=>e(l).formData.fee_type=o)},{default:r(()=>[d(k,{label:"template"},{default:r(()=>[_(i(e(a)("selectTemplate")),1)]),_:1}),d(k,{label:"fixed"},{default:r(()=>[_(i(e(a)("fixedShipping")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),[[S,e(l).formData.delivery_type.indexOf("express")!=-1&&e(l).formData.is_free_shipping==0]]),T(d(n,{label:e(a)("deliveryMoney"),prop:"delivery_money"},{default:r(()=>[d(m,{modelValue:e(l).formData.delivery_money,"onUpdate:modelValue":t[68]||(t[68]=o=>e(l).formData.delivery_money=o),modelModifiers:{trim:!0},clearable:"",placeholder:"0.00",class:"input-width",maxlength:"8"},{append:r(()=>[_(i(e(a)("yuan")),1)]),_:1},8,["modelValue"])]),_:1},8,["label"]),[[S,e(l).formData.delivery_type.indexOf("express")!=-1&&e(l).formData.is_free_shipping==0&&e(l).formData.fee_type=="fixed"]]),T(d(n,{label:e(a)("deliveryTemplateId"),prop:"delivery_template_id"},{default:r(()=>[d(P,{modelValue:e(l).formData.delivery_template_id,"onUpdate:modelValue":t[69]||(t[69]=o=>e(l).formData.delivery_template_id=o),placeholder:e(a)("deliveryTemplateIdPlaceholder"),filterable:"",autocomplete:"off",clearable:""},{default:r(()=>[(p(!0),u(h,null,x($,o=>(p(),y(F,{key:o.template_id,label:o.template_name,value:o.template_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"]),s("div",Zo,[s("span",{class:"cursor-pointer text-primary mr-[10px]",onClick:Q},i(e(a)("refresh")),1),s("span",{class:"cursor-pointer text-primary",onClick:se},i(e(a)("addDeliveryTemplateId")),1)])]),_:1},8,["label"]),[[S,e(l).formData.delivery_type.indexOf("express")!=-1&&e(l).formData.is_free_shipping==0&&e(l).formData.fee_type=="template"]])]),_:1},8,["model","rules"])]),_:1},8,["label"]),d(O,{label:e(a)("goodsArguments"),name:"goods_arguments"},{default:r(()=>[d(U,{model:e(l).formData,"label-width":"120px",ref_key:"goodsArgumentsFormRef",ref:q,rules:e(l).formRules,class:"page-form"},{default:r(()=>[d(n,{label:e(a)("goodsArgumentsTemp")},{default:r(()=>[s("div",null,[d(P,{modelValue:e(l).formData.attr_ids,"onUpdate:modelValue":t[70]||(t[70]=o=>e(l).formData.attr_ids=o),placeholder:e(a)("goodsArgumentsTempPlaceholder"),clearable:"",multiple:"",onChange:e(l).attrChange,onClear:e(l).attrChange},{default:r(()=>[(p(!0),u(h,null,x(e(l).attrOptions,o=>(p(),y(F,{key:o.attr_id,label:o.attr_name,value:o.attr_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onClear"]),s("div",Wo,i(e(a)("goodsArgumentsTempHint")),1),s("table",qo,[zo,s("thead",jo,[s("tr",null,[s("th",null,i(e(a)("argumentsName")),1),s("th",null,i(e(a)("argumentsValue")),1),s("th",Yo,[s("div",Jo,[s("span",null,i(e(a)("sort")),1),d(ue,{class:"box-item",effect:"dark",content:e(a)("argumentsSortHint"),placement:"right"},{default:r(()=>[Qo]),_:1},8,["content"])])]),s("th",null,i(e(a)("operation")),1)])]),s("tbody",Xo,[e(l).attrTableData.length?(p(!0),u(h,{key:0},x(e(l).attrTableData,(o,b)=>(p(),u("tr",{class:"goods-attr-tr goods-new-attr-tr",key:b},[o.attr_value_id>0?(p(),u("td",et,i(o.attr_value_name),1)):(p(),u("td",lt,[d(m,{modelValue:o.attr_value_name,"onUpdate:modelValue":c=>o.attr_value_name=c,modelModifiers:{trim:!0},maxlength:"30","show-word-limit":"",clearable:""},null,8,["modelValue","onUpdate:modelValue"])])),s("td",null,[o.type=="text"?(p(),y(m,{key:0,maxlength:"30","show-word-limit":"",modelValue:o.select_child_val,"onUpdate:modelValue":c=>o.select_child_val=c,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])):o.type=="radio"?(p(),u("div",ot,[d(C,{modelValue:o.select_child_name,"onUpdate:modelValue":c=>o.select_child_name=c,onChange:c=>e(l).attrRadioChange(b,c)},{default:r(()=>[(p(!0),u(h,null,x(o.child,(c,f)=>(p(),y(k,{key:f,label:c.id},{default:r(()=>[_(i(c.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])):o.type=="checkbox"?(p(),u("div",tt,[d(N,{modelValue:o.select_child_name,"onUpdate:modelValue":c=>o.select_child_name=c,onChange:c=>e(l).attrCheckboxChange(b,c)},{default:r(()=>[(p(!0),u(h,null,x(o.child,(c,f)=>(p(),y(R,{key:f,label:c.id},{default:r(()=>[_(i(c.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])):D("",!0)]),s("td",null,[d(m,{modelValue:o.sort,"onUpdate:modelValue":c=>o.sort=c,modelModifiers:{trim:!0},maxlength:"6","show-word-limit":"",clearable:"",onKeyup:t[71]||(t[71]=c=>e(M)(c))},null,8,["modelValue","onUpdate:modelValue"])]),s("td",null,[s("span",{class:"cursor-pointer text-[var(--el-color-primary)]",onClick:c=>e(l).delAttr(b)},i(e(a)("delAttr")),9,at)])]))),128)):(p(),u("tr",st,[s("td",dt,i(e(a)("noData")),1)]))])]),d(G,{class:"mt-[15px]",onClick:e(l).addAttr},{default:r(()=>[_(i(e(a)("addGoodsArguments")),1)]),_:1},8,["onClick"])])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"]),d(O,{label:e(a)("goodsDesc"),name:"detail"},{default:r(()=>[d(U,{model:e(l).formData,"label-width":"120px",ref_key:"detailFormRef",ref:z,rules:e(l).formRules,class:"page-form"},{default:r(()=>[d(n,{label:e(a)("goodsDesc"),prop:"goods_desc"},{default:r(()=>[d(me,{modelValue:e(l).formData.goods_desc,"onUpdate:modelValue":t[72]||(t[72]=o=>e(l).formData.goods_desc=o),height:600,class:"editor-width",onHandleBlur:e(l).handleBlur},null,8,["modelValue","onHandleBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["label"])]),_:1},8,["modelValue","onTabClick"])]),_:1}),s("div",rt,[s("div",it,[d(G,{type:"primary",onClick:t[74]||(t[74]=o=>ie())},{default:r(()=>[_(i(e(a)("save")),1)]),_:1}),d(G,{onClick:t[75]||(t[75]=o=>e(l).back())},{default:r(()=>[_(i(e(a)("back")),1)]),_:1})])])])}}});const ta=Ye(nt,[["__scopeId","data-v-f9cd1166"]]);export{ta as default};
