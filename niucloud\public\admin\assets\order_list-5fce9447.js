import{d as A,y as K,f as X,r as x,n as ee,bI as ae,a5 as te,h as p,c as m,e,w as n,a as i,t as s,u as a,q as t,F as P,W as V,s as U,i as v,Z as le,C as w,B as oe,bJ as se,a4 as re,a1 as ne,L as de,M as ie,bL as pe,E as me,N as ce,a9 as _e,ap as ue,aq as be,ag as he,ah as fe,a0 as ge,aY as ye,a2 as xe,a3 as ve}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                 *//* empty css                    *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                *//* empty css                  *//* empty css                       */import"./el-form-item-4ed993c7.js";import{_ as ke}from"./goods_default-664bb559.js";import{g as Pe,a as Ve,b as we,c as Te}from"./order-caf8547a.js";import{_ as Fe}from"./_plugin-vue_export-helper-c27b6911.js";const Ce={class:"main-container"},Ee={class:"flex justify-between items-center"},De={class:"text-page-title"},Ne={class:"table-body min-h-[150px]"},Se={key:0},Me={class:"flex items-center justify-between bg-[#f7f8fa] mt-[10px] border-[#e4e7ed] border-solid border-b-[1px] px-3 h-[35px] text-[12px] text-[#666]"},Ue={class:"ml-5"},Ye={key:0,class:"ml-5"},ze=["onClick"],Be={class:"flex items-center min-w-[50px] mr-[10px]"},Le=["src"],Oe={key:1,class:"w-[50px] h-[50px]",src:ke,alt:""},Ie={class:"flex flex-col"},$e={class:"multi-hidden text-[14px]"},je={class:"text-[12px] text-[#999]"},He={style:{display:"inline-flex","align-items":"center"}},Re={class:"mr-[5px]"},qe={class:"flex flex-col"},Qe={class:"text-[13px]"},Ge={key:0,class:"text-[13px] mt-[5px]"},Je={key:0},We={class:"text-[#999]"},Ze={key:1},Ae={key:1,class:"text-[13px] mt-[5px]"},Ke={class:"text-[14px]"},Xe={class:"text-[14px]"},ea={class:"mt-[16px] flex justify-end"},aa=A({__name:"order_list",setup(ta){const Y=K(),z=X(),B=Y.meta.title,T=x(""),L=x([]),F=x([]),C=x([]);(async()=>{L.value=await(await Pe()).data,F.value=await(await Ve()).data,C.value=await(await we()).data})();const l=ee({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{search_type:"order_no",search_name:"",pay_type:"",order_from:"",status:"",create_time:[],pay_time:[],activity_type:"newcomer_discount"}}),E=x(),h=(c=1)=>{l.loading=!0,l.page=c,Te({page:l.page,limit:l.limit,...l.searchParam}).then(r=>{l.loading=!1,l.data=r.data.data.map(_=>(_.order_goods.forEach(b=>{b.rowNum=_.order_goods.length}),_)),l.total=r.data.total,se(l.page,l.limit,l.searchParam)}).catch(()=>{l.loading=!1})};h(ae(l.searchParam).page);const O=({row:c,column:r,rowIndex:_,columnIndex:b})=>_===0?b>1?[c.rowNum,1]:[1,1]:b>1?[0,0]:[1,1],I=c=>{l.searchParam.status=c,h()},D=c=>{const r=z.resolve({path:"/shop/order/detail",query:{order_id:c.order_id}});window.open(r.href)},$=c=>{c&&(c.resetFields(),h())};return(c,r)=>{const _=re,b=ne,j=de,f=ie,N=pe,k=me,H=ce,S=_e,y=ue,R=be,u=he,M=fe,q=te("QuestionFilled"),Q=ge,G=ye,J=xe,W=ve;return p(),m("div",Ce,[e(S,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[i("div",Ee,[i("span",De,s(a(B)),1)]),e(S,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:n(()=>[e(H,{inline:!0,model:l.searchParam,ref_key:"searchFormRef",ref:E},{default:n(()=>[e(f,{label:a(t)("orderInfo"),prop:"search_name"},{default:n(()=>[e(b,{modelValue:l.searchParam.search_type,"onUpdate:modelValue":r[0]||(r[0]=o=>l.searchParam.search_type=o),clearable:"",class:"input-item"},{default:n(()=>[e(_,{label:a(t)("orderNo"),value:"order_no"},null,8,["label"]),e(_,{label:a(t)("outTradeNo"),value:"out_trade_no"},null,8,["label"])]),_:1},8,["modelValue"]),e(j,{class:"input-item ml-3",modelValue:l.searchParam.search_name,"onUpdate:modelValue":r[1]||(r[1]=o=>l.searchParam.search_name=o),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1},8,["label"]),e(f,{label:a(t)("payType"),prop:"pay_type"},{default:n(()=>[e(b,{modelValue:l.searchParam.pay_type,"onUpdate:modelValue":r[2]||(r[2]=o=>l.searchParam.pay_type=o),clearable:"",class:"input-item"},{default:n(()=>[(p(!0),m(P,null,V(F.value,(o,g)=>(p(),U(_,{key:g,label:o.name,value:o.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(f,{label:a(t)("fromType"),prop:"order_from"},{default:n(()=>[e(b,{modelValue:l.searchParam.order_from,"onUpdate:modelValue":r[3]||(r[3]=o=>l.searchParam.order_from=o),clearable:"",class:"input-item"},{default:n(()=>[(p(!0),m(P,null,V(C.value,(o,g)=>(p(),U(_,{key:g,label:o,value:g},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(f,{label:a(t)("createTime"),prop:"create_time"},{default:n(()=>[e(N,{modelValue:l.searchParam.create_time,"onUpdate:modelValue":r[4]||(r[4]=o=>l.searchParam.create_time=o),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":a(t)("startDate"),"end-placeholder":a(t)("endDate")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1},8,["label"]),e(f,{label:a(t)("payTime"),prop:"pay_time"},{default:n(()=>[e(N,{modelValue:l.searchParam.pay_time,"onUpdate:modelValue":r[5]||(r[5]=o=>l.searchParam.pay_time=o),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":a(t)("startDate"),"end-placeholder":a(t)("endDate")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1},8,["label"]),e(f,null,{default:n(()=>[e(k,{type:"primary",onClick:r[6]||(r[6]=o=>h())},{default:n(()=>[v(s(a(t)("search")),1)]),_:1}),e(k,{onClick:r[7]||(r[7]=o=>$(E.value))},{default:n(()=>[v(s(a(t)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(R,{modelValue:T.value,"onUpdate:modelValue":r[8]||(r[8]=o=>T.value=o),class:"demo-tabs",onTabChange:I},{default:n(()=>[e(y,{label:a(t)("all"),name:""},null,8,["label"]),e(y,{label:a(t)("toBeShipped"),name:"2"},null,8,["label"]),e(y,{label:a(t)("shipped"),name:"3"},null,8,["label"]),e(y,{label:a(t)("completed"),name:"5"},null,8,["label"]),e(y,{label:a(t)("closed"),name:"-1"},null,8,["label"])]),_:1},8,["modelValue"]),i("div",null,[e(M,{data:l.data,size:"large",class:"table-top"},{default:n(()=>[e(u,{label:a(t)("orderGoods"),"min-width":"200"},null,8,["label"]),e(u,{label:a(t)("goodsPriceNumber"),"min-width":"150"},null,8,["label"]),e(u,{label:a(t)("orderMoney"),"min-width":"150"},null,8,["label"]),e(u,{label:a(t)("orderStatus"),"min-width":"100"},null,8,["label"]),e(u,{label:a(t)("operation"),fixed:"right",align:"right","min-width":"100"},null,8,["label"])]),_:1},8,["data"])]),le((p(),m("div",Ne,[l.loading?w("",!0):(p(),m("div",Se,[l.data.length?(p(!0),m(P,{key:0},V(l.data,(o,g)=>(p(),m("div",{key:g},[i("div",Me,[i("div",null,[i("span",null,s(a(t)("orderNo"))+"："+s(o.order_no),1),i("span",Ue,s(a(t)("createTime"))+"："+s(o.create_time),1),o.pay?(p(),m("span",Ye,s(a(t)("payType"))+"："+s(o.pay.type_name),1)):w("",!0)])]),e(M,{data:o.order_goods,size:"large","show-header":!1,"span-method":O},{empty:n(()=>[i("span",null,s(l.loading?"":a(t)("emptyData")),1)]),default:n(()=>[e(u,{"min-width":"200"},{default:n(({row:d})=>[i("div",{class:"flex cursor-pointer",onClick:Z=>D(d)},[i("div",Be,[d.goods_image_thumb_small?(p(),m("img",{key:0,class:"w-[50px] h-[50px]",src:a(oe)(d.goods_image_thumb_small),alt:""},null,8,Le)):(p(),m("img",Oe))]),i("div",Ie,[i("p",$e,s(d.goods_name),1),i("span",je,s(d.sku_name),1)])],8,ze)]),_:1}),e(u,{"min-width":"150"},{header:n(()=>[i("div",He,[i("span",Re,s(a(t)("goodsPriceNumber")),1),e(G,{class:"box-item",effect:"light",placement:"top"},{content:n(()=>[v(s(a(t)("goodsPriceNumberTips")),1)]),default:n(()=>[e(Q,{color:"#666"},{default:n(()=>[e(q)]),_:1})]),_:1})])]),default:n(({row:d})=>[i("div",qe,[i("span",Qe,"￥"+s(d.price),1),d.extend&&d.extend.newcomer_price?(p(),m("span",Ge,[parseFloat(d.extend.newcomer_price)&&d.num>1?(p(),m("span",Je,[v(s(d.num)+s(a(t)("piece")),1),i("span",We,"（第1"+s(a(t)("piece"))+"，￥"+s(parseFloat(d.extend.newcomer_price).toFixed(2))+"/"+s(a(t)("piece"))+"；第"+s(d.num>2?"2~"+d.num:"2")+s(a(t)("piece"))+"，￥"+s(parseFloat(d.price).toFixed(2))+"/"+s(a(t)("piece"))+"）",1)])):(p(),m("span",Ze,s(d.num)+s(a(t)("piece")),1))])):(p(),m("span",Ae,s(d.num)+s(a(t)("piece")),1))])]),_:1}),e(u,{label:a(t)("orderMoney"),"min-width":"150","class-name":"border-0 border-l-[1px] border-solid border-[var(--el-table-border-color)]"},{default:n(({row:d})=>[i("span",Ke,"￥"+s(o.order_money),1)]),_:2},1032,["label"]),e(u,{label:a(t)("orderStatus"),"min-width":"100"},{default:n(({row:d})=>[i("span",Xe,s(o.status_name.name),1)]),_:2},1032,["label"]),e(u,{label:a(t)("operation"),fixed:"right",align:"right","min-width":"100"},{default:n(({row:d})=>[e(k,{type:"primary",link:"",onClick:Z=>D(o)},{default:n(()=>[v(s(a(t)("info")),1)]),_:2},1032,["onClick"])]),_:2},1032,["label"])]),_:2},1032,["data"])]))),128)):w("",!0)]))])),[[W,l.loading]]),i("div",ea,[e(J,{"current-page":l.page,"onUpdate:current-page":r[9]||(r[9]=o=>l.page=o),"page-size":l.limit,"onUpdate:page-size":r[10]||(r[10]=o=>l.limit=o),layout:"total, sizes, prev, pager, next, jumper",total:l.total,onSizeChange:r[11]||(r[11]=o=>h()),onCurrentChange:h},null,8,["current-page","page-size","total"])])]),_:1})])}}});const Ta=Fe(aa,[["__scopeId","data-v-c08a2b0d"]]);export{Ta as default};
