import{d as j,y as I,f as q,n as J,r as P,bI as O,h as _,c as k,e as t,w as r,a as d,t as i,u as n,i as c,q as a,F as W,W as Z,s as x,Z as A,bJ as G,af as H,E as K,ap as Q,aq as X,L as Y,M as ee,a4 as te,a1 as ae,N as le,a9 as ne,ag as oe,ah as re,a2 as pe,a3 as ie}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                    *//* empty css                  */import{b as se,l as me,n as de}from"./printer-342d98c9.js";const ce={class:"main-container"},ue={class:"flex justify-between items-center mb-[5px]"},_e={class:"text-lg"},ge={class:"mt-[10px]"},be={class:"mt-[16px] flex justify-end"},Me=j({__name:"template_list",setup(fe){const E=I(),g=q(),w=E.meta.title,C=p=>{g.push({path:p})},e=J({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{template_name:"",template_type:""}}),f=P(),h=P([]);se({}).then(p=>{p.data&&(h.value=p.data)});const s=(p=1)=>{e.loading=!0,e.page=p,me({page:e.page,limit:e.limit,...e.searchParam}).then(l=>{e.loading=!1,e.data=l.data.data,e.total=l.data.total,G(e.page,e.limit,e.searchParam)}).catch(()=>{e.loading=!1})};s(O(e.searchParam).page);const V=()=>{g.push("/printer/template/add")},B=p=>{g.push("/printer/template/edit?template_id="+p.template_id)},F=p=>{H.confirm(a("printerTemplateDeleteTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{de(p).then(()=>{s()})})},N=p=>{p&&(p.resetFields(),s())};return(p,l)=>{const m=K,y=Q,z=X,L=Y,b=ee,S=te,$=ae,D=le,v=ne,u=oe,M=re,R=pe,U=ie;return _(),k("div",ce,[t(v,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[d("div",ue,[d("span",_e,i(n(w)),1),t(m,{type:"primary",onClick:V},{default:r(()=>[c(i(n(a)("addPrinterTemplate")),1)]),_:1})]),t(z,{class:"demo-tabs","model-value":"/printer/template/list",onTabChange:C},{default:r(()=>[t(y,{label:n(a)("tabPrinterManager"),name:"/printer/list"},null,8,["label"]),t(y,{label:n(a)("tabPrinterTemplate"),name:"/printer/template/list"},null,8,["label"])]),_:1}),t(v,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:r(()=>[t(D,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:f},{default:r(()=>[t(b,{label:n(a)("templateName"),prop:"template_name"},{default:r(()=>[t(L,{modelValue:e.searchParam.template_name,"onUpdate:modelValue":l[0]||(l[0]=o=>e.searchParam.template_name=o),modelModifiers:{trim:!0},placeholder:n(a)("templateNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(b,{label:n(a)("templateType"),prop:"template_type"},{default:r(()=>[t($,{modelValue:e.searchParam.template_type,"onUpdate:modelValue":l[1]||(l[1]=o=>e.searchParam.template_type=o),placeholder:n(a)("templateTypePlaceholder"),clearable:""},{default:r(()=>[(_(!0),k(W,null,Z(h.value,(o,T)=>(_(),x(S,{key:p.itemkey,label:o.title,value:o.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),t(b,null,{default:r(()=>[t(m,{type:"primary",onClick:l[2]||(l[2]=o=>s())},{default:r(()=>[c(i(n(a)("search")),1)]),_:1}),t(m,{onClick:l[3]||(l[3]=o=>N(f.value))},{default:r(()=>[c(i(n(a)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),d("div",ge,[A((_(),x(M,{data:e.data,size:"large"},{empty:r(()=>[d("span",null,i(e.loading?"":n(a)("emptyData")),1)]),default:r(()=>[t(u,{prop:"template_name",label:n(a)("templateName"),"min-width":"220","show-overflow-tooltip":!0},null,8,["label"]),t(u,{prop:"template_type_name",label:n(a)("templateType"),"min-width":"180","show-overflow-tooltip":!0},null,8,["label"]),t(u,{prop:"create_time",label:n(a)("createTime"),"min-width":"120"},null,8,["label"]),t(u,{label:n(a)("operation"),fixed:"right","min-width":"120"},{default:r(({row:o})=>[t(m,{type:"primary",link:"",onClick:T=>B(o)},{default:r(()=>[c(i(n(a)("edit")),1)]),_:2},1032,["onClick"]),t(m,{type:"primary",link:"",onClick:T=>F(o.template_id)},{default:r(()=>[c(i(n(a)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[U,e.loading]]),d("div",be,[t(R,{"current-page":e.page,"onUpdate:current-page":l[4]||(l[4]=o=>e.page=o),"page-size":e.limit,"onUpdate:page-size":l[5]||(l[5]=o=>e.limit=o),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:l[6]||(l[6]=o=>s()),onCurrentChange:s},null,8,["current-page","page-size","total"])])])]),_:1})])}}});export{Me as default};
