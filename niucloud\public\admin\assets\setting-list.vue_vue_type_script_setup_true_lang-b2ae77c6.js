import{d as T,r as C,l as f,h as n,c as s,F as V,W as g,i as c,a as i,e as a,u as e,q as p,C as d,w as k,t as b,s as B,L as F,aL as S,aZ as W,E as M,V as G}from"./index-30109030.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                          *//* empty css                    */import{_ as q}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";/* empty css                 */const Z={key:0,class:"flex items-center mb-[8px]"},j={class:"!w-[200px] inline-block mx-[5px]"},z={key:1,class:"flex items-center mb-[8px]"},H={class:"!w-[200px] inline-block mx-[5px]"},J={key:2,class:"flex items-center mb-[8px]"},K={class:"!w-[200px] inline-block mx-[5px]"},Q={key:3,class:"flex items-center mb-[8px]"},Y={class:"!w-[200px] inline-block mx-[5px]"},ee={key:4,class:"flex items-center mb-[8px]"},le={class:"!w-[200px] inline-block mx-[5px]"},oe={key:5,class:"flex items-center mb-[8px]"},te={class:"!w-[200px] inline-block mx-[5px]"},ae={key:6,class:"flex items-center mb-[8px]"},ce={class:"!w-[200px] inline-block mx-[5px]"},re={key:7,class:"flex items-center mb-[8px]"},ne={class:"!w-[200px] inline-block mx-[5px]"},se={key:8,class:"flex items-center mb-[8px]"},pe={class:"!w-[200px] inline-block mx-[5px]"},ie={key:9,class:"flex items-center mb-[8px]"},de={class:"!w-[200px] inline-block mx-[5px]"},me={key:10,class:"flex items-center mb-[8px]"},ue={class:"!w-[200px] inline-block mx-[5px]"},_e={key:11,class:"flex items-center mb-[8px]"},ve={class:"!w-[200px] inline-block mx-[5px]"},ye={key:12,class:"flex items-center mb-[8px]"},ke={class:"!w-[200px] inline-block mx-[5px]"},he={key:13,class:"flex items-center mb-[8px]"},xe={class:"!w-[200px] inline-block mx-[5px]"},Ve={key:14,class:"flex items-center mb-[8px]"},Ce={class:"!w-[200px] inline-block mx-[5px]"},be={key:15,class:"flex items-center mb-[8px]"},Ue={class:"!w-[200px] inline-block mx-[5px]"},fe={key:16,class:"flex items-center mb-[8px]"},ge={class:"!w-[200px] inline-block mx-[5px]"},Pe={key:17,class:"flex items-center mb-[8px]"},we={class:"!w-[200px] inline-block mx-[5px]"},Re={key:18,class:"flex items-center mb-[8px]"},$e={class:"!w-[200px] inline-block mx-[5px]"},Ee={key:19,class:"flex items-center mb-[8px]"},Le={class:"!w-[200px] inline-block mx-[5px]"},Ie={key:20,class:"flex items-center mb-[8px]"},Ae={class:"!w-[200px] inline-block mx-[5px]"},De={key:21,class:"flex items-center mb-[8px]"},Xe={class:"!w-[200px] inline-block mx-[5px]"},Ne={key:22,class:"flex items-center mb-[8px]"},Oe={class:"!w-[200px] inline-block mx-[5px]"},Te={key:23,class:"flex items-center mb-[8px]"},Be={class:"!w-[200px] inline-block mx-[5px]"},Fe={key:24,class:"flex items-center mb-[8px]"},Se={class:"!w-[200px] inline-block mx-[5px]"},We={key:25,class:"flex items-center mb-[8px]"},Me={class:"!w-[200px] inline-block mx-[5px]"},Ge={key:26,class:"flex items-center mb-[8px]"},qe={class:"!w-[200px] inline-block mx-[5px]"},Ze={key:27,class:"flex items-center mb-[8px]"},je={class:"!w-[200px] inline-block mx-[5px]"},ze={class:"dialog-footer"},tl=T({__name:"setting-list",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue","change"],setup(P,{expose:w,emit:R}){const $=P,h=C(!1),E=C([{privacy_key:"UserInfo",privacy_text:"用户信息（微信昵称、头像）"},{privacy_key:"Location",privacy_text:"位置信息"},{privacy_key:"Address",privacy_text:"地址"},{privacy_key:"Invoice",privacy_text:"发票信息"},{privacy_key:"RunData",privacy_text:"微信运动数据"},{privacy_key:"Record",privacy_text:"麦克风"},{privacy_key:"Album",privacy_text:"选中的照片或视频信息"},{privacy_key:"Camera",privacy_text:"摄像头"},{privacy_key:"PhoneNumber",privacy_text:"手机号码"},{privacy_key:"Contact",privacy_text:"通讯录（仅写入）权限"},{privacy_key:"DeviceInfo",privacy_text:"设备信息"},{privacy_key:"EXIDNumber",privacy_text:"身份证号码"},{privacy_key:"EXOrderInfo",privacy_text:"订单信息"},{privacy_key:"EXUserPublishContent",privacy_text:"发布内容"},{privacy_key:"EXUserFollowAcct",privacy_text:"所关注账号"},{privacy_key:"EXUserOpLog",privacy_text:"操作日志"},{privacy_key:"AlbumWriteOnly",privacy_text:"相册（仅写入）权限"},{privacy_key:"LicensePlate",privacy_text:"车牌号"},{privacy_key:"BlueTooth",privacy_text:"蓝牙"},{privacy_key:"CalendarWriteOnly",privacy_text:"日历（仅写入）权限"},{privacy_key:"Email",privacy_text:"邮箱"},{privacy_key:"MessageFile",privacy_text:"选中的文件"},{privacy_key:"ChooseLocation",privacy_text:"选择的位置信息"},{privacy_key:"Accelerometer",privacy_text:"加速传感器"},{privacy_key:"Compass",privacy_text:"磁场传感器"},{privacy_key:"DeviceMotion",privacy_text:"方向传感器"},{privacy_key:"Gyroscope",privacy_text:"陀螺仪传感器"},{privacy_key:"Clipboard",privacy_text:"剪切板"}]),t=f({get(){return $.modelValue},set(v){R("update:modelValue",v)}}),m=v=>{t.value.splice(v,1)},x=C([]),L=()=>{x.value.forEach(v=>{t.value.push({privacy_key:v,privacy_text:""})}),h.value=!1,x.value=[]},I=()=>{h.value=!0},A=f(()=>t.value.map(v=>v.privacy_key)),D=v=>A.value.includes(v);return w({addSettingList:I}),(v,y)=>{const u=F,_=q,X=S,N=W,U=M,O=G;return n(),s(V,null,[(n(!0),s(V,null,g(e(t),(r,l)=>(n(),s("div",null,[r.privacy_key=="UserInfo"?(n(),s("div",Z,[c(" 为了"),i("div",j,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，收集你的微信昵称、头像。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Location"?(n(),s("div",z,[c(" 为了"),i("div",H,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，收集你的位置信息。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="PhoneNumber"?(n(),s("div",J,[c(" 为了"),i("div",K,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，收集你的手机号。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Address"?(n(),s("div",Q,[c(" 开发者收集你的地址，用于"),i("div",Y,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Record"?(n(),s("div",ee,[c(" 为了"),i("div",le,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，访问你的麦克风。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Contact"?(n(),s("div",oe,[c(" 开发者使用你的通讯录（仅写入）权限，用于"),i("div",te,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="EXOrderInfo"?(n(),s("div",ae,[c(" 开发者收集你的订单信息，用于"),i("div",ce,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="EXUserOpLog"?(n(),s("div",re,[c(" 开发者收集你的操作日志，用于"),i("div",ne,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="BlueTooth"?(n(),s("div",se,[c(" 开发者访问你的蓝牙，用于"),i("div",pe,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="MessageFile"?(n(),s("div",ie,[c(" 开发者收集你选中的文件，用于"),i("div",de,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Compass"?(n(),s("div",me,[c(" 开发者调用你的磁场传感器，用于"),i("div",ue,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Clipboard"?(n(),s("div",_e,[c(" 开发者读取你的剪切板，用于"),i("div",ve,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="DeviceMotion"?(n(),s("div",ye,[c(" 开发者调用你的方向传感器，用于"),i("div",ke,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="ChooseLocation"?(n(),s("div",he,[c(" 开发者获取你选择的位置信息，用于"),i("div",xe,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="CalendarWriteOnly"?(n(),s("div",Ve,[c(" 开发者使用你的日历（仅写入）权限，用于"),i("div",Ce,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="AlbumWriteOnly"?(n(),s("div",be,[c(" 为了"),i("div",Ue,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c(" ，开发者将在获取你的明示同意后，使用你的相册（仅写入）权限。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="EXUserPublishContent"?(n(),s("div",fe,[c(" 开发者收集你的发布内容，用于"),i("div",ge,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="DeviceInfo"?(n(),s("div",Pe,[c(" 开发者收集你的设备信息，用于"),i("div",we,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Album"?(n(),s("div",Re,[c(" 开发者收集你选中的照片或视频信息，用于"),i("div",$e,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Invoice"?(n(),s("div",Ee,[c(" 开发者收集你的发票信息，用于"),i("div",Le,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="RunData"?(n(),s("div",Ie,[c(" 为了"),i("div",Ae,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，收集你的微信运动步数。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Camera"?(n(),s("div",De,[c(" 为了"),i("div",Xe,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，访问你的摄像头。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="EXUserFollowAcct"?(n(),s("div",Ne,[c(" 开发者收集你的所关注账号，用于"),i("div",Oe,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="EXIDNumber"?(n(),s("div",Te,[c(" 开发者收集你的身份证号码，用于"),i("div",Be,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="LicensePlate"?(n(),s("div",Fe,[c(" 为了"),i("div",Se,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),c("，开发者将在获取你的明示同意后，收集你的车牌号。 "),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Email"?(n(),s("div",We,[c(" 开发者收集你的邮箱，用于"),i("div",Me,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Accelerometer"?(n(),s("div",Ge,[c(" 开发者调用你的加速传感器，用于"),i("div",qe,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0),r.privacy_key=="Gyroscope"?(n(),s("div",Ze,[c(" 开发者调用你的陀螺仪传感器，用于"),i("div",je,[a(u,{modelValue:e(t)[l].privacy_text,"onUpdate:modelValue":o=>e(t)[l].privacy_text=o,placeholder:e(p)("settingPlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),a(_,{name:"element Remove",onClick:o=>m(l),color:"red",class:"cursor-pointer"},null,8,["onClick"])])):d("",!0)]))),256)),a(O,{modelValue:h.value,"onUpdate:modelValue":y[3]||(y[3]=r=>h.value=r),title:e(p)("settingTypeTitle"),width:"500px","destroy-on-close":!0},{footer:k(()=>[i("span",ze,[a(U,{onClick:y[1]||(y[1]=r=>h.value=!1)},{default:k(()=>[c(b(e(p)("cancel")),1)]),_:1}),a(U,{type:"primary",onClick:y[2]||(y[2]=r=>L())},{default:k(()=>[c(b(e(p)("confirm")),1)]),_:1})])]),default:k(()=>[a(N,{modelValue:x.value,"onUpdate:modelValue":y[0]||(y[0]=r=>x.value=r)},{default:k(()=>[(n(!0),s(V,null,g(E.value,(r,l)=>(n(),s(V,null,[D(r.privacy_key)?d("",!0):(n(),B(X,{key:0,label:r.privacy_key},{default:k(()=>[c(b(r.privacy_text),1)]),_:2},1032,["label"]))],64))),256))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])],64)}}});export{tl as _};
