<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js?aea0c61c"></script>
    <style type="text/css">
        .wrapper {
            width: 600px;
            padding: 10px;
            height: 352px;
            overflow: hidden;
            position: relative;
            border-bottom: 1px solid #d7d7d7
        }

        .wrapper .image-info {
            display: flex;
            align-items: center;
        }

        .wrapper .image-info .input {
            flex-grow: 1;
            display: flex;
        }

        .wrapper .image-info .input input {
            display: block;
            border: 1px solid #CCC;
            height: 23px;
            border-radius: 3px;
            flex-grow: 1;
            outline: none;
        }

        .wrapper .image-info .copy {
            width: 60px;
            cursor: pointer;
            margin: 0 5px;
        }

        .wrapper .image-info .copy div {
            height: 30px;
            font-size: 12px;
            line-height: 28px;
            cursor: pointer;
            border-radius: 4px;
            text-align: center;
            background-color: #F8F8F8;
            border: 1px solid #EEE;
        }

        .wrapper .image-info .action {
            width: 100px;
            cursor: pointer;
        }

        .wrapper .image-info .action .save-from-local {
            position: relative;
            height: 30px;
            font-size: 12px;
            line-height: 28px;
            cursor: pointer;
            border-radius: 4px;
            text-align: center;
            background-color: #F8F8F8;
            border: 1px solid #EEE;
        }

        .wrapper .image-info .action .save-from-local input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            cursor: pointer;
        }

        .wrapper .image-manual {
            background: #EEE;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            line-height: 2;
        }

        .wrapper .image-manual .title {
            font-weight: bold;
            font-size: 120%;
        }

        .wrapper .image-manual .body {
        }
    </style>
</head>
<body>
<div class="wrapper">
    <div class="image-info">
        <div class="input">
            <input type="text" id="fileUrl"/>
        </div>
        <div class="copy">
            <div id="copyButton">复制路径</div>
        </div>
        <div class="action">
            <div class="save-from-local">
                <div>本地选择保存</div>
                <input type="file" id="saveFile" multiple=true/>
            </div>
        </div>
    </div>
    <div class="image-tip"></div>
    <div class="image-manual">
        <div class="title">
            Windows使用教程
        </div>
        <div class="body">
            <p>1、点击复制地址按钮</p>
            <p>2、点击本地选择文件，粘贴剪切板的路径到文件选择路径</p>
            <p>3、点击确定</p>
        </div>
        <div class="title">
            Mac使用教程
        </div>
        <div class="body">
            <p>1、点击复制地址按钮</p>
            <p>2、点击本地选择文件，按快捷 Command+Shift+G ，粘贴剪切板的路径到文件选择路径</p>
            <p>3、点击确定</p>
        </div>
    </div>
</div>
<script src="../../third-party/jquery-1.10.2.js?628072e7"></script>
<script src="../../third-party/webuploader/webuploader.js?f37088cc"></script>
<script type="text/javascript" src="../../third-party/clipboard/clipboard.js?555edf0a"></script>
<script type="text/javascript" src="wordimage.js?f4efe813"></script>
<script type="text/javascript">
    editor.setOpt({
        wordImageFieldName: "upfile",
        compressSide: 0,
        maxImageSideLength: 900
    });
    //全局变量
    var imageUrls = [],          //用于保存从服务器返回的图片信息数组
        selectedImageCount = 0,  //当前已选择的但未上传的图片数量
        optImageUrl = editor.getActionUrl(editor.getOpt('imageActionName')),
        optImageFieldName = editor.getOpt('imageFieldName'),
        optImageCompressBorder = editor.getOpt('imageCompressEnable') ? editor.getOpt('imageCompressBorder') : null,
        maxSize = editor.getOpt('imageMaxSize') / 1024 / 1024,
        extension = editor.getOpt('imageAllowFiles').join(';').replace(/\./g, '*.');

    /* 添加额外的GET参数 */
    var params = utils.serializeParam(editor.queryCommandValue('serverparam')) || '',
        urlWidthParams = optImageUrl + (optImageUrl.indexOf('?') == -1 ? '?' : '&') + params;

    var uploader,
        actionUrl = editor.getActionUrl(editor.getOpt('imageActionName')),
        acceptExtensions = (editor.getOpt('imageAllowFiles') || []).join('').replace(/\./g, ',').replace(/^[,]/, ''),
        imageMaxSize = editor.getOpt('imageMaxSize'),
        imageCompressBorder = editor.getOpt('imageCompressBorder');
    uploader = WebUploader.create({
        accept: {
            title: 'Images',
            extensions: acceptExtensions,
            mimeTypes: 'image/*'
        },
        swf: '../../third-party/webuploader/Uploader.swf',
        server: actionUrl,
        fileVal: editor.getOpt('imageFieldName'),
        duplicate: true,
        fileSingleSizeLimit: imageMaxSize,    // 默认 2 M
        threads: 1,
        compress: editor.getOpt('imageCompressEnable') ? {
            width: imageCompressBorder,
            height: imageCompressBorder,
            // 图片质量，只有type为`image/jpeg`的时候才有效。
            quality: 90,
            // 是否允许放大，如果想要生成小图的时候不失真，此选项应该设置为false.
            allowMagnify: false,
            // 是否允许裁剪。
            crop: false,
            // 是否保留头部meta信息。
            preserveHeaders: true
        } : false
    });
    uploader.on('uploadSuccess', function (file, res) {
        if ('state' in res) {
            if (res.state === 'SUCCESS') {
                if (res.url) {
                    imageUrls.push({
                        name: file.name,
                        url: res.url,
                        title: '',
                    });
                    $('.image-tip').html('<span style="color:#139213;">图片' + imageUrls.length + '(' + file.name + ')' + '转存成功</span>');
                } else {
                    $('.image-tip').html('上传失败:' + JSON.stringify(res))
                }
                // 触发上传附件事件
                editor.fireEvent("uploadsuccess", {
                    res: res,
                    type: 'image'
                });
            } else {
                $('.image-tip').html('上传失败:' + JSON.stringify(res))
            }
        } else {
            $('.image-tip').html('上传失败:' + JSON.stringify(res))
        }
    });
    uploader.on('uploadFinished', function () {
        $('.image-tip').html('<span style="color:#139213;">转存成功，请提交确认</span>');
    });
    utils.domReady(function () {
        var options = {};
        var callbacks = {};
        wordImage.init(options, callbacks);
    });
</script>

</body>
</html>
