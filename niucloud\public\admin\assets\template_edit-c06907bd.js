import{d as ee,y as te,f as ae,r as U,n as le,l as oe,q as c,h as s,c as _,e as u,w as d,u as b,aT as se,Z as ne,s as x,a as v,t as h,F as T,W as F,i as O,C as V,x as re,b1 as ue,$ as R,aU as de,a9 as pe,L as ie,M as me,au as ce,av as _e,aL as ve,aZ as fe,a4 as ye,a1 as be,N as ke,E as ge,a3 as xe}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                          *//* empty css                    *//* empty css                       *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                *//* empty css                       */import{_ as he}from"./preview-goods-order-e129af73.js";import{b as Ve,i as we,j as Ee,k as Ue}from"./printer-342d98c9.js";import"./poster-97e95d37.js";import"./browser-a1ac24ac.js";import"./_plugin-vue_export-helper-c27b6911.js";const Te={class:"main-container"},Ce={class:"flex"},Ne={class:"flex-1 mr-[20px] bg-[#fff]"},Pe={class:"panel-title !text-sm"},Fe={class:"panel-title !text-sm"},je={class:"panel-title !text-sm"},Oe={class:"flex"},Se={class:"leading-[30px] w-[50px] text-center text-[#707070] bg-[#d7d7d7] border-1 border-solid border-[#ededed]"},Be={key:4,class:"flex mr-[30px]"},Re=v("div",{class:"leading-[30px] w-[50px] text-center text-[#707070] bg-[#d7d7d7] border-1 border-solid border-[#ededed]"},"字号",-1),ze={key:5,class:"flex mr-[30px]"},De=v("div",{class:"leading-[30px] w-[50px] text-center text-[#707070] bg-[#d7d7d7] border-1 border-solid border-[#ededed]"},"粗细",-1),Le={key:0,class:"text-[12px] text-[#b2b2b2] mt-[10px]"},Me={class:"panel-title !text-sm"},We={class:"fixed-footer-wrap"},$e={class:"fixed-footer"},ct=ee({__name:"template_edit",setup(qe){const z=te(),Z=ae(),H=z.meta.title,C=U(!1),N=U(!0),A={template_id:z.query.template_id||0,template_type:"",template_name:"",value:{}},e=le({...A}),D=U(),J=oe(()=>({template_name:[{required:!0,message:c("templateNamePlaceholder"),trigger:"blur"}],template_type:[{required:!0,message:c("templateTypePlaceholder"),trigger:"blur"}]})),L=Object.assign({}),M=Object.assign({"/src/addon/shop/views/printer/components/preview-goods-order.vue":he});M&&Object.assign(L,M);const W={};for(const[r,o]of Object.entries(L)){const n=r.split("/").pop().split(".")[0];W[n]=o.default}const $=U(""),f=U([]),w=U([]);(async()=>{await Ve({}).then(r=>{r.data&&r.data.length&&(f.value=r.data,q(f.value[0].key,Boolean(parseInt(e.template_id)))),e.template_id||(N.value=!1)}),e.template_id&&we(e.template_id).then(r=>{let o=r.data;o&&Object.keys(o).length?(Object.keys(e).forEach(l=>{if(l=="value")for(let n in e[l])Object.assign(e[l][n],o[l][n]);else e[l]=o[l]}),N.value=!1):(R({type:"warning",duration:1500,message:c("printTemplateEmpty")}),setTimeout(()=>{j(),N.value=!1},2e3))})})();const q=(r,o=!1)=>{for(let l=0;l<f.value.length;l++)if(f.value[l].key==r){e.template_type=f.value[l].key,$.value=f.value[l].path,w.value=f.value[l].template;break}for(let l in e.value)delete e.value[l];K()},K=()=>{for(let r=0;r<w.value.length;r++){let o=w.value[r];e.value[o.key]={};for(let l=0;l<o.list.length;l++){let n=o.list[l];e.value[o.key][n.key]={type:n.type,value:n.value,status:n.status,fontSize:n.fontSize,fontWeight:n.fontWeight}}}},Q=async r=>{if(N.value||!r)return;if(f.value.length==0){R({type:"warning",message:c("printTypeEmpty")});return}let o=e.template_id?Ee:Ue;await r.validate(async l=>{if(l){let n=!1,E="";for(let y=0;y<w.value.length;y++){let m=w.value[y],k=!1;for(let g=0;g<m.list.length;g++){let i=m.list[g];if(e.value[m.key][i.key].status!=0){if(i.type=="input"){if(e.value[m.key][i.key].value==""){n=!0,E=`请输入${i.label}`,k=!0;break}}else if(i.type=="select"&&e.value[m.key][i.key].value==""){n=!0,E=`${i.label}未设置[${i.text}]`,k=!0;break}}}if(k)break}if(n){R({type:"warning",message:E});return}for(let y=0;y<w.value.length;y++){let m=w.value[y];for(let k=0;k<m.list.length;k++){let g=m.list[k];g.type=="checkbox"&&(e.value[m.key][g.key].value.length?e.value[m.key][g.key].status=1:e.value[m.key][g.key].status=0)}}if(C.value)return;C.value=!0,o(e).then(y=>{C.value=!1,e.template_id||j()}).catch(y=>{C.value=!1})}})},j=()=>{Z.push("/printer/template/list")};return(r,o)=>{const l=de,n=pe,E=ie,S=me,y=ce,m=_e,k=ve,g=fe,i=ye,B=be,X=ke,G=ge,Y=xe;return s(),_("div",Te,[u(n,{class:"card !border-none mb-[15px]",shadow:"never"},{default:d(()=>[u(l,{content:b(H),icon:b(se),onBack:j},null,8,["content","icon"])]),_:1}),ne((s(),x(X,{class:"page-form",model:e,rules:b(J),"label-width":"150px",ref_key:"formRef",ref:D},{default:d(()=>[v("div",Ce,[v("div",Ne,[u(n,{class:"box-card !border-none",shadow:"never"},{default:d(()=>[v("h3",Pe,h(b(c)("templateInfoLabel")),1),u(S,{label:b(c)("templateName"),prop:"template_name"},{default:d(()=>[u(E,{modelValue:e.template_name,"onUpdate:modelValue":o[0]||(o[0]=a=>e.template_name=a),modelModifiers:{trim:!0},clearable:"",placeholder:b(c)("templateNamePlaceholder"),class:"input-width",maxlength:"20"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),f.value.length?(s(),x(S,{key:0,label:b(c)("templateType"),prop:"template_type"},{default:d(()=>[u(m,{modelValue:e.template_type,"onUpdate:modelValue":o[1]||(o[1]=a=>e.template_type=a)},{default:d(()=>[(s(!0),_(T,null,F(f.value,a=>(s(),x(y,{key:a.key,label:a.key,onChange:q},{default:d(()=>[O(h(a.title),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):V("",!0)]),_:1}),f.value.length?(s(),x(n,{key:0,class:"box-card !border-none",shadow:"never"},{default:d(()=>[v("h3",Fe,h(b(c)("templateEditLabel")),1),(s(!0),_(T,null,F(w.value,a=>(s(),_("div",{key:a.key,class:"bg-[#f8f8f9] mb-[20px] py-[20px] px-[40px] text-[14px]"},[v("h4",je,h(a.title),1),(s(!0),_(T,null,F(a.list,(t,I)=>(s(),_("div",{key:t.key,class:"ml-[30px]",style:re({"margin-bottom":a.list.length==I+1?"0":"20px"})},[v("div",Oe,[t.label?(s(),x(k,{key:0,modelValue:e.value[a.key][t.key].status,"onUpdate:modelValue":p=>e.value[a.key][t.key].status=p,label:t.label,value:t.status,"true-value":1,"false-value":0,class:"w-[180px] mr-[10px]",disabled:t.disabled},null,8,["modelValue","onUpdate:modelValue","label","value","disabled"])):V("",!0),t.type=="input"?(s(),x(E,{key:1,modelValue:e.value[a.key][t.key].value,"onUpdate:modelValue":p=>e.value[a.key][t.key].value=p,modelModifiers:{trim:!0},clearable:"",placeholder:"请输入"+(t.placeholder?t.placeholder:t.label),class:"input-width mr-[30px]",maxlength:"32"},null,8,["modelValue","onUpdate:modelValue","placeholder"])):V("",!0),t.type=="checkbox"?(s(),x(g,{key:2,modelValue:e.value[a.key][t.key].value,"onUpdate:modelValue":p=>e.value[a.key][t.key].value=p,class:"mr-[30px]"},{default:d(()=>[(s(!0),_(T,null,F(t.list,(p,P)=>(s(),x(k,{label:P,key:P,disabled:t.disabled},{default:d(()=>[O(h(p),1)]),_:2},1032,["label","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):V("",!0),t.type=="select"?(s(),_(T,{key:3},[v("div",Se,h(t.text),1),u(B,{modelValue:e.value[a.key][t.key].value,"onUpdate:modelValue":p=>e.value[a.key][t.key].value=p,class:"!w-[130px] mr-[30px]"},{default:d(()=>[(s(!0),_(T,null,F(t.list,(p,P)=>(s(),x(i,{key:P,label:p,value:P},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])],64)):V("",!0),t.fontSize?(s(),_("div",Be,[Re,u(B,{modelValue:e.value[a.key][t.key].fontSize,"onUpdate:modelValue":p=>e.value[a.key][t.key].fontSize=p,class:"!w-[130px]"},{default:d(()=>[u(i,{label:"小",value:"normal"}),u(i,{label:"大",value:"big"})]),_:2},1032,["modelValue","onUpdate:modelValue"])])):V("",!0),t.fontWeight?(s(),_("div",ze,[De,u(B,{modelValue:e.value[a.key][t.key].fontWeight,"onUpdate:modelValue":p=>e.value[a.key][t.key].fontWeight=p,class:"!w-[130px]"},{default:d(()=>[u(i,{label:"正常",value:"normal"}),u(i,{label:"加粗",value:"bold"})]),_:2},1032,["modelValue","onUpdate:modelValue"])])):V("",!0)]),t.remark?(s(),_("div",Le,h(t.remark),1)):V("",!0)],4))),128))]))),128))]),_:1})):V("",!0)]),u(n,{class:"box-card !border-none w-[450px]",shadow:"never"},{default:d(()=>[v("h3",Me,h(b(c)("preview")),1),(s(),x(ue(W[$.value]),{value:e.value},null,8,["value"]))]),_:1})])]),_:1},8,["model","rules"])),[[Y,N.value]]),v("div",We,[v("div",$e,[u(G,{type:"primary",loading:C.value,onClick:o[2]||(o[2]=a=>Q(D.value))},{default:d(()=>[O(h(b(c)("save")),1)]),_:1},8,["loading"]),u(G,{onClick:o[3]||(o[3]=a=>j())},{default:d(()=>[O(h(b(c)("cancel")),1)]),_:1})])])])}}});export{ct as default};
