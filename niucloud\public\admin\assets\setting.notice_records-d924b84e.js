const e="站点id",c="消息模板",t="请选择消息模板",o="消息类型",s="消息详情",i="通知的用户id",n="消息的会员id",a="接收会员",l="手机/OPENID",r="消息内容",d="点击次数",h="接收人",m="访问次数",P="发送时间",y="开始时间",p="结束时间",T="短信",I="微信小程序",D="微信公众号",k="请选择消息类型",v="请输入通知的用户id",u="请输入消息的会员id",b="请输入接收人用户昵称或姓名",K="请输入接收人手机号/openid",f="请输入消息数据",g="请输入点击次数",N="请输入访问次数",w="请输入访问时间",C="请输入消息时间",V="会员消息",R="消息模版",_="发送记录详情",x="短信类型",E="平台消息",O={siteId:e,noticeKey:c,noticeKeyPlaceholder:t,noticeType:o,noticeInfo:s,uid:i,memberId:n,nickname:a,receiver:l,noticeData:r,isClick:d,searchReceiver:h,isVisit:m,createTime:P,startDate:y,endDate:p,sms:T,weapp:I,wechat:D,noticeTypePlaceholder:k,uidPlaceholder:v,memberIdPlaceholder:u,nicknamePlaceholder:b,receiverPlaceholder:K,noticeDataPlaceholder:f,isClickPlaceholder:g,isVisitPlaceholder:N,visitTimePlaceholder:w,createTimePlaceholder:C,buyerNotice:V,messageKey:R,messageInfo:_,smsType:x,sellerNotice:E};export{V as buyerNotice,P as createTime,C as createTimePlaceholder,O as default,p as endDate,d as isClick,g as isClickPlaceholder,m as isVisit,N as isVisitPlaceholder,n as memberId,u as memberIdPlaceholder,_ as messageInfo,R as messageKey,a as nickname,b as nicknamePlaceholder,r as noticeData,f as noticeDataPlaceholder,s as noticeInfo,c as noticeKey,t as noticeKeyPlaceholder,o as noticeType,k as noticeTypePlaceholder,l as receiver,K as receiverPlaceholder,h as searchReceiver,E as sellerNotice,e as siteId,T as sms,x as smsType,y as startDate,i as uid,v as uidPlaceholder,w as visitTimePlaceholder,I as weapp,D as wechat};
