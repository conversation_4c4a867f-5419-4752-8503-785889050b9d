import{d as de,r as x,h as n,s as b,w as p,Z as pe,c as i,e as r,C as d,u as a,q as l,a as s,t,F as V,W as O,B as ce,i as w,af as I,a6 as q,ap as _e,aq as ue,M as xe,aQ as ve,aR as me,ag as fe,ah as ye,N as he,aa as be,a9 as ge,bS as ke,a3 as De}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                 *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                        *//* empty css               *//* empty css               */import"./el-form-item-4ed993c7.js";/* empty css                    */import{n as we,p as Fe,q as Ce}from"./order-caf8547a.js";import{p as Te}from"./printer-342d98c9.js";import{_ as Ee}from"./delivery-action.vue_vue_type_style_index_0_lang-422f15f0.js";import{_ as Ne}from"./order-notes.vue_vue_type_style_index_0_lang-368924cb.js";import{_ as Ae}from"./order-edit-address.vue_vue_type_style_index_0_lang-f7c7b26a.js";import{_ as $e}from"./delivery-package.vue_vue_type_style_index_0_lang-7dee34ef.js";import{_ as Me}from"./adjust-money.vue_vue_type_style_index_0_lang-6e7e535f.js";import{_ as Be}from"./electronic-sheet-print.vue_vue_type_style_index_0_lang-06d52af2.js";import{_ as je}from"./shop-active-refund.vue_vue_type_style_index_0_lang-8da3f595.js";/* empty css                  *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                 */import"./delivery-ef62b210.js";import"./weapp-e4b43176.js";import"./electronic_sheet-35199d4d.js";import"./lodop-13c94777.js";import"./qqmap-011f3cfb.js";const Ve={class:"main-container"},Re={key:0},Pe={class:"input-width"},Se={class:"input-width"},Oe={class:"input-width"},Ie={class:"input-width"},qe={class:"input-width"},Ge={key:0},Le={class:"input-width"},Ue={class:"input-width"},We={class:"input-width"},ze={key:1},Qe={class:"input-width"},Ze={class:"input-width"},He={class:"input-width"},Je={class:"input-width"},Ke={class:"input-width line-feed"},Xe={class:"input-width line-feed"},Ye={key:1},et={class:"mb-[20px]"},tt={class:"ml-[30px] text-[14px] mr-[20px]"},st={class:"text-[14px]"},lt={class:"flex mt-[10px]"},at={key:8,class:"flex"},ot=["onClick"],nt=["onClick"],it={class:"flex ml-[30px] mt-[15px]"},rt={class:"text-[14px] text-[#ff7f5b]"},dt={class:"ml-[10px]"},pt={class:"text-[14px] text-[#a4a4a4]"},ct={class:"text-[14px] text-[#a4a4a4]"},_t={class:"text-[14px] text-[#a4a4a4]"},ut={key:2},xt={class:"flex"},vt={class:"flex items-center shrink-0"},mt=["src"],ft={class:"flex flex-col items-start"},yt={class:"multi-hidden text-[14px]"},ht={class:"text-[12px] text-[#999]"},bt={key:0,class:"px-[4px] text-[12px] text-[#fff] rounded-[4px] bg-primary leading-[18px]"},gt={class:"flex flex-col"},kt={key:0},Dt={key:0},wt={key:1,class:"text-[13px]"},Ft={key:2,class:"text-[13px] mt-[5px]"},Ct={key:0},Tt={class:"text-[#999]"},Et={key:1},Nt={key:3,class:"text-[13px] mt-[5px]"},At={class:"py-[12px] px-[16px] border-b border-color flex justify-end"},$t={class:"w-[310px] flex flex-col text-right"},Mt={class:"flex mb-[10px]"},Bt={class:"text-base flex-1"},jt={class:"text-base flex-1 pl-[30px]"},Vt={key:0,class:"text-[14px]"},Rt={key:0},Pt={key:1,class:"text-[14px]"},St={key:0,class:"flex mb-[10px]"},Ot={class:"text-base flex-1"},It={class:"text-base flex-1 pl-[30px]"},qt={key:1,class:"flex mb-[10px]"},Gt={class:"text-base flex-1"},Lt={class:"text-base flex-1 pl-[30px]"},Ut={class:"flex mb-[10px]"},Wt={class:"text-base flex-1"},zt={class:"text-base flex-1 pl-[30px]"},Qt={class:"flex mb-[10px]"},Zt={class:"text-base flex-1"},Ht={class:"text-base flex-1 pl-[30px]"},Jt={class:"flex"},Kt={class:"text-base flex-1"},Xt={class:"text-base flex-1 pl-[30px] text-[red]"},Yt={key:0,class:"text-[14px]"},es={key:0},ts={key:1,class:"text-[14px]"},ss={key:3},ls={class:"mb-[100px] px-[20px]",style:{"min-height":"100px"}},as={class:"mr-[20px] min-w-[71px]"},os={class:"leading-[1] text-[14px] w-[100px] flex justify-end"},ns={class:"leading-[1] text-[14px] w-[100px] flex justify-end mt-[15px]"},is=s("div",{class:"w-[16px] h-[16px] flex items-center bg-[#D1EBFF] border-[1px] border-[#0091FF] rounded-[999px]"},[s("div",{class:"w-[8px] h-[8px] mx-auto bg-[#0091FF] rounded-[999px]"})],-1),rs={key:0,class:"w-[2px] h-[50px] bg-[#D1EBFF] mx-auto"},ds={class:"leading-[1] ml-[20px] text-[14px]"},ps={class:"leading-[1] ml-[20px] text-[14px] mt-[15px]"},cs={class:"ml-[10px]"},Xs=de({__name:"order-detail",emits:["load","close-event"],setup(_s,{expose:G,emit:R}){const g=x(!1),f=x(!1),D=x(!1);let L="订单详情",v="";const U=_=>{g.value=!1,R("close-event")},m=x("order"),e=x(null),W=async()=>{f.value=!0,v?await Ce(v).then(({data:_})=>{e.value=_;let c=0;e.value.order_goods.forEach((h,S)=>{h.is_enable_refund==1&&c++}),e.value.is_refund_show=c>0,f.value=!1}).catch(()=>{}):f.value=!1},z=()=>{I.confirm(l("orderCloseTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{we(v).then(()=>{M(v)})})},F=x(null),Q=()=>{F.value.setFormData(e.value),F.value.showDialog=!0},C=x(null),Z=()=>{C.value.setFormData(e.value),C.value.showDialog=!0},T=x(null),H=()=>{T.value.setFormData(e.value),T.value.showDialog=!0},J=()=>{I.confirm(l("orderFinishTips"),l("warning"),{confirmButtonText:l("confirm"),cancelButtonText:l("cancel"),type:"warning"}).then(()=>{Fe(v).then(()=>{M(v)})})},E=x(null),P=(_,c)=>{E.value.setFormData(_,c),E.value.showDialog=!0},N=x(null),K=()=>{let _={print_type:"single"};Object.assign(_,q(e.value)),N.value.setFormData(_),N.value.showDialog=!0},A=x(null),X=async()=>{let _=q(e.value);A.value.showDialog=!0,A.value.setFormData(_)},Y=()=>{e.value.order_id&&(D.value||(D.value=!0,Te({type:"shopGoodsOrder",trigger:"manual",business:{order_id:e.value.order_id}}).then(_=>{D.value=!1}).catch(()=>{D.value=!1})))},$=x(null),ee=()=>{$.value.setFormData(e.value),$.value.showDialog=!0},M=async(_=null)=>{v=_.id,e.value=null,m.value="order",W()},y=()=>{g.value=!1,R("load")};return G({showDialog:g,setFormData:M}),(_,c)=>{const h=_e,S=ue,u=xe,B=ve,te=me,j=fe,se=ye,le=he,ae=be,oe=ge,ne=ke,ie=De;return n(),b(ne,{modelValue:g.value,"onUpdate:modelValue":c[7]||(c[7]=o=>g.value=o),title:a(L),direction:"rtl","before-close":U,class:"detail-drawer"},{default:p(()=>[pe((n(),i("div",Ve,[r(S,{modelValue:m.value,"onUpdate:modelValue":c[0]||(c[0]=o=>m.value=o),class:"pb-[10px]",onTabChange:_.tabChange},{default:p(()=>[r(h,{label:"订单信息",name:"order"}),r(h,{label:"订单状态",name:"state"}),r(h,{label:"商品信息",name:"goods"}),e.value&&e.value.order_log&&e.value.order_log.length>0?(n(),b(h,{key:0,label:"订单日志",name:"log"})):d("",!0)]),_:1},8,["modelValue","onTabChange"]),f.value?d("",!0):(n(),b(le,{key:0,model:e.value,"label-width":"100px",ref:"formRef",class:"page-form","label-position":"left"},{default:p(()=>[m.value=="order"?(n(),i("div",Re,[r(te,{class:"row-bg px-[30px] mb-[20px]",gutter:20},{default:p(()=>[r(B,{span:8},{default:p(()=>[r(u,{label:a(l)("orderDetailNo")},{default:p(()=>[s("div",Pe,t(e.value.order_no),1)]),_:1},8,["label"]),r(u,{label:a(l)("orderDetailForm")},{default:p(()=>[s("div",Se,t(e.value.order_from_name),1)]),_:1},8,["label"]),e.value&&e.value.out_trade_no?(n(),b(u,{key:0,label:a(l)("orderDetailoutTradeNo")},{default:p(()=>[s("div",Oe,t(e.value.out_trade_no),1)]),_:1},8,["label"])):d("",!0),e.value.pay?(n(),b(u,{key:1,label:a(l)("payWay")},{default:p(()=>[s("div",Ie,t(e.value.pay.type_name),1)]),_:1},8,["label"])):d("",!0)]),_:1}),r(B,{span:8},{default:p(()=>[r(u,{label:a(l)("deliveryType")},{default:p(()=>[s("div",qe,t(e.value.delivery_type_name),1)]),_:1},8,["label"]),e.value.delivery_type=="express"||e.value.delivery_type=="local_delivery"?(n(),i("div",Ge,[r(u,{label:a(l)("orderDetailTakerName")},{default:p(()=>[s("div",Le,t(e.value.taker_name),1)]),_:1},8,["label"]),r(u,{label:a(l)("orderDetailTakerMobile")},{default:p(()=>[s("div",Ue,t(e.value.taker_mobile),1)]),_:1},8,["label"]),r(u,{label:a(l)("orderDetailTakerFullAddress")},{default:p(()=>[s("div",We,t(e.value.taker_full_address),1)]),_:1},8,["label"])])):d("",!0),e.value.delivery_type=="store"?(n(),i("div",ze,[r(u,{label:a(l)("storeName")},{default:p(()=>[s("div",Qe,t(e.value.store.store_name),1)]),_:1},8,["label"]),r(u,{label:a(l)("storeAddress")},{default:p(()=>[s("div",Ze,t(e.value.store.full_address),1)]),_:1},8,["label"]),r(u,{label:a(l)("storeMobile")},{default:p(()=>[s("div",He,t(e.value.store.store_mobile),1)]),_:1},8,["label"]),r(u,{label:a(l)("tradeTime")},{default:p(()=>[s("div",Je,t(e.value.store.trade_time),1)]),_:1},8,["label"])])):d("",!0)]),_:1}),r(B,{span:8},{default:p(()=>[r(u,{label:a(l)("memberRemark")},{default:p(()=>[s("div",Ke,t(e.value.member_remark??"--"),1)]),_:1},8,["label"]),r(u,{label:a(l)("notes")},{default:p(()=>[s("div",Xe,t(e.value.shop_remark??"--"),1)]),_:1},8,["label"])]),_:1})]),_:1})])):d("",!0),m.value=="state"?(n(),i("div",Ye,[s("div",et,[s("p",null,[s("span",tt,t(a(l)("orderStatus"))+"：",1),s("span",st,t(e.value.status_name.name),1)]),s("div",lt,[s("span",{class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#ff7f5b] bg-[#fff0e5] cursor-pointer",onClick:H},t(a(l)("notes")),1),e.value.status==2?(n(),i("span",{key:0,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:Z},t(a(l)("delivery")),1)):d("",!0),e.value.status==1?(n(),i("span",{key:1,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:z},t(a(l)("close")),1)):d("",!0),e.value.status==1?(n(),i("span",{key:2,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:Q},t(a(l)("editPrice")),1)):d("",!0),e.value.status==3?(n(),i("span",{key:3,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:J},t(a(l)("finish")),1)):d("",!0),e.value.delivery_type=="express"&&e.value.status==3?(n(),i("span",{key:4,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:K},t(a(l)("electronicSheetPrintTitle")),1)):d("",!0),e.value.delivery_type=="virtual"&&(e.value.status==2||e.value.status==3||e.value.status==5)?(n(),i("span",{key:5,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:Y},t(a(l)("printTicket")),1)):d("",!0),e.value.status==1&&e.value.delivery_type!="virtual"&&e.value.activity_type!="giftcard"?(n(),i("span",{key:6,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:X},t(a(l)("editAddress")),1)):d("",!0),e.value.is_refund_show&&e.value.status!=1&&e.value.status!=-1?(n(),i("span",{key:7,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#5c96fc] bg-[#ebf3ff] cursor-pointer",onClick:ee},t(a(l)("voluntaryRefund")),1)):d("",!0),e.value.order_delivery?(n(),i("div",at,[(n(!0),i(V,null,O(e.value.order_delivery,(o,k)=>(n(),i(V,{key:k},[o.delivery_type=="express"&&o.sub_delivery_type=="express"?(n(),i("span",{key:0,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#ff7f5b] bg-[#fff0e5] cursor-pointer",onClick:re=>P(o.id,e.value.taker_mobile)},t(a(l)("package"))+t(k+1),9,ot)):d("",!0),o.delivery_type=="express"&&o.sub_delivery_type=="none_express"?(n(),i("span",{key:1,class:"text-[14px] px-[15px] py-[5px] ml-[30px] text-[#ff7f5b] bg-[#fff0e5] cursor-pointer",onClick:re=>P(o.id,e.value.taker_mobile)},t(a(l)("noLogisticsRequired")),9,nt)):d("",!0)],64))),128))])):d("",!0)]),s("div",it,[s("span",rt,t(a(l)("remind"))+"：",1),s("div",dt,[s("p",pt,t(a(l)("remindTips1")),1),s("p",ct,t(a(l)("remindTips2")),1),s("p",_t,t(a(l)("remindTips3")),1)])])])])):d("",!0),m.value=="goods"?(n(),i("div",ut,[r(se,{data:e.value.order_goods,size:"large"},{default:p(()=>[r(j,{label:a(l)("orderDetailGoodsName"),align:"left",width:"300"},{default:p(({row:o})=>[s("div",xt,[s("div",vt,[s("img",{class:"w-[50px] h-[50px] mr-[10px]",src:a(ce)(o.goods_image)},null,8,mt)]),s("div",ft,[s("p",yt,t(o.goods_name),1),s("span",ht,t(o.sku_name),1),o.is_gift==1?(n(),i("span",bt,"赠品")):d("",!0)])])]),_:1},8,["label"]),r(j,{label:a(l)("orderDetailPrice"),"min-width":"50",align:"left"},{default:p(({row:o})=>[s("div",gt,[e.value.activity_type=="exchange"?(n(),i("span",kt,[w(t(o.extend.point)+t(a(l)("point"))+" ",1),parseFloat(o.price)?(n(),i("span",Dt,"+￥"+t(o.price),1)):d("",!0)])):(n(),i("span",wt,"￥"+t(o.price),1)),o.extend&&o.extend.newcomer_price?(n(),i("span",Ft,[parseFloat(o.extend.newcomer_price)&&o.num>1?(n(),i("span",Ct,[w(t(o.num)+t(a(l)("piece")),1),s("span",Tt,"（第1"+t(a(l)("piece"))+"，￥"+t(parseFloat(o.extend.newcomer_price).toFixed(2))+"/"+t(a(l)("piece"))+"；第"+t(o.num>2?"2~"+o.num:"2")+t(a(l)("piece"))+"，￥"+t(parseFloat(o.price).toFixed(2))+"/"+t(a(l)("piece"))+"）",1)])):(n(),i("span",Et,t(o.num)+t(a(l)("piece")),1))])):(n(),i("span",Nt,t(o.num)+t(a(l)("piece")),1))])]),_:1},8,["label"]),r(j,{prop:"num",label:a(l)("detailNum"),"min-width":"50",align:"right"},null,8,["label"])]),_:1},8,["data"]),s("div",At,[s("div",$t,[s("div",Mt,[s("div",Bt,t(a(l)("orderDetailGoodsMoney")),1),s("div",jt,[e.value.activity_type=="exchange"?(n(),i("span",Vt,[w(t(e.value.point)+t(a(l)("point"))+" ",1),parseFloat(e.value.goods_money)?(n(),i("span",Rt,"+￥"+t(e.value.goods_money),1)):d("",!0)])):(n(),i("span",Pt,"￥"+t(e.value.goods_money),1))])]),e.value.coupon_money>0?(n(),i("div",St,[s("div",Ot,t(a(l)("couponMoney")),1),s("div",It,t(e.value.coupon_money),1)])):d("",!0),e.value.manjian_discount_money>0?(n(),i("div",qt,[s("div",Gt,t(a(l)("manjianDiscountMoney")),1),s("div",Lt,t(e.value.manjian_discount_money),1)])):d("",!0),s("div",Ut,[s("div",Wt,t(a(l)("discountMoney")),1),s("div",zt,t(e.value.discount_money),1)]),s("div",Qt,[s("div",Zt,t(a(l)("orderDetailDeliveryMoney")),1),s("div",Ht,t(e.value.delivery_money),1)]),s("div",Jt,[s("div",Kt,t(a(l)("detailOrderMoney")),1),s("div",Xt,[e.value.activity_type=="exchange"?(n(),i("span",Yt,[w(t(e.value.point)+t(a(l)("point"))+" ",1),parseFloat(e.value.order_money)?(n(),i("span",es,"+￥"+t(e.value.order_money),1)):d("",!0)])):(n(),i("span",ts,"￥"+t(e.value.order_money),1))])])])])])):d("",!0),m.value=="log"&&e.value.order_log.length>0?(n(),i("div",ss,[s("div",ls,[(n(!0),i(V,null,O(e.value.order_log,(o,k)=>(n(),i("div",{class:"flex",key:k},[s("div",as,[s("div",os,t(o.create_time&&o.create_time.split(" ")[0]),1),s("div",ns,t(o.create_time&&o.create_time.split(" ")[1]),1)]),s("div",null,[is,k+1!=e.value.order_log.length?(n(),i("div",rs)):d("",!0)]),s("div",null,[s("div",ds,t(o.main_type_name)+t(o.main_name),1),s("div",ps,[s("span",null,t(o.type_name),1),s("span",cs,t(o.content),1)])])]))),128))])])):d("",!0)]),_:1},8,["model"])),!f.value&&!e.value?(n(),b(oe,{key:1,class:"box-card !border-none relative",shadow:"never"},{default:p(()=>[r(ae,{description:a(l)("orderInfoEmpty")},null,8,["description"])]),_:1})):d("",!0),r(Me,{ref_key:"orderAdjustMoneyActionDialog",ref:F,onComplete:c[1]||(c[1]=o=>y())},null,512),r(Ee,{ref_key:"deliveryActionDialog",ref:C,onComplete:c[2]||(c[2]=o=>y())},null,512),r(Ne,{ref_key:"orderNotesDialog",ref:T,onComplete:c[3]||(c[3]=o=>y())},null,512),r($e,{ref_key:"packageDialog",ref:E},null,512),r(Be,{ref_key:"electronicSheetPrintDialog",ref:N,onComplete:c[4]||(c[4]=o=>y())},null,512),r(Ae,{ref_key:"orderEditAddressDialog",ref:A,onComplete:c[5]||(c[5]=o=>y())},null,512),r(je,{ref_key:"shopActiveRefundDialog",ref:$,onComplete:c[6]||(c[6]=o=>y())},null,512)])),[[ie,f.value]])]),_:1},8,["modelValue","title"])}}});export{Xs as default};
