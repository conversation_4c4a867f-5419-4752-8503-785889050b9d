import{d as z,r as b,n as A,aN as H,h as o,c as x,e as d,w as s,i as g,a as t,F as _,W as u,v as m,cB as $,b4 as I,aY as N,ap as T,bL as M,E as S,V as U,aq as L,p as P,g as R}from"./index-30109030.js";/* empty css                    *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  */import"./el-tooltip-4ed993c7.js";import{i as q}from"./index-d2519496.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";const a=h=>(P("data-v-d5b994b9"),h=h(),R(),h),W={class:"min-w-[100px] min-h-[650px] p-[15px] bg-white tab-index mb-[150px]"},G={class:"flex w-full justify-between flex-wrap"},J=["onClick"],K={class:"flex items-center"},Q=a(()=>t("div",{class:"text-[14px]"},"全部",-1)),X=a(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[4px]"},null,-1)),Z=a(()=>t("div",{class:"flex justify-between"},[t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"访客数"),t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"3")],-1)),tt={class:"flex w-full justify-between flex-wrap"},et=["onClick"],st={class:"flex items-center"},at=a(()=>t("div",{class:"text-[14px]"},"全部",-1)),dt=a(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[4px]"},null,-1)),lt=a(()=>t("div",{class:"flex justify-between"},[t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"访客数"),t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"3")],-1)),it={class:"flex w-full justify-between flex-wrap"},ot=["onClick"],xt={class:"flex items-center"},nt=a(()=>t("div",{class:"text-[14px]"},"全部",-1)),ct=a(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[4px]"},null,-1)),pt=a(()=>t("div",{class:"flex justify-between"},[t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"访客数"),t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"3")],-1)),vt={class:"flex w-full justify-between flex-wrap"},rt=["onClick"],ft={class:"flex items-center"},bt=a(()=>t("div",{class:"text-[14px]"},"全部",-1)),_t=a(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[4px]"},null,-1)),ut=a(()=>t("div",{class:"flex justify-between"},[t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"访客数"),t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"3")],-1)),mt=a(()=>t("div",{class:"text-[14px] color-[#333] h-[42px] leading-[42px]"},"自定义时间选择",-1)),ht={class:"flex items-center"},wt=a(()=>t("span",{class:"mr-[10px]"},"选择时间:",-1)),yt={class:"dialog-footer"},Ct={class:"flex w-full justify-between flex-wrap"},gt={class:"flex w-full justify-between flex-wrap"},Vt=["onClick"],kt={class:"flex items-center"},jt=a(()=>t("div",{class:"text-[14px]"},"全部",-1)),Bt=a(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[4px]"},null,-1)),Ft=a(()=>t("div",{class:"flex justify-between"},[t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"访客数"),t("div",{class:"text-[14px] mt-[16px] text-[#999]"},"3")],-1)),Dt=$('<div class="w-full h-[120px] bg-[#f3f8ff] mt-[15px] flex" data-v-d5b994b9><div class="flex items-center justify-center flex-col w-[100px] h-[120px] bg-[#105CFB] text-[#fff]" data-v-d5b994b9><div class="nc-iconfont nc-icon-wodeV6xx-11 my_icon" data-v-d5b994b9></div><div class="text-[14px]" data-v-d5b994b9>浏览访问</div></div><div class="w-[237px] h-[120px] mx-[50px] flex flex-col justify-center" data-v-d5b994b9><div class="text-[14px]" data-v-d5b994b9>访客数(人)</div><div class="text-[26px]" data-v-d5b994b9>3</div><div class="flex" data-v-d5b994b9><div class="text-[12px] text-[#666] pr-[2px]" data-v-d5b994b9>比前日</div><div class="text-[12px] text-[#f97337] pr-[2px]" data-v-d5b994b9>⬆</div><div class="text-[12px] text-[#f97337]" data-v-d5b994b9>100%</div></div></div><div class="w-[237px] h-[120px] mx-[50px] flex flex-col justify-center" data-v-d5b994b9><div class="text-[14px]" data-v-d5b994b9>新增会员数(人)</div><div class="text-[26px]" data-v-d5b994b9>3</div><div class="flex" data-v-d5b994b9><div class="text-[12px] text-[#666] pr-[2px]" data-v-d5b994b9>比前日</div><div class="text-[12px] text-[#f97337] pr-[2px]" data-v-d5b994b9>⬆</div><div class="text-[12px] text-[#f97337]" data-v-d5b994b9>0%</div></div></div></div><div class="w-full h-[120px] bg-[#fffaf3] mt-[15px] flex" data-v-d5b994b9><div class="flex items-center justify-center flex-col w-[100px] h-[120px] bg-[#ff9f15] text-[#fff]" data-v-d5b994b9><div class="nc-iconfont nc-icon-liebiaoV6xx my_icon" data-v-d5b994b9></div><div class="text-[14px]" data-v-d5b994b9>成交转化</div></div><div class="w-[237px] h-[120px] mx-[50px] flex flex-col justify-center" data-v-d5b994b9><div class="text-[14px]" data-v-d5b994b9>支付人数(人)</div><div class="text-[26px]" data-v-d5b994b9>3</div><div class="flex" data-v-d5b994b9><div class="text-[12px] text-[#666] pr-[2px]" data-v-d5b994b9>比前日</div><div class="text-[12px] text-[#f97337] pr-[2px]" data-v-d5b994b9>⬆</div><div class="text-[12px] text-[#f97337]" data-v-d5b994b9>100%</div></div></div><div class="w-[237px] h-[120px] mx-[50px] flex flex-col justify-center" data-v-d5b994b9><div class="text-[14px]" data-v-d5b994b9>访问-支付转化率(%)</div><div class="text-[26px]" data-v-d5b994b9>0.33</div><div class="flex" data-v-d5b994b9><div class="text-[12px] text-[#666] pr-[2px]" data-v-d5b994b9>比前日</div><div class="text-[12px] text-[#f97337] pr-[2px]" data-v-d5b994b9>⬆</div><div class="text-[12px] text-[#f97337]" data-v-d5b994b9>0%</div></div></div></div>',2),Yt=z({__name:"visit",setup(h){const V=b("first"),v=b(!1),k=b(""),F=(n,l)=>{n.props.name=="fifth"&&(v.value=!0),I(()=>{j()})},r=A([{content:"盒子 1",id:1},{content:"盒子 2",id:2},{content:"盒子 3",id:3},{content:"盒子 1",id:4}]),i=b(-1),f=n=>{i.value=n.id},C=b(null),j=()=>{if(C.value!==null){const n=q(C.value),c={tooltip:{trigger:"axis"},legend:{data:["全部"]},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"全部",type:"line",data:[0,-20,-50,134,90,230,210]}]};n.setOption(c),n.resize({width:"auto"})}};return H(()=>{j()}),(n,l)=>{const p=N,c=T,D=M,B=S,Y=U,E=L;return o(),x("div",W,[d(E,{modelValue:V.value,"onUpdate:modelValue":l[4]||(l[4]=e=>V.value=e),class:"demo-tabs",onTabClick:F,type:"card"},{default:s(()=>[d(c,{name:"first"},{label:s(()=>[g("今日")]),default:s(()=>[t("div",G,[(o(!0),x(_,null,u(r,(e,w)=>(o(),x("div",{class:m(["w-[24.3%] border-[#eee] border-solid border-[1px] h-[85px] p-[15px] cursor-pointer",{"border-[#105CFB]":i.value==e.id,"text-[#105CFB]":i.value==e.id}]),onClick:y=>f(e)},[t("div",K,[Q,d(p,{class:"box-item",effect:"light",content:"统计时间内，全部站点总的访客人数",placement:"top-start"},{default:s(()=>[X]),_:1})]),Z],10,J))),256))])]),_:1}),d(c,{label:"昨日",name:"second"},{default:s(()=>[t("div",tt,[(o(!0),x(_,null,u(r,(e,w)=>(o(),x("div",{class:m(["w-[24.3%] border-[#eee] border-solid border-[1px] h-[85px] p-[15px] cursor-pointer",{"border-[#105CFB]":i.value==e.id,"text-[#105CFB]":i.value==e.id}]),onClick:y=>f(e)},[t("div",st,[at,d(p,{class:"box-item",effect:"light",content:"统计时间内，全部站点总的访客人数",placement:"top-start"},{default:s(()=>[dt]),_:1})]),lt],10,et))),256))])]),_:1}),d(c,{label:"7日内",name:"third"},{default:s(()=>[t("div",it,[(o(!0),x(_,null,u(r,(e,w)=>(o(),x("div",{class:m(["w-[24.3%] border-[#eee] border-solid border-[1px] h-[85px] p-[15px] cursor-pointer",{"border-[#105CFB]":i.value==e.id,"text-[#105CFB]":i.value==e.id}]),onClick:y=>f(e)},[t("div",xt,[nt,d(p,{class:"box-item",effect:"light",content:"统计时间内，全部站点总的访客人数",placement:"top-start"},{default:s(()=>[ct]),_:1})]),pt],10,ot))),256))])]),_:1}),d(c,{label:"30日内",name:"fourth"},{default:s(()=>[t("div",vt,[(o(!0),x(_,null,u(r,(e,w)=>(o(),x("div",{class:m(["w-[24.3%] border-[#eee] border-solid border-[1px] h-[85px] p-[15px] cursor-pointer",{"border-[#105CFB]":i.value==e.id,"text-[#105CFB]":i.value==e.id}]),onClick:y=>f(e)},[t("div",ft,[bt,d(p,{class:"box-item",effect:"light",content:"统计时间内，全部站点总的访客人数",placement:"top-start"},{default:s(()=>[_t]),_:1})]),ut],10,rt))),256))])]),_:1}),d(c,{label:"自定义",name:"fifth"},{default:s(()=>[d(Y,{modelValue:v.value,"onUpdate:modelValue":l[3]||(l[3]=e=>v.value=e),width:"520",draggable:"true"},{header:s(()=>[mt]),footer:s(()=>[t("div",yt,[d(B,{type:"primary",onClick:l[1]||(l[1]=e=>v.value=!1)},{default:s(()=>[g("确认")]),_:1}),d(B,{onClick:l[2]||(l[2]=e=>v.value=!1)},{default:s(()=>[g("取消")]),_:1})])]),default:s(()=>[t("div",ht,[wt,d(D,{modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),class:"w-[100px]",type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","date-format":"YYYY/MM/DD ddd","time-format":"A hh:mm:ss"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),t("div",Ct,[t("div",gt,[(o(!0),x(_,null,u(r,(e,w)=>(o(),x("div",{class:m(["w-[24.3%] border-[#eee] border-solid border-[1px] h-[85px] p-[15px] cursor-pointer",{"border-[#105CFB]":i.value==e.id,"text-[#105CFB]":i.value==e.id}]),onClick:y=>f(e)},[t("div",kt,[jt,d(p,{class:"box-item",effect:"light",content:"统计时间内，全部站点总的访客人数",placement:"top-start"},{default:s(()=>[Bt]),_:1})]),Ft],10,Vt))),256))])])]),_:1})]),_:1},8,["modelValue"]),t("div",{ref_key:"incomeChartRef",ref:C,class:"h-[400px] mt-[60px] ml-[-90px]"},null,512),Dt])}}});const Pt=O(Yt,[["__scopeId","data-v-d5b994b9"]]);export{Pt as default};
