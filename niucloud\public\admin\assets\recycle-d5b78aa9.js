import{d as W,y as Z,r as h,n as E,bI as K,h as f,c as T,e as l,w as r,a as i,t as c,u as s,q as o,F as Q,W as X,s as R,i as k,Z as Y,B as ee,C as N,$ as ae,af as z,a6 as te,bJ as oe,L as le,M as se,bH as ne,a4 as re,a1 as ie,E as ce,N as de,a9 as pe,aL as me,ag as ue,ah as ge,a2 as _e,a3 as he}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                *//* empty css                  *//* empty css                          *//* empty css                 */import"./el-form-item-4ed993c7.js";import{g as fe,b as ye,ai as F,aj as be}from"./goods-e07707eb.js";import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";const xe={class:"main-container"},Ce={class:"flex justify-between items-center"},Te={class:"text-page-title"},ke={class:"mt-[10px]"},Pe={class:"mb-[10px] flex items-center"},Ee={class:"flex items-center cursor-pointer"},we={class:"min-w-[70px] h-[70px] flex items-center justify-center"},Ve=["src"],Se={class:"ml-2"},Be=["title"],Re={class:"text-primary text-[12px]"},Ne={key:0},ze={key:1},Fe={class:"mt-[16px] flex justify-end"},Le=W({__name:"recycle",setup(De){const L=Z().meta.title,m=h(!1),a=E({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{goods_name:"",goods_category:[],goods_type:"",order:"",sort:""}}),w=h(),P=E([]),V=E([]);(()=>{fe().then(n=>{const e=n.data;if(e){const d=[];e.forEach(p=>{const v=[];p.child_list&&p.child_list.forEach(x=>{v.push({value:x.category_id,label:x.category_name})}),d.push({value:p.category_id,label:p.category_name,children:v})}),P.splice(0,P.length,...d)}}),ye().then(n=>{const e=n.data;if(e)for(const d in e)V.push(e[d])})})();const y=h(),b=h(!1),D=n=>{b.value=!1,S.value.toggleAllSelection()},S=h(),_=h([]),G=n=>{_.value=n,y.value=!1,_.value.length>0&&_.value.length<a.data.length?b.value=!0:b.value=!1,_.value.length==a.data.length&&a.data.length&&_.value.length&&(y.value=!0)},I=()=>{if(_.value.length==0){ae({type:"warning",message:`${o("batchEmptySelectedGoodsTips")}`});return}z.confirm(o("batchGoodsRecycleTips"),o("warning"),{confirmButtonText:o("confirm"),cancelButtonText:o("cancel"),type:"warning"}).then(()=>{if(m.value)return;m.value=!0;const n=[];_.value.forEach(e=>{n.push(e.goods_id)}),F({goods_ids:n}).then(()=>{u(),y.value=!1,m.value=!1}).catch(()=>{m.value=!1})})},u=(n=1)=>{a.loading=!0,a.page=n;const e=te(a.searchParam);be({page:a.page,limit:a.limit,...e}).then(d=>{a.loading=!1,a.data=d.data.data,a.total=d.data.total,oe(a.page,a.limit,a.searchParam)}).catch(()=>{a.loading=!1})};u(K(a.searchParam).page);const U=n=>{z.confirm(o("goodsRecycleTips"),o("warning"),{confirmButtonText:o("confirm"),cancelButtonText:o("cancel"),type:"warning"}).then(()=>{m.value||(m.value=!0,F({goods_ids:n.goods_id}).then(e=>{e.code==1&&u(),m.value=!1}).catch(()=>{m.value=!1}))})},$=n=>{n&&(n.resetFields(),u())},j=n=>{let e="";n.order=="ascending"?e="asc":n.order=="descending"&&(e="desc"),e&&(a.searchParam.order=n.prop,a.searchParam.sort=e),u()};return(n,e)=>{const d=le,p=se,v=ne,x=re,M=ie,C=ce,O=de,B=pe,q=me,g=ue,A=ge,H=_e,J=he;return f(),T("div",xe,[l(B,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[i("div",Ce,[i("span",Te,c(s(L)),1)]),l(B,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:r(()=>[l(O,{inline:!0,model:a.searchParam,ref_key:"searchFormRef",ref:w},{default:r(()=>[l(p,{label:s(o)("goodsName"),prop:"goods_name"},{default:r(()=>[l(d,{modelValue:a.searchParam.goods_name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.searchParam.goods_name=t),modelModifiers:{trim:!0},placeholder:s(o)("goodsNamePlaceholder"),maxlength:"60"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(p,{label:s(o)("goodsCategory"),prop:"goods_category"},{default:r(()=>[l(v,{modelValue:a.searchParam.goods_category,"onUpdate:modelValue":e[1]||(e[1]=t=>a.searchParam.goods_category=t),options:P,placeholder:s(o)("goodsCategoryPlaceholder"),clearable:"",props:{value:"value",label:"label",emitPath:!1}},null,8,["modelValue","options","placeholder"])]),_:1},8,["label"]),l(p,{label:s(o)("goodsType"),prop:"goods_type"},{default:r(()=>[l(M,{modelValue:a.searchParam.goods_type,"onUpdate:modelValue":e[2]||(e[2]=t=>a.searchParam.goods_type=t),placeholder:s(o)("goodsTypePlaceholder"),clearable:""},{default:r(()=>[(f(!0),T(Q,null,X(V,t=>(f(),R(x,{key:t.type,label:t.name,value:t.type},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),l(p,null,{default:r(()=>[l(C,{type:"primary",onClick:e[3]||(e[3]=t=>u())},{default:r(()=>[k(c(s(o)("search")),1)]),_:1}),l(C,{onClick:e[4]||(e[4]=t=>$(w.value))},{default:r(()=>[k(c(s(o)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),i("div",ke,[i("div",Pe,[l(q,{modelValue:y.value,"onUpdate:modelValue":e[5]||(e[5]=t=>y.value=t),size:"large",class:"px-[14px]",onChange:D,indeterminate:b.value},null,8,["modelValue","indeterminate"]),l(C,{onClick:I,size:"small"},{default:r(()=>[k(c(s(o)("batchRecycle")),1)]),_:1})]),Y((f(),R(A,{data:a.data,size:"large",ref_key:"goodsListTableRef",ref:S,onSortChange:j,onSelectionChange:G},{empty:r(()=>[i("span",null,c(a.loading?"":s(o)("emptyData")),1)]),default:r(()=>[l(g,{type:"selection",width:"55"}),l(g,{prop:"goods_id",label:s(o)("goodsInfo"),"min-width":"300"},{default:r(({row:t})=>[i("div",Ee,[i("div",we,[i("img",{class:"max-w-[70px] max-h-[70px]",src:s(ee)(t.goods_cover_thumb_small)},null,8,Ve)]),i("div",Se,[i("span",{title:t.goods_name,class:"multi-hidden"},c(t.goods_name),9,Be),i("span",Re,c(t.goods_type_name),1)])])]),_:1},8,["label"]),l(g,{prop:"price",label:s(o)("price"),"min-width":"120",align:"right",sortable:"custom"},{default:r(({row:t})=>[i("div",null,"￥"+c(t.goodsSku.price),1)]),_:1},8,["label"]),l(g,{prop:"stock",label:s(o)("stock"),"min-width":"120",sortable:"custom"},null,8,["label"]),l(g,{prop:"sale_num",label:s(o)("saleNum"),"min-width":"100",sortable:"custom"},null,8,["label"]),l(g,{prop:"status",label:s(o)("status"),"min-width":"100"},{default:r(({row:t})=>[t.status==1?(f(),T("div",Ne,c(s(o)("statusOn")),1)):N("",!0),t.status==0?(f(),T("div",ze,c(s(o)("statusOff")),1)):N("",!0)]),_:1},8,["label"]),l(g,{prop:"create_time",label:s(o)("createTime"),"min-width":"150",sortable:"custom"},{default:r(({row:t})=>[i("div",null,c(t.create_time),1)]),_:1},8,["label"]),l(g,{label:s(o)("operation"),fixed:"right",align:"right","min-width":"120"},{default:r(({row:t})=>[l(C,{type:"primary",link:"",onClick:Ue=>U(t)},{default:r(()=>[k(c(s(o)("recycle")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[J,a.loading]]),i("div",Fe,[l(H,{"current-page":a.page,"onUpdate:current-page":e[6]||(e[6]=t=>a.page=t),"page-size":a.limit,"onUpdate:page-size":e[7]||(e[7]=t=>a.limit=t),layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:e[8]||(e[8]=t=>u()),onCurrentChange:u},null,8,["current-page","page-size","total"])])])]),_:1})])}}});const ra=ve(Le,[["__scopeId","data-v-2ec5c563"]]);export{ra as default};
