import{d as q,y as G,f as J,r as y,n as W,bI as Z,h as d,c as p,e as t,w as r,a as m,t as n,u as l,q as o,F as v,W as P,s as k,i as x,Z as A,B as K,C as N,bJ as Q,a4 as X,a1 as ee,L as ae,M as te,bL as le,E as oe,N as se,a9 as re,ap as ne,aq as de,ag as ie,ah as pe,a2 as me,a3 as _e}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                    *//* empty css                *//* empty css                *//* empty css                  *//* empty css                       */import"./el-form-item-4ed993c7.js";import{_ as ce}from"./goods_default-664bb559.js";import{g as ue,a as be,b as he,c as ge}from"./order-caf8547a.js";import{_ as fe}from"./_plugin-vue_export-helper-c27b6911.js";const ye={class:"main-container"},xe={class:"flex justify-between items-center"},ve={class:"text-page-title"},Pe={class:"text-[14px]"},Ve={class:"flex cursor-pointer"},ke={class:"flex items-center min-w-[50px] mr-[10px]"},we=["src"],De={key:1,class:"w-[50px] h-[50px]",src:ce,alt:""},Te={class:"flex flex-col"},Ee={class:"multi-hidden text-[14px]"},Ce={class:"text-[12px] text-[#999]"},Fe={class:"flex flex-col"},Ne={key:0,class:"text-[14px]"},Ue={key:0},Se={key:1,class:"text-[13px]"},Ye={class:"text-[13px] mt-[5px]"},Be={key:0,class:"text-[14px]"},Me={key:0},ze={key:1,class:"text-[14px]"},Le={class:"text-[14px]"},Oe={class:"text-[14px]"},Ie={class:"mt-[16px] flex justify-end"},He=q({__name:"order_list",setup(Re){const U=G(),S=J(),Y=U.meta.title,w=y(""),B=y([]),D=y([]),T=y([]);(async()=>{B.value=await(await ue()).data,D.value=await(await be()).data,T.value=await(await he()).data})();const a=W({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{search_type:"order_no",search_name:"",pay_type:"",order_from:"",status:"",create_time:[],pay_time:[],activity_type:"exchange"}}),E=y(),b=(_=1)=>{a.loading=!0,a.page=_,ge({page:a.page,limit:a.limit,...a.searchParam}).then(s=>{a.loading=!1,a.data=s.data.data.map(c=>(c.order_goods.forEach(g=>{g.rowNum=c.order_goods.length}),c)),a.total=s.data.total,Q(a.page,a.limit,a.searchParam)}).catch(()=>{a.loading=!1})};b(Z(a.searchParam).page);const M=_=>{a.searchParam.status=_,b()},z=_=>{S.push("/shop/order/detail?order_id="+_.order_id)},L=_=>{_&&(_.resetFields(),b())};return(_,s)=>{const c=X,g=ee,O=ae,h=te,C=le,V=oe,I=se,F=re,f=ne,H=de,u=ie,R=pe,$=me,j=_e;return d(),p("div",ye,[t(F,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[m("div",xe,[m("span",ve,n(l(Y)),1)]),t(F,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:r(()=>[t(I,{inline:!0,model:a.searchParam,ref_key:"searchFormRef",ref:E},{default:r(()=>[t(h,{label:l(o)("orderInfo"),prop:"search_name"},{default:r(()=>[t(g,{modelValue:a.searchParam.search_type,"onUpdate:modelValue":s[0]||(s[0]=e=>a.searchParam.search_type=e),clearable:"",class:"input-item"},{default:r(()=>[t(c,{label:l(o)("orderNo"),value:"order_no"},null,8,["label"]),t(c,{label:l(o)("outTradeNo"),value:"out_trade_no"},null,8,["label"])]),_:1},8,["modelValue"]),t(O,{class:"input-item ml-3",modelValue:a.searchParam.search_name,"onUpdate:modelValue":s[1]||(s[1]=e=>a.searchParam.search_name=e),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1},8,["label"]),t(h,{label:l(o)("payType"),prop:"pay_type"},{default:r(()=>[t(g,{modelValue:a.searchParam.pay_type,"onUpdate:modelValue":s[2]||(s[2]=e=>a.searchParam.pay_type=e),clearable:"",class:"input-item"},{default:r(()=>[(d(!0),p(v,null,P(D.value,(e,i)=>(d(),k(c,{key:i,label:e.name,value:e.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(h,{label:l(o)("fromType"),prop:"order_from"},{default:r(()=>[t(g,{modelValue:a.searchParam.order_from,"onUpdate:modelValue":s[3]||(s[3]=e=>a.searchParam.order_from=e),clearable:"",class:"input-item"},{default:r(()=>[(d(!0),p(v,null,P(T.value,(e,i)=>(d(),k(c,{key:i,label:e,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(h,{label:l(o)("createTime"),prop:"create_time"},{default:r(()=>[t(C,{modelValue:a.searchParam.create_time,"onUpdate:modelValue":s[4]||(s[4]=e=>a.searchParam.create_time=e),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":l(o)("startDate"),"end-placeholder":l(o)("endDate")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1},8,["label"]),t(h,{label:l(o)("payTime"),prop:"pay_time"},{default:r(()=>[t(C,{modelValue:a.searchParam.pay_time,"onUpdate:modelValue":s[5]||(s[5]=e=>a.searchParam.pay_time=e),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":l(o)("startDate"),"end-placeholder":l(o)("endDate")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1},8,["label"]),t(h,null,{default:r(()=>[t(V,{type:"primary",onClick:s[6]||(s[6]=e=>b())},{default:r(()=>[x(n(l(o)("search")),1)]),_:1}),t(V,{onClick:s[7]||(s[7]=e=>L(E.value))},{default:r(()=>[x(n(l(o)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(H,{modelValue:w.value,"onUpdate:modelValue":s[8]||(s[8]=e=>w.value=e),class:"demo-tabs",onTabChange:M},{default:r(()=>[t(f,{label:l(o)("all"),name:""},null,8,["label"]),t(f,{label:l(o)("toBeShipped"),name:"2"},null,8,["label"]),t(f,{label:l(o)("shipped"),name:"3"},null,8,["label"]),t(f,{label:l(o)("completed"),name:"5"},null,8,["label"]),t(f,{label:l(o)("closed"),name:"-1"},null,8,["label"])]),_:1},8,["modelValue"]),A((d(),k(R,{data:a.data,size:"large"},{empty:r(()=>[m("span",null,n(a.loading?"":l(o)("emptyData")),1)]),default:r(()=>[t(u,{label:l(o)("orderNo"),"min-width":"250"},{default:r(({row:e})=>[m("span",Pe,n(e.order_no),1)]),_:1},8,["label"]),t(u,{label:l(o)("orderGoods"),"min-width":"350"},{default:r(({row:e})=>[(d(!0),p(v,null,P(e.order_goods,i=>(d(),p("div",Ve,[m("div",ke,[i.goods_image_thumb_small?(d(),p("img",{key:0,class:"w-[50px] h-[50px]",src:l(K)(i.goods_image_thumb_small),alt:""},null,8,we)):(d(),p("img",De))]),m("div",Te,[m("p",Ee,n(i.goods_name),1),m("span",Ce,n(i.sku_name),1)])]))),256))]),_:1},8,["label"]),t(u,{label:l(o)("goodsPriceNumber"),"min-width":"200"},{default:r(({row:e})=>[(d(!0),p(v,null,P(e.order_goods,i=>(d(),p("div",Fe,[e.activity_type=="exchange"?(d(),p("span",Ne,[x(n(i.extend.point)+n(l(o)("point"))+" ",1),parseFloat(i.extend.price)?(d(),p("span",Ue,"+￥"+n(i.extend.price),1)):N("",!0)])):(d(),p("span",Se,"￥"+n(i.price),1)),m("span",Ye,n(i.num)+n(l(o)("piece")),1)]))),256))]),_:1},8,["label"]),t(u,{label:l(o)("orderMoney"),"min-width":"200"},{default:r(({row:e})=>[e.activity_type=="exchange"?(d(),p("span",Be,[x(n(e.point)+n(l(o)("point"))+" ",1),parseFloat(e.order_money)?(d(),p("span",Me,"+￥"+n(e.order_money),1)):N("",!0)])):(d(),p("span",ze,"￥"+n(e.order_money),1))]),_:1},8,["label"]),t(u,{label:l(o)("createTime"),"min-width":"200"},{default:r(({row:e})=>[m("span",Le,n(e.create_time),1)]),_:1},8,["label"]),t(u,{label:l(o)("orderStatus"),"min-width":"100"},{default:r(({row:e})=>[m("span",Oe,n(e.status_name.name),1)]),_:1},8,["label"]),t(u,{label:l(o)("操作"),fixed:"right",align:"right","min-width":"100"},{default:r(({row:e})=>[t(V,{type:"primary",link:"",onClick:i=>z(e)},{default:r(()=>[x(n(l(o)("info")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[j,a.loading]]),m("div",Ie,[t($,{"current-page":a.page,"onUpdate:current-page":s[9]||(s[9]=e=>a.page=e),"page-size":a.limit,"onUpdate:page-size":s[10]||(s[10]=e=>a.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:s[11]||(s[11]=e=>b()),onCurrentChange:b},null,8,["current-page","page-size","total"])])]),_:1})])}}});const _a=fe(He,[["__scopeId","data-v-2c55750d"]]);export{_a as default};
