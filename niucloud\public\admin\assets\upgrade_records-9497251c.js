import{d as j,y as H,r as h,n as I,h as p,c as f,a as d,e as l,w as o,t as r,u as s,q as n,i as g,Z as q,s as R,F as V,W,C as Z,$ as A,af as G,L as J,M as K,E as O,N as Q,a9 as X,ag as Y,ah as ee,a2 as te,U as ae,V as le,a3 as ne}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";import{k as oe,l as se}from"./upgrade-5c77adbf.js";const re={class:"main-container"},ie={class:"flex justify-between items-center"},pe={class:"text-page-title"},de={key:0},me={key:1},ce={class:"mt-[10px] flex items-center"},ue={class:"mt-[16px] flex justify-end"},_e=["innerHTML"],Me=j({__name:"upgrade_records",setup(fe){const w=H().meta.title,y=h(),F=h(),t=I({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{name:""}}),N=i=>{i&&(i.resetFields(),m())},m=(i=1)=>{t.loading=!0,t.page=i,oe({page:t.page,limit:t.limit,...t.searchParam}).then(a=>{t.loading=!1,t.data=a.data.data,t.total=a.data.total}).catch(()=>{t.loading=!1})};m();const x=h(""),v=h(!1),T=i=>{x.value=i.fail_reason,v.value=!0};let b=[];const B=i=>{b=i.map(a=>a.id)},D=()=>{if(!b.length){A({message:"请先勾选要删除的记录",type:"error",duration:5e3});return}k(b)},k=i=>{G.confirm(n("deleteTips"),n("warning"),{confirmButtonText:n("confirm"),cancelButtonText:n("cancel"),type:"warning"}).then(()=>{se({ids:i}).then(()=>{m()}).catch(()=>{})})};return(i,a)=>{const z=J,C=K,u=O,P=Q,E=X,c=Y,U=ee,L=te,M=ae,$=le,S=ne;return p(),f(V,null,[d("div",re,[l(E,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[d("div",ie,[d("span",pe,r(s(w)),1)]),l(E,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:o(()=>[l(P,{inline:!0,model:t.searchParam,ref_key:"searchFormRef",ref:y},{default:o(()=>[l(C,{label:s(n)("upgradeName"),prop:"name"},{default:o(()=>[l(z,{modelValue:t.searchParam.name,"onUpdate:modelValue":a[0]||(a[0]=e=>t.searchParam.name=e),modelModifiers:{trim:!0},placeholder:s(n)("upgradeNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(C,null,{default:o(()=>[l(u,{type:"primary",onClick:a[1]||(a[1]=e=>m())},{default:o(()=>[g(r(s(n)("search")),1)]),_:1}),l(u,{onClick:a[2]||(a[2]=e=>N(y.value))},{default:o(()=>[g(r(s(n)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),q((p(),R(U,{data:t.data,size:"large",ref_key:"tableRef",ref:F,onSelectionChange:B},{empty:o(()=>[d("span",null,r(t.loading?"":s(n)("emptyData")),1)]),default:o(()=>[l(c,{type:"selection",width:"55"}),l(c,{prop:"id",label:s(n)("id"),width:"140"},null,8,["label"]),l(c,{prop:"name",label:s(n)("upgradeName")},{default:o(({row:e})=>[!e.content||typeof e.content=="string"?(p(),f("div",de," 【"+r(e.name)+"】从"+r(e.prev_version)+"升级到"+r(e.current_version),1)):(p(),f("div",me,[(p(!0),f(V,null,W(e.content.content,_=>(p(),f("div",null,"【"+r(_.app.app_name)+"】从"+r(_.version)+"升级到"+r(_.upgrade_version),1))),256))]))]),_:1},8,["label"]),l(c,{prop:"create_time",label:s(n)("completeTime"),width:"220px"},null,8,["label"]),l(c,{prop:"status_name",label:s(n)("status"),width:"120px"},null,8,["label"]),l(c,{label:s(n)("operation"),align:"right",width:"160px"},{default:o(({row:e})=>[e.status=="fail"?(p(),R(u,{key:0,type:"primary",link:"",onClick:_=>T(e)},{default:o(()=>[g(r(s(n)("failReason")),1)]),_:2},1032,["onClick"])):Z("",!0),l(u,{type:"primary",link:"",onClick:_=>k(e.id)},{default:o(()=>[g(r(s(n)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[S,t.loading]]),d("div",ce,[l(u,{onClick:D,size:"small"},{default:o(()=>[g(r(s(n)("batchDelete")),1)]),_:1})]),d("div",ue,[l(L,{"current-page":t.page,"onUpdate:current-page":a[3]||(a[3]=e=>t.page=e),"page-size":t.limit,"onUpdate:page-size":a[4]||(a[4]=e=>t.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:a[5]||(a[5]=e=>m()),onCurrentChange:m},null,8,["current-page","page-size","total"])])]),_:1})]),l($,{modelValue:v.value,"onUpdate:modelValue":a[6]||(a[6]=e=>v.value=e),title:s(n)("failReason"),width:"60%"},{default:o(()=>[l(M,{class:"h-[60vh] w-full whitespace-pre-wrap p-[20px]"},{default:o(()=>[d("div",{innerHTML:x.value},null,8,_e)]),_:1})]),_:1},8,["modelValue","title"])],64)}}});export{Me as default};
