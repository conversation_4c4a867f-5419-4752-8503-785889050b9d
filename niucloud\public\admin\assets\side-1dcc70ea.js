import{d as g,r as x,G as y,j as v,y as E,f as S,l as b,h as l,s as d,w as r,e as c,u as t,c as k,F as w,W as C,v as B,a as I,cZ as A,U as F,O as M,A as N}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  */import R from"./menu-item-ee0176be.js";import"./el-menu-item-4ed993c7.js";import"./el-sub-menu-4ed993c7.js";import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./_plugin-vue_export-helper-c27b6911.js";const T=I("div",{class:"h-[48px]"},null,-1),H=g({__name:"side",setup(U){x("@/app/assets/images/login_logo.png");const o=y(),a=v(),m=E();S(),a.siteInfo;const _=b(()=>String(m.name));return a.routers=a.routers.filter((e,p)=>{if(e.name=="setting_manage")return e.children&&e.children.forEach((s,i)=>{s.meta.class=1,s.children&&s.children.forEach((n,u)=>{n.meta.class=2})}),e.children}),(e,p)=>{const s=A,i=F,n=M,u=N;return l(),d(u,{class:B(["w-100",[{"sidebar-dark-mode":t(o).sidebar=="twoType"},{"sidebar-brightness-mode":t(o).sidebar=="oneType"}]])},{default:r(()=>[c(n,{class:"menu-wrap"},{default:r(()=>[c(i,null,{default:r(()=>[c(s,{"default-active":t(_),router:!0,class:"aside-menu h-full","unique-opened":!0,collapse:t(o).menuIsCollapse},{default:r(()=>[(l(!0),k(w,null,C(t(a).routers[0].children,(f,h)=>(l(),d(R,{routes:f,key:h},null,8,["routes"]))),128))]),_:1},8,["default-active","collapse"]),T]),_:1})]),_:1})]),_:1},8,["class"])}}});export{H as default};
