import{P as r}from"./index-30109030.js";function s(e){return r.get("sys/printer",{params:e})}function n(e){return r.get(`sys/printer/${e}`)}function u(e){return r.post("sys/printer",e,{showErrorMessage:!0,showSuccessMessage:!0})}function i(e){return r.put(`sys/printer/${e.printer_id}`,e,{showErrorMessage:!0,showSuccessMessage:!0})}function o(e){return r.put("sys/printer/status",e,{showErrorMessage:!0,showSuccessMessage:!0})}function a(e){return r.delete(`sys/printer/${e}`,{showErrorMessage:!0,showSuccessMessage:!0})}function p(e){return r.get("sys/printer/template",{params:e})}function c(e){return r.get("sys/printer/template/list",{params:e})}function g(e){return r.get(`sys/printer/template/${e}`)}function f(e){return r.post("sys/printer/template",e,{showErrorMessage:!0,showSuccessMessage:!0})}function h(e){return r.put(`sys/printer/template/${e.template_id}`,e,{showErrorMessage:!0,showSuccessMessage:!0})}function w(e){return r.delete(`sys/printer/template/${e}`,{showErrorMessage:!0,showSuccessMessage:!0})}function y(e){return r.get("sys/printer/type",{params:e})}function M(e){return r.get("sys/printer/brand",{params:e})}function l(e){return r.put(`sys/printer/refreshtoken/${e}`,{},{showErrorMessage:!0,showSuccessMessage:!0})}function P(e){return r.put(`sys/printer/testprint/${e}`,{},{showErrorMessage:!0,showSuccessMessage:!0})}function d(e){return r.post("sys/printer/printticket",e,{showErrorMessage:!0,showSuccessMessage:!0})}export{c as a,y as b,n as c,u as d,i as e,s as f,M as g,a as h,g as i,h as j,f as k,p as l,o as m,w as n,d as p,l as r,P as t};
