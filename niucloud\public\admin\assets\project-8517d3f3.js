import{P as r}from"./index-30109030.js";function o(t){return r.get("niucrowd/project",{params:t})}function u(t){return r.get(`niucrowd/project/${t}`)}function c(t){return r.post("niucrowd/project",t)}function i(t,e){return r.put(`niucrowd/project/${t}`,e)}function a(t){return r.delete(`niucrowd/project/${t}`)}function d(t,e){return r.put(`niucrowd/project/${t}/audit`,e)}function s(){return r.get("niucrowd/project/options")}function g(t){return r.get("niucrowd/category",{params:t})}function w(t){return r.get(`niucrowd/category/${t}`)}function f(){return r.get("niucrowd/category/options")}function p(t){return r.post("niucrowd/category",t)}function j(t,e){return r.put(`niucrowd/category/${t}`,e)}function y(t){return r.delete(`niucrowd/category/${t}`)}function $(t){return r.get("niucrowd/reward",{params:t})}function P(t){return r.post("niucrowd/reward",t)}function l(t,e){return r.put(`niucrowd/reward/${t}`,e)}function C(t){return r.delete(`niucrowd/reward/${t}`)}export{g as a,o as b,p as c,y as d,j as e,w as f,f as g,d as h,i,c as j,u as k,$ as l,C as m,a as n,l as o,P as p,s as q};
