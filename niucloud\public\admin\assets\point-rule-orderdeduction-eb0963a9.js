import{d as b,r as d,n as w,I as u,l as g,R as p,h as E,s as R,w as _,e as n,a as o,Z as j,_ as B,aL as C,L as I,M,N}from"./index-30109030.js";/* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                    */const O=o("span",{class:"ml-[10px] el-form-item__label"},"积分抵现",-1),U={class:"flex"},D=o("span",{class:"el-form-item__label"},"每",-1),F={class:"w-[70px]"},L=o("span",{class:"ml-[10px] el-form-item__label"},"积分，可抵扣",-1),z={class:"w-[70px]"},S=o("span",{class:"ml-[10px] el-form-item__label"},"元",-1),J=b({__name:"point-rule-orderdeduction",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(f,{expose:v,emit:c}){const y=f,e=d({is_use:0,money:"",point:""}),m=d(null),V=w({money:[{validator:(s,l,t)=>{e.value.is_use&&(u.empty(e.value.point)&&t("请输入积分数量"),u.digits(e.value.point)||t("积分数量格式错误"),e.value.point<=0&&t("积分数量不能小于等于0"),u.empty(e.value.money)&&t("请输入可抵扣金额"),u.amount(e.value.money)||t("抵扣金额格式错误"),e.value.money<=0&&t("抵扣金额不能小于等于0")),t()}}]}),i=g({get(){return y.modelValue},set(s){c("update:modelValue",s)}});return p(()=>i.value,(s,l)=>{(!l||!Object.keys(l).length)&&Object.keys(s).length&&(e.value=i.value)},{immediate:!0}),p(()=>e.value,()=>{i.value=e.value},{deep:!0}),v({verify:async()=>{var l;let s=!0;return await((l=m.value)==null?void 0:l.validate(t=>{s=t})),s}}),(s,l)=>{const t=C,r=I,h=M,x=N;return E(),R(x,{ref_key:"formRef",ref:m,model:e.value,rules:V},{default:_(()=>[n(h,{label:"",prop:"money"},{default:_(()=>[o("div",null,[o("div",null,[n(t,{modelValue:e.value.is_use,"onUpdate:modelValue":l[0]||(l[0]=a=>e.value.is_use=a),"true-label":1,"false-label":0,label:"",size:"large"},null,8,["modelValue"]),O]),j(o("div",U,[D,o("div",F,[n(r,{modelValue:e.value.point,"onUpdate:modelValue":l[1]||(l[1]=a=>e.value.point=a),modelModifiers:{number:!0,trim:!0},clearable:""},null,8,["modelValue"])]),L,o("div",z,[n(r,{modelValue:e.value.money,"onUpdate:modelValue":l[2]||(l[2]=a=>e.value.money=a),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),S],512),[[B,e.value.is_use]])])]),_:1})]),_:1},8,["model","rules"])}}});export{J as default};
