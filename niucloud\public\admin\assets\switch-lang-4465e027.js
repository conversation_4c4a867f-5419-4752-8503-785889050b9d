import{d as p,y as u,G as f,h as g,s as w,w as o,e as n,u as d,i as c,H as h,db as b,bk as x,bl as y,bm as k}from"./index-30109030.js";/* empty css                  *//* empty css                         *//* empty css                  *//* empty css                     */import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";const N=p({__name:"switch-lang",setup(C){const l=u(),e=f(),r=a=>{e.$patch(t=>{t.lang=a,h.set({key:"lang",data:a})}),b.loadLocaleMessages(l.path,e.lang),location.reload()};return(a,t)=>{const _=E,s=x,m=y,i=k;return g(),w(i,{onCommand:r,tabindex:1},{dropdown:o(()=>[n(m,null,{default:o(()=>[n(s,{command:"zh-cn",disabled:d(e).lang=="zh-cn"},{default:o(()=>[c("简体中文")]),_:1},8,["disabled"]),n(s,{command:"en",disabled:d(e).lang=="en"},{default:o(()=>[c("English")]),_:1},8,["disabled"])]),_:1})]),default:o(()=>[n(_,{name:"iconfont iconfanyi"})]),_:1})}}});export{N as default};
