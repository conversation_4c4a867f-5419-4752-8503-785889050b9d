import{d as q,r as _,n as x,l as K,q as l,h as y,s as h,w as s,a as k,e as o,i as f,t as p,u as a,Z as M,c9 as F,ca as R,au as C,av as N,M as A,L as I,N as j,E as L,V as O,a3 as T}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                       *//* empty css                 */const $={class:"form-tip"},G={class:"dialog-footer"},oe=q({__name:"storage-tencent",emits:["complete"],setup(Z,{expose:w,emit:P}){const c=_(!1),i=_(!0),g={storage_type:"",bucket:"",access_key:"",secret_key:"",domain:"",is_use:"",region:""},t=x({...g}),b=_(),D=K(()=>({bucket:[{required:!0,message:l("tencentBucketPlaceholder"),trigger:"blur"}],access_key:[{required:!0,message:l("tencentAccessKeyPlaceholder"),trigger:"blur"}],secret_key:[{required:!0,message:l("tencentSecretKeyPlaceholder"),trigger:"blur"}],region:[{required:!0,message:l("regionPlaceholder"),trigger:"blur"}],domain:[{required:!0,message:l("domainPlaceholder"),trigger:"blur"}]})),U=async d=>{i.value||!d||await d.validate(async e=>{e&&(i.value=!0,F(t).then(v=>{i.value=!1,c.value=!1,P("complete")}).catch(()=>{i.value=!1}))})};return w({showDialog:c,setFormData:async(d=null)=>{if(i.value=!0,Object.assign(t,g),d){const e=await(await R(d.storage_type)).data;Object.keys(t).forEach(n=>{e[n]!=null&&(t[n]=e[n]),e.params[n]!=null&&(t[n]=e.params[n].value)})}i.value=!1}}),(d,e)=>{const n=C,v=N,u=A,m=I,B=j,V=L,E=O,S=T;return y(),h(E,{modelValue:c.value,"onUpdate:modelValue":e[8]||(e[8]=r=>c.value=r),title:a(l)("tencentStorage"),width:"580px","destroy-on-close":!0},{footer:s(()=>[k("span",G,[o(V,{onClick:e[6]||(e[6]=r=>c.value=!1)},{default:s(()=>[f(p(a(l)("cancel")),1)]),_:1}),o(V,{type:"primary",loading:i.value,onClick:e[7]||(e[7]=r=>U(b.value))},{default:s(()=>[f(p(a(l)("confirm")),1)]),_:1},8,["loading"])])]),default:s(()=>[M((y(),h(B,{model:t,"label-width":"140px",ref_key:"formRef",ref:b,rules:a(D),class:"page-form"},{default:s(()=>[o(u,{label:a(l)("isUse")},{default:s(()=>[o(v,{modelValue:t.is_use,"onUpdate:modelValue":e[0]||(e[0]=r=>t.is_use=r)},{default:s(()=>[o(n,{label:"1"},{default:s(()=>[f(p(a(l)("startUsing")),1)]),_:1}),o(n,{label:"0"},{default:s(()=>[f(p(a(l)("statusDeactivate")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),o(u,{label:a(l)("tencentBucket"),prop:"bucket"},{default:s(()=>[o(m,{modelValue:t.bucket,"onUpdate:modelValue":e[1]||(e[1]=r=>t.bucket=r),modelModifiers:{trim:!0},placeholder:a(l)("tencentBucketPlaceholder"),class:"input-width","show-word-limit":"",clearable:""},null,8,["modelValue","placeholder"]),k("div",$,p(a(l)("tencentBucketTips")),1)]),_:1},8,["label"]),o(u,{label:a(l)("tencentAccessKey"),prop:"access_key"},{default:s(()=>[o(m,{modelValue:t.access_key,"onUpdate:modelValue":e[2]||(e[2]=r=>t.access_key=r),modelModifiers:{trim:!0},placeholder:a(l)("tencentAccessKeyPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(u,{label:a(l)("tencentSecretKey"),prop:"secret_key"},{default:s(()=>[o(m,{modelValue:t.secret_key,"onUpdate:modelValue":e[3]||(e[3]=r=>t.secret_key=r),modelModifiers:{trim:!0},placeholder:a(l)("tencentSecretKeyPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(u,{label:a(l)("region"),prop:"region"},{default:s(()=>[o(m,{modelValue:t.region,"onUpdate:modelValue":e[4]||(e[4]=r=>t.region=r),modelModifiers:{trim:!0},placeholder:a(l)("regionPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(u,{label:a(l)("domain"),prop:"domain"},{default:s(()=>[o(m,{modelValue:t.domain,"onUpdate:modelValue":e[5]||(e[5]=r=>t.domain=r),modelModifiers:{trim:!0},placeholder:a(l)("domainPlaceholder"),class:"input-width",clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[S,i.value]])]),_:1},8,["modelValue","title"])}}});export{oe as default};
