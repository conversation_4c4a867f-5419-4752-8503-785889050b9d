<?php

namespace addon\niucrowd;

use app\service\admin\diy\DiyService;

/**
 * 众筹系统插件
 */
class Addon
{
    /**
     * 插件安装执行
     */
    public function install()
    {
        $diy_service = new DiyService();

        // 设置众筹首页模板
        $diy_service->changeTemplate([
            'type' => 'DIY_NIUCROWD_INDEX',
            'name' => 'NIUCROWD_INDEX',
            'parent' => 'NIUCROWD_LINK',
            'page' => '/addon/niucrowd/pages/index',
            'title' => '共享创业',
            'action' => 'decorate'
        ]);

        // 设置众筹个人中心模板
        $diy_service->changeTemplate([
            'type' => 'DIY_NIUCROWD_MEMBER_INDEX',
            'name' => 'NIUCROWD_MEMBER_CENTER',
            'parent' => 'NIUCROWD_LINK',
            'page' => '/addon/niucrowd/pages/member/center',
            'title' => '共享创业个人中心',
            'action' => 'decorate'
        ]);

        return true;
    }

    /**
     * 插件卸载执行
     */
    public function uninstall()
    {
        return true;
    }

    /**
     * 插件升级执行
     */
    public function upgrade()
    {
        return true;
    }
}
