import{d as S,r as b,n as C,q as e,h as u,c as g,e as o,w as l,a as p,i as s,t as r,u as t,s as V,C as c,F as M,$ as P,ai as U,ao as $,aj as A,E as L,V as O,L as z,M as G,N as H}from"./index-30109030.js";/* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                             *//* empty css               */import{d as J}from"./support-9983e54a.js";import{_ as K}from"./_plugin-vue_export-helper-c27b6911.js";const Q={class:"support-detail"},W={key:0,class:"mt-4"},X={key:1,class:"mt-4"},Y={class:"p-3 bg-gray-50 rounded mt-2"},Z={key:2,class:"mt-4"},ee={class:"dialog-footer"},le={class:"dialog-footer"},ae=S({__name:"support-detail",emits:["complete"],setup(te,{expose:E,emit:F}){const f=b(!1),y=b(!1),k=b(!1),h=b(),a=C({support_id:"",support_no:"",project_name:"",member_name:"",member_phone:"",reward_name:"",amount:0,status:0,status_name:"",create_time:"",pay_time:"",delivery_time:"",delivery_address:null,delivery_info:null,remark:""}),m=C({company:"",no:""}),I={company:[{required:!0,message:e("expressCompanyRequired"),trigger:"blur"}],no:[{required:!0,message:e("expressNoRequired"),trigger:"blur"}]},R=(_={})=>{Object.assign(a,_)},T=async()=>{h.value&&await h.value.validate(async _=>{if(_){k.value=!0;try{await J(a.support_id,m),P.success(e("deliverySuccess")),y.value=!1,f.value=!1,F("complete")}catch(d){console.error("Delivery failed:",d)}finally{k.value=!1}}})},j=_=>({0:"warning",1:"success",2:"danger",3:"info"})[_]||"info";return E({showDetail:f,setFormData:R}),(_,d)=>{const n=U,q=$,x=A,v=L,D=O,N=z,w=G,B=H;return u(),g(M,null,[o(D,{modelValue:f.value,"onUpdate:modelValue":d[2]||(d[2]=i=>f.value=i),title:t(e)("supportDetail"),width:"800px","destroy-on-close":!0},{footer:l(()=>[p("span",ee,[o(v,{onClick:d[0]||(d[0]=i=>f.value=!1)},{default:l(()=>[s(r(t(e)("close")),1)]),_:1}),a.status==1?(u(),V(v,{key:0,type:"primary",onClick:d[1]||(d[1]=i=>y.value=!0)},{default:l(()=>[s(r(t(e)("delivery")),1)]),_:1})):c("",!0)])]),default:l(()=>[p("div",Q,[o(x,{column:2,border:""},{default:l(()=>[o(n,{label:t(e)("supportNo")},{default:l(()=>[s(r(a.support_no),1)]),_:1},8,["label"]),o(n,{label:t(e)("projectName")},{default:l(()=>[s(r(a.project_name),1)]),_:1},8,["label"]),o(n,{label:t(e)("supporterName")},{default:l(()=>[s(r(a.member_name),1)]),_:1},8,["label"]),o(n,{label:t(e)("supporterPhone")},{default:l(()=>[s(r(a.member_phone),1)]),_:1},8,["label"]),o(n,{label:t(e)("rewardName")},{default:l(()=>[s(r(a.reward_name),1)]),_:1},8,["label"]),o(n,{label:t(e)("supportAmount")},{default:l(()=>[s(" ¥"+r(a.amount),1)]),_:1},8,["label"]),o(n,{label:t(e)("status")},{default:l(()=>[o(q,{type:j(a.status)},{default:l(()=>[s(r(a.status_name),1)]),_:1},8,["type"])]),_:1},8,["label"]),o(n,{label:t(e)("supportTime")},{default:l(()=>[s(r(a.create_time),1)]),_:1},8,["label"]),a.pay_time?(u(),V(n,{key:0,label:t(e)("payTime")},{default:l(()=>[s(r(a.pay_time),1)]),_:1},8,["label"])):c("",!0),a.delivery_time?(u(),V(n,{key:1,label:t(e)("deliveryTime")},{default:l(()=>[s(r(a.delivery_time),1)]),_:1},8,["label"])):c("",!0)]),_:1}),a.delivery_address?(u(),g("div",W,[p("h4",null,r(t(e)("deliveryAddress")),1),o(x,{column:1,border:"",class:"mt-2"},{default:l(()=>[o(n,{label:t(e)("receiverName")},{default:l(()=>[s(r(a.delivery_address.name),1)]),_:1},8,["label"]),o(n,{label:t(e)("receiverPhone")},{default:l(()=>[s(r(a.delivery_address.phone),1)]),_:1},8,["label"]),o(n,{label:t(e)("receiverAddress")},{default:l(()=>[s(r(a.delivery_address.province)+" "+r(a.delivery_address.city)+" "+r(a.delivery_address.district)+" "+r(a.delivery_address.address),1)]),_:1},8,["label"])]),_:1})])):c("",!0),a.remark?(u(),g("div",X,[p("h4",null,r(t(e)("supportRemark")),1),p("div",Y,r(a.remark),1)])):c("",!0),a.delivery_info?(u(),g("div",Z,[p("h4",null,r(t(e)("deliveryInfo")),1),o(x,{column:2,border:"",class:"mt-2"},{default:l(()=>[o(n,{label:t(e)("expressCompany")},{default:l(()=>[s(r(a.delivery_info.company),1)]),_:1},8,["label"]),o(n,{label:t(e)("expressNo")},{default:l(()=>[s(r(a.delivery_info.no),1)]),_:1},8,["label"])]),_:1})])):c("",!0)])]),_:1},8,["modelValue","title"]),o(D,{modelValue:y.value,"onUpdate:modelValue":d[6]||(d[6]=i=>y.value=i),title:t(e)("deliveryInfo"),width:"500px"},{footer:l(()=>[p("span",le,[o(v,{onClick:d[5]||(d[5]=i=>y.value=!1)},{default:l(()=>[s(r(t(e)("cancel")),1)]),_:1}),o(v,{type:"primary",loading:k.value,onClick:T},{default:l(()=>[s(r(t(e)("confirm")),1)]),_:1},8,["loading"])])]),default:l(()=>[o(B,{ref_key:"deliveryFormRef",ref:h,model:m,rules:I,"label-width":"100px"},{default:l(()=>[o(w,{label:t(e)("expressCompany"),prop:"company"},{default:l(()=>[o(N,{modelValue:m.company,"onUpdate:modelValue":d[3]||(d[3]=i=>m.company=i),placeholder:t(e)("expressCompanyPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o(w,{label:t(e)("expressNo"),prop:"no"},{default:l(()=>[o(N,{modelValue:m.no,"onUpdate:modelValue":d[4]||(d[4]=i=>m.no=i),placeholder:t(e)("expressNoPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])],64)}}});const fe=K(ae,[["__scopeId","data-v-e07e38ed"]]);export{fe as default};
