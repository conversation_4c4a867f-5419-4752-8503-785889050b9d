import{d as _,l as n,h as l,c as s,u as e,B as r,x as p}from"./index-30109030.js";const m=["src"],d=["src"],f=_({__name:"preview-image",props:{value:{type:Object,default:{}}},setup(c,{expose:u}){const a=c,o=n(()=>a.value),i=n(()=>{var t="";return t+=`width: ${a.value.width}px;`,t});return u({}),(t,v)=>(l(),s("div",{class:"pointer-events-none max-w-[720px]",style:p(e(i))},[e(o).value?(l(),s("img",{key:0,src:e(r)(e(o).value),class:"w-full h-full"},null,8,m)):(l(),s("img",{key:1,src:e(r)("static/resource/images/diy/crack_figure.png"),class:"w-full h-full"},null,8,d))],4))}}),y=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));export{y as _};
