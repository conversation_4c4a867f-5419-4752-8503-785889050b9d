import{d as _e,r as S,y as me,f as fe,n as ce,cW as ve,l as B,q as t,I,h as U,c as O,a as b,e as l,w as n,u as r,aT as he,Z as W,s as H,i as h,t as v,bM as C,C as ge,_ as J,aM as ye,F as be,$ as Q,aU as Ve,a9 as ke,L as Se,M as we,au as Ee,av as Ce,ag as xe,E as Ue,ah as De,aW as Be,N as Ne,U as Ae,V as Te,a3 as Me}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                       *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                *//* empty css                       */import{A as Pe,B as Re,C as ze}from"./delivery-ef62b210.js";import{_ as Fe}from"./_plugin-vue_export-helper-c27b6911.js";const Ke={class:"main-container"},Ie={class:"area-input"},Oe=["onClick"],We={key:1},je={class:"mt-[10px]"},qe={class:"area-input"},Le={class:"form-tip"},Ze={class:"mt-[10px]"},Ge={class:"area-input"},He={class:"mt-[10px]"},Je={class:"fixed-footer-wrap"},Qe={class:"fixed-footer"},Xe={class:"dialog-footer"},Ye=_e({__name:"template_edit",setup($e){const x=S(!1),N=me(),j=fe(),V=S(!1),X={template_id:"",template_name:"",fee_type:"num",area:[],no_delivery:0,is_free_shipping:0,fee_data:[],free_shipping_data:[],no_delivery_data:[]},Y=N.meta.title,u=ce({...X}),q=S(),A=S([]);N.query.id&&(V.value=!0,Pe(N.query.id).then(({data:d})=>{d&&(Object.keys(u).forEach(e=>{d[e]!=null&&(u[e]=d[e])}),m.value=d.fee_data,y.value=d.no_delivery_data,f.value=d.free_shipping_data),V.value=!1}).catch(()=>{V.value=!1})),ve(2).then(d=>{A.value=d.data}).catch();const $=B(()=>({template_name:[{required:!0,message:t("templateNamePlaceholder"),trigger:"blur"}],fee_data:[{validator:ee}],free_shipping_data:[{validator:ae}],no_delivery_data:[{validator:le}]})),ee=(d,e,p)=>{for(let _=0;_<m.value.length;_++){const o=m.value[_];if(!o.area_ids.length){p(new Error(t("areaPlaceholder")));break}if(I.empty(o.snum)||o.snum<0){p(new Error(D.value.first+t("notUnderZero")));break}if(I.empty(o.xnum)||o.snum<0){p(new Error(D.value.continue+t("notUnderZero")));break}}p()},ae=(d,e,p)=>{if(u.is_free_shipping){for(let _=0;_<f.value.length;_++){const o=f.value[_];if(!o.area_ids.length){p(new Error(t("freeShippingPlaceholder")));break}if(I.empty(o.free_shipping_num)||o.free_shipping_num<0){p(new Error(L.value+t("notUnderZero")));break}}p()}else p()},le=(d,e,p)=>{if(u.no_delivery){for(let _=0;_<y.value.length;_++)if(!y.value[_].area_ids.length){p(new Error(t("noDeliveryPlaceholder")));break}p()}else p()},D=B(()=>({num:{first:t("firstNum"),continue:t("continueNum")},weight:{first:t("firstWeight"),continue:t("continueWeight")},volume:{first:t("firstVolume"),continue:t("continueVolume")}})[u.fee_type]),L=B(()=>({num:t("freeShippingNum"),weight:t("freeShippingWeight"),volume:t("freeShippingVolume")})[u.fee_type]),m=S([{area_ids:[0],fee_area_names:"全国",snum:1,sprice:0,xnum:1,xprice:0}]),f=S([]),y=S([]),T=d=>{switch(d){case"fee":m.value.push({area_ids:[],fee_area_names:"",snum:1,sprice:0,xnum:1,xprice:0});break;case"free_shipping":f.value.push({area_ids:[],free_shipping_area_names:"",free_shipping_num:0,free_shipping_price:0});break;case"no_delivery":y.value.push({area_ids:[],no_delivery_area_names:""});break}},M=(d,e)=>{switch(d){case"fee":m.value.splice(e,1);break;case"free_shipping":f.value.splice(e,1);break;case"no_delivery":y.value.splice(e,1);break}};let P=[];const R=S([]);let w={type:"",index:0};const z=(d,e)=>{w={type:d,index:e};let p=[];switch(d){case"fee":p=m.value;break;case"free_shipping":p=f.value;break;case"no_delivery":p=y.value;break}P=p[e].area_ids,R.value=[],p.forEach((_,o)=>{e!=o&&R.value.push(..._.area_ids)}),x.value=!0},te=B(()=>(A.value.forEach(d=>{d.child.forEach(e=>{e.disabled=R.value.includes(e.id)})}),A.value)),F=S(),re=()=>{const d=F.value.getCheckedNodes(!1,!1),e=[],p=[];switch(d.forEach(_=>{_.level==2&&(e.push(_.id),p.push(_.name))}),w.type){case"fee":m.value[w.index].area_ids=e,m.value[w.index].fee_area_names=p.toString();break;case"free_shipping":f.value[w.index].area_ids=e,f.value[w.index].free_shipping_area_names=p.toString();break;case"no_delivery":y.value[w.index].area_ids=e,y.value[w.index].no_delivery_area_names=p.toString();break}x.value=!1},ie=()=>{F.value.setCheckedKeys(P,!1)},ne=async d=>{if(V.value||!d)return;const e=u.template_id?Re:ze;await d.validate(async p=>{if(p){if(u.is_free_shipping&&f.value.length==0){Q.error(t("freeShippingPlaceholder"));return}if(u.no_delivery&&y.value.length==0){Q.error("noDeliveryPlaceholder");return}V.value=!0;const _={template_id:u.template_id,template_name:u.template_name,fee_type:u.fee_type,no_delivery:u.no_delivery,is_free_shipping:u.is_free_shipping},o={};m.value.forEach(s=>{s.area_ids.forEach(c=>{o["city_"+c]={city_id:c,fee_area_ids:s.area_ids.toString(),fee_area_names:s.fee_area_names,snum:s.snum,sprice:s.sprice,xnum:s.xnum,xprice:s.xprice}})}),f.value.forEach(s=>{s.area_ids.forEach(c=>{o["city_"+c]?Object.assign(o["city_"+c],{free_shipping_area_ids:s.area_ids.toString(),free_shipping_area_names:s.free_shipping_area_names,free_shipping_num:s.free_shipping_num,free_shipping_price:s.free_shipping_price}):o["city_"+c]={city_id:c,free_shipping_area_ids:s.area_ids.toString(),free_shipping_area_names:s.free_shipping_area_names,free_shipping_num:s.free_shipping_num,free_shipping_price:s.free_shipping_price}})}),y.value.forEach(s=>{s.area_ids.forEach(c=>{o["city_"+c]?Object.assign(o["city_"+c],{no_delivery_area_ids:s.area_ids.toString(),no_delivery_area_names:s.no_delivery_area_names}):o["city_"+c]={city_id:c,no_delivery_area_ids:s.area_ids.toString(),no_delivery_area_names:s.no_delivery_area_names}})}),_.area=Object.values(o),e(_).then(()=>{V.value=!1,j.push({path:"/shop/order/shipping/template"})}).catch(()=>{V.value=!1})}})},Z=()=>{j.push({path:"/shop/order/shipping/template"})};return(d,e)=>{const p=Ve,_=ke,o=Se,s=we,c=Ee,oe=Ce,g=xe,k=Ue,K=De,G=Be,se=Ne,de=Ae,pe=Te,ue=Me;return U(),O(be,null,[b("div",Ke,[l(_,{class:"card !border-none",shadow:"never"},{default:n(()=>[l(p,{content:r(Y),icon:r(he),onBack:Z},null,8,["content","icon"])]),_:1}),l(_,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:n(()=>[W((U(),H(se,{model:u,"label-width":"120px",ref_key:"formRef",ref:q,rules:r($),class:"page-form"},{default:n(()=>[l(s,{label:r(t)("templateName"),prop:"template_name"},{default:n(()=>[l(o,{modelValue:u.template_name,"onUpdate:modelValue":e[0]||(e[0]=a=>u.template_name=a),modelModifiers:{trim:!0},clearable:"",placeholder:r(t)("templateNamePlaceholder"),class:"input-width",maxlength:"60"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(s,{label:r(t)("feeTypeName"),prop:"fee_type"},{default:n(()=>[l(oe,{modelValue:u.fee_type,"onUpdate:modelValue":e[1]||(e[1]=a=>u.fee_type=a)},{default:n(()=>[l(c,{label:"num",size:"large"},{default:n(()=>[h(v(r(t)("num")),1)]),_:1}),l(c,{label:"weight",size:"large"},{default:n(()=>[h(v(r(t)("weight")),1)]),_:1}),l(c,{label:"volume",size:"large"},{default:n(()=>[h(v(r(t)("volume")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),l(s,{label:r(t)("feeSetting"),prop:"fee_data"},{default:n(()=>[l(K,{data:m.value,style:{width:"100%"},size:"default"},{default:n(()=>[l(g,{label:r(t)("deliveryArea")},{default:n(({row:a,$index:i})=>[b("div",Ie,[i?(U(),O("span",{key:0,onClick:E=>z("fee",i),class:"cursor-pointer"},v(a.fee_area_names?a.fee_area_names:r(t)("areaPlaceholder")),9,Oe)):(U(),O("span",We,v(a.fee_area_names?a.fee_area_names:r(t)("areaPlaceholder")),1))])]),_:1},8,["label"]),l(g,{label:r(D).first},{default:n(({$index:a})=>[l(o,{modelValue:m.value[a].snum,"onUpdate:modelValue":i=>m.value[a].snum=i,modelModifiers:{trim:!0},maxlength:"8",onKeyup:e[2]||(e[2]=i=>r(C)(i)),onBlur:i=>m.value[a].snum=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(t)("fee")},{default:n(({$index:a})=>[l(o,{modelValue:m.value[a].sprice,"onUpdate:modelValue":i=>m.value[a].sprice=i,modelModifiers:{trim:!0},maxlength:"8",onKeyup:e[3]||(e[3]=i=>r(C)(i)),onBlur:i=>m.value[a].sprice=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(D).continue},{default:n(({$index:a})=>[l(o,{modelValue:m.value[a].xnum,"onUpdate:modelValue":i=>m.value[a].xnum=i,modelModifiers:{trim:!0},maxlength:"8",onKeyup:e[4]||(e[4]=i=>r(C)(i)),onBlur:i=>m.value[a].xnum=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(t)("continueFee")},{default:n(({$index:a})=>[l(o,{modelValue:m.value[a].xprice,"onUpdate:modelValue":i=>m.value[a].xprice=i,modelModifiers:{trim:!0},onKeyup:e[5]||(e[5]=i=>r(C)(i)),maxlength:"8",onBlur:i=>m.value[a].xprice=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(t)("operation"),align:"right",width:"150"},{default:n(({$index:a})=>[a?(U(),H(k,{key:0,type:"primary",onClick:i=>M("fee",a),link:""},{default:n(()=>[h(v(r(t)("delete")),1)]),_:2},1032,["onClick"])):ge("",!0)]),_:1},8,["label"])]),_:1},8,["data"]),b("div",je,[l(k,{type:"primary",onClick:e[6]||(e[6]=a=>T("fee"))},{default:n(()=>[h(v(r(t)("addDeliveryArea")),1)]),_:1})])]),_:1},8,["label"]),l(s,{label:r(t)("freeShipping"),prop:"is_free_shipping"},{default:n(()=>[l(G,{modelValue:u.is_free_shipping,"onUpdate:modelValue":e[7]||(e[7]=a=>u.is_free_shipping=a),size:"small","inactive-value":0,"active-value":1},null,8,["modelValue"])]),_:1},8,["label"]),W(l(s,{prop:"free_shipping_data"},{default:n(()=>[l(K,{data:f.value,style:{width:"100%"},size:"default"},{default:n(()=>[l(g,{label:r(t)("freeShippingArea")},{default:n(({row:a,$index:i})=>[b("div",qe,[l(o,{modelValue:a.free_shipping_area_names,"onUpdate:modelValue":E=>a.free_shipping_area_names=E,modelModifiers:{trim:!0},placeholder:r(t)("areaPlaceholder"),readonly:"",onClick:E=>z("free_shipping",i)},null,8,["modelValue","onUpdate:modelValue","placeholder","onClick"])])]),_:1},8,["label"]),l(g,{label:r(L)},{default:n(({$index:a})=>[l(o,{modelValue:f.value[a].free_shipping_num,"onUpdate:modelValue":i=>f.value[a].free_shipping_num=i,modelModifiers:{trim:!0},onKeyup:e[8]||(e[8]=i=>r(C)(i)),maxlength:"8",onBlur:i=>f.value[a].free_shipping_num=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(t)("freeShippingPrice")},{default:n(({$index:a})=>[l(o,{modelValue:f.value[a].free_shipping_price,"onUpdate:modelValue":i=>f.value[a].free_shipping_price=i,modelModifiers:{trim:!0},onKeyup:e[9]||(e[9]=i=>r(C)(i)),maxlength:"8",onBlur:i=>f.value[a].free_shipping_price=i.target.value},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1},8,["label"]),l(g,{label:r(t)("operation"),align:"right",width:"150"},{default:n(({$index:a})=>[l(k,{type:"primary",onClick:i=>M("free_shipping",a),link:""},{default:n(()=>[h(v(r(t)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"]),b("div",Le,v(r(t)("freeShippingAreaTips")),1),b("div",Ze,[l(k,{type:"primary",onClick:e[10]||(e[10]=a=>T("free_shipping"))},{default:n(()=>[h(v(r(t)("addFreeShippingArea")),1)]),_:1})])]),_:1},512),[[J,u.is_free_shipping]]),l(s,{label:r(t)("noDelivery"),prop:"no_delivery"},{default:n(()=>[l(G,{modelValue:u.no_delivery,"onUpdate:modelValue":e[11]||(e[11]=a=>u.no_delivery=a),size:"small","inactive-value":0,"active-value":1},null,8,["modelValue"])]),_:1},8,["label"]),W(l(s,{prop:"no_delivery_data"},{default:n(()=>[l(K,{data:y.value,style:{width:"100%"},size:"default"},{default:n(()=>[l(g,{label:r(t)("noDelivery")},{default:n(({row:a,$index:i})=>[b("div",Ge,[l(o,{modelValue:a.no_delivery_area_names,"onUpdate:modelValue":E=>a.no_delivery_area_names=E,modelModifiers:{trim:!0},readonly:"",onClick:E=>z("no_delivery",i),placeholder:r(t)("areaPlaceholder")},null,8,["modelValue","onUpdate:modelValue","onClick","placeholder"])])]),_:1},8,["label"]),l(g,{label:r(t)("operation"),align:"right",width:"150"},{default:n(({$index:a})=>[l(k,{type:"primary",onClick:i=>M("no_delivery",a),link:""},{default:n(()=>[h(v(r(t)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"]),b("div",He,[l(k,{type:"primary",onClick:e[12]||(e[12]=a=>T("no_delivery"))},{default:n(()=>[h(v(r(t)("addNoDelivery")),1)]),_:1})])]),_:1},512),[[J,u.no_delivery]])]),_:1},8,["model","rules"])),[[ue,V.value]])]),_:1}),b("div",Je,[b("div",Qe,[l(k,{type:"primary",onClick:e[13]||(e[13]=a=>ne(q.value)),disabled:V.value},{default:n(()=>[h(v(r(t)("save")),1)]),_:1},8,["disabled"]),l(k,{onClick:e[14]||(e[14]=a=>Z())},{default:n(()=>[h(v(r(t)("cancel")),1)]),_:1})])])]),l(pe,{modelValue:x.value,"onUpdate:modelValue":e[16]||(e[16]=a=>x.value=a),title:r(t)("selectArea"),width:"80%",class:"diy-dialog-wrap","destroy-on-close":!0,onOpened:ie},{footer:n(()=>[b("span",Xe,[l(k,{onClick:e[15]||(e[15]=a=>x.value=!1)},{default:n(()=>[h(v(r(t)("cancel")),1)]),_:1}),l(k,{type:"primary",loading:V.value,onClick:re},{default:n(()=>[h(v(r(t)("confirm")),1)]),_:1},8,["loading"])])]),default:n(()=>[l(de,{height:"50vh"},{default:n(()=>[l(r(ye),{data:r(te),props:{children:"child",label:"name"},"default-expand-all":"","show-checkbox":"",ref_key:"areaTreeRef",ref:F,"default-checked-keys":r(P),"node-key":"id"},null,8,["data","default-checked-keys"])]),_:1})]),_:1},8,["modelValue","title"])],64)}}});const ka=Fe(Ye,[["__scopeId","data-v-2079f040"]]);export{ka as default};
