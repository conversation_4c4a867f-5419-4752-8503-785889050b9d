import{y as Je,f as Ke,r as R,n as h,b4 as Z,l as Ye,q as n,$ as d,aH as ae,a6 as M,aB as H,I as le}from"./index-30109030.js";import{S as Qe}from"./sortable.esm-be94e56d.js";import{b as Ue,g as We,E as Xe,C as et,D as tt,af as st,ag as rt,ah as ce}from"./goods-e07707eb.js";import{j as ot}from"./poster-65e59011.js";import{u as it}from"./diy_form-59d7f2cd.js";import{r as nt}from"./range-77a5ce89.js";function ht(v={}){const pe=Je(),b=Ke(),D=R(!1),o=h({goods_id:"",goods_type:"real",goods_name:"",sub_title:"",goods_image:"",goods_video:"",goods_category:"",brand_id:"",poster_id:"",form_id:"",label_ids:[],service_ids:[],supplier_id:"",status:"1",is_gift:0,sort:"",addon_shop_supplier:[],spec_type:"single",price:"",market_price:"",cost_price:"",stock:"",sku_no:"",unit:"",virtual_sale_num:"",member_discount:"",is_limit:0,limit_type:1,max_buy:"",min_buy:"",attr_ids:[],attr_format:[],goods_desc:"",skuCheckAll:!1,skuIsIndeterminate:!1,skuCheckedCities:[]});Object.assign(o,v.formData),o.goods_id=R(pe.query.goods_id);const _e=v.appendFormData;Object.assign(o,_e);const y=v.appendRefreshGoodsSkuData||{},z=v.appendSingleGoodsData,J=v.getFormRules,w=h({}),x=v.getFormRef,A=h([]);Z(()=>{let e=x();for(let r in e)w[r]=e[r];v.getVerify&&A.splice(0,A.length,...v.getVerify())});const ue=v.editApi,fe=v.addApi,O=R("basic"),de=(e,r)=>{},K=h([]),ge=e=>{b.push(e.path)};Ue().then(e=>{const r=e.data;if(r)for(const t in r)K.push(r[t])});const P=h([]),me={multiple:!0},he=e=>{},ke=()=>{const e=b.resolve({path:"/shop/goods/category"});window.open(e.href)},Y=(e=!1)=>{We().then(r=>{const t=r.data;if(t){const s=[];t.forEach(l=>{const i=[];l.child_list&&l.child_list.forEach(a=>{i.push({value:a.category_id,label:a.category_name})}),s.push({value:l.category_id,label:l.category_name,children:i})}),P.splice(0,P.length,...s),e&&d({message:n("refreshSuccess"),type:"success"})}})};Y();const I=h([]),ye=()=>{const e=b.resolve({path:"/shop/goods/brand"});window.open(e.href)},Q=(e=!1)=>{Xe({}).then(r=>{const t=r.data;t&&(I.splice(0,I.length,...t),e&&d({message:n("refreshSuccess"),type:"success"}))})};Q();const F=h([]),ve=()=>{const e=b.resolve({path:"/poster/list"});window.open(e.href)},U=(e=!1)=>{ot({type:"shop_goods"}).then(r=>{const t=r.data;t&&(F.splice(0,F.length,...t),e&&d({message:n("refreshSuccess"),type:"success"}))})};U();const j=h([]),be=()=>{const e=b.resolve({path:"/diy_form/list"});window.open(e.href)},W=(e=!1)=>{it({type:"DIY_FORM_GOODS_DETAIL",status:1}).then(r=>{const t=r.data;t&&(j.splice(0,j.length,...t),e&&d({message:n("refreshSuccess"),type:"success"}))})};W();const $=h([]),we=()=>{const e=b.resolve({path:"/shop/goods/label"});window.open(e.href)},X=()=>{et({}).then(e=>{const r=e.data;r&&$.splice(0,$.length,...r)})};X();const G=h([]),Ne=()=>{const e=b.resolve({path:"/shop/goods/service"});window.open(e.href)},ee=()=>{tt({}).then(e=>{const r=e.data;r&&G.splice(0,G.length,...r)})};ee();const L=h([]),Ee=()=>{const e=b.resolve({path:"/shop_supplier/supplier"});window.open(e.href)},te=()=>{st({}).then(e=>{const r=e.data;r&&L.splice(0,L.length,...r)})},f=h([]),p=h({}),V=h([]),se=R(0),N=()=>se.value>0,Se=e=>{if(o.addon_shop_supplier=e.addon_shop_supplier,o.addon_shop_supplier&&o.addon_shop_supplier.status==1&&te(),e.default_sort&&(o.sort=e.default_sort),o.goods_id&&e.goods_info){if(se.value=e.goods_info.active_goods_count,o.goods_name=e.goods_info.goods_name,o.sub_title=e.goods_info.sub_title,o.goods_type=e.goods_info.goods_type,o.goods_image=e.goods_info.goods_image,o.goods_video=e.goods_info.goods_video,o.goods_category=e.goods_info.goods_category,o.brand_id=e.goods_info.brand_id,o.poster_id=e.goods_info.poster_id,o.form_id=e.goods_info.form_id,o.label_ids=e.goods_info.label_ids,o.service_ids=e.goods_info.service_ids,o.supplier_id=e.goods_info.supplier_id,o.status=e.goods_info.status,o.sort=e.goods_info.sort,o.is_gift=e.goods_info.is_gift,o.attr_format=e.goods_info.attr_format,o.attr_ids=e.goods_info.attr_ids,ne(o.attr_ids,!0),o.spec_type=e.goods_info.spec_type,o.stock=e.goods_info.stock,o.spec_type=="single"){const r=e.goods_info.sku_list[0];o.price=r.price,o.market_price=r.market_price,o.cost_price=r.cost_price,o.sku_no=r.sku_no,z&&Object.assign(o,z(r))}else if(o.spec_type=="multi"){e.goods_info.spec_list.forEach(s=>{const l=[];s.spec_values=s.spec_values.split(","),s.spec_values.forEach(i=>{l.push({id:E(),spec_value_name:i})}),f.push({id:E(),spec_id:s.spec_id,goods_id:s.goods_id,spec_name:s.spec_name,values:l})}),T();const t=e.goods_info.sku_list;for(let s in p)for(let l=0;l<t.length;l++){let i=t[l];if(p[s].spec_name==i.sku_spec_format.replace(/,/g," ")){p[s].sku_id=i.sku_id,p[s].sku_image=i.sku_image,p[s].price=i.price,p[s].market_price=i.market_price,p[s].cost_price=i.cost_price;for(let a in y)p[s][a]=i[a];p[s].stock=i.stock,p[s].sku_id=i.sku_id,p[s].sku_no=i.sku_no,p[s].is_default=i.is_default;break}}Z(()=>{C(),re()})}o.member_discount=e.goods_info.member_discount,o.unit=e.goods_info.unit,o.virtual_sale_num=e.goods_info.virtual_sale_num,o.is_limit=e.goods_info.is_limit,o.limit_type=e.goods_info.limit_type,o.max_buy=e.goods_info.max_buy,o.min_buy=e.goods_info.min_buy,o.goods_desc=e.goods_info.goods_desc}},re=()=>{if(!N()&&x().specValueRef)for(let e=0;e<x().specValueRef.length;e++){const r=x().specValueRef[e],t=Qe.create(r,{group:"draggable-element-"+e,animation:200,onEnd:s=>{const l=f[e].values[s.oldIndex];f[e].values.splice(s.oldIndex,1),f[e].values.splice(s.newIndex,0,l),Z(()=>{t.sort(nt(f[e].values.length).map(i=>i.toString())),T(),C()})}})}},E=(e=5)=>Number(Math.random().toString().substr(3,e)+Date.now()).toString(36),Te=()=>{if(N()){d({type:"warning",message:`${n("participateInActiveDisableTips")}`});return}if(f.length>4){d({type:"warning",message:`${n("maxAddSpecTips")}`});return}f.push({id:E(),spec_name:"",values:[{id:E(),spec_value_name:""}]})},Ce=e=>{if(N()){d({type:"warning",message:`${n("participateInActiveDisableTips")}`});return}f.splice(e,1),T(),C(),B()},De=e=>{if(N()){d({type:"warning",message:`${n("participateInActiveDisableTips")}`});return}f[e].values.push({id:E(),spec_value_name:""}),re()},xe=ae(e=>{T(),C()}),Oe=(e,r)=>{if(N()){d({type:"warning",message:`${n("participateInActiveDisableTips")}`});return}f[e].values.splice(r,1),T(),C(),B()},Re=(e,r)=>{for(const t in p)t==r?p[t].is_default=e:p[t].is_default=0},B=ae(()=>{let e=0;for(const r in p)p[r].stock&&(e+=parseInt(p[r].stock));o.stock=e}),T=()=>{const e=f,r=M(p);let t={},s=0;for(const i of e){let a={};if(Object.keys(t).length>0)for(const c in t)for(let u of i.values){let k=M(t[c].sku_spec);k.push(u),a["sku_"+s]={spec_name:`${t[c].spec_name} ${u.spec_value_name}`,sku_spec:k,sku_image:"",price:"",market_price:"",cost_price:"",stock:"",sku_no:"",is_default:0};for(let S in y)a["sku_"+s][S]=y[S].value;s++}else for(let c of i.values){let u=c.spec_value_name;a["sku_"+s]={spec_name:u,sku_spec:[c],sku_image:"",price:"",market_price:"",cost_price:"",stock:"",sku_no:"",is_default:0};for(let k in y)a["sku_"+s][k]=y[k].value;s++}t=Object.keys(a).length>0?a:t}for(const i in r)for(const a in t)if(Ae(r[i].sku_spec,t[a].sku_spec)===t[a].sku_spec.length){const u=t[a].spec_name,k=t[a].sku_spec;Object.assign(t[a],r[i]),t[a].spec_name=u,t[a].sku_spec=k;break}for(const i in p)delete p[i];let l="";for(const i in t)l==""?(l=i,t[i].is_default=1):t[i].is_default=0,p[i]=t[i];o.skuCheckAll=!1,o.skuIsIndeterminate=!1,o.skuCheckedCities=[]},Ae=(e,r)=>{let t=0;for(let s=0;s<e.length;s++)for(let l=0;l<r.length;l++)if(e[s].id===r[l].id){t++;break}return t},C=()=>{let e=0;for(let s=0;s<f.length;s++)f[s].spec_name!=""&&f[s].values.length>0&&e++;let r=1;const t=[];for(let s=e-1;s>=0;s--){for(let l=0;l<Object.keys(p).length;)if(f[s].values.length>0)for(let i of f[s].values)t.push({index:l,colSpan:s,rowSpan:r,spec_value_name:i.spec_value_name}),l=l+r;else l++;r=r*f[s].values.length}t.reverse(),V.splice(0,V.length,...t)},_=h({spec:"",price:"",market_price:"",cost_price:"",stock:"",sku_no:""});var oe={};for(let e in y)oe[e]=y[e].value;Object.assign(_,oe);const Pe=e=>{o.skuIsIndeterminate=!1,e?o.skuCheckedCities=Object.keys(p):o.skuCheckedCities=[]},Ie=e=>{const r=e.length;o.skuCheckAll=r===Object.keys(p).length,o.skuIsIndeterminate=r>0&&r<Object.keys(p).length},Fe=()=>{if(o.skuCheckedCities.length==0){d({type:"warning",message:`${n("pleaseSelectSku")}`});return}if(_.price&&(isNaN(_.price)||!g.digit.test(_.price))){d({type:"warning",message:`${n("priceTips")}`});return}if(_.market_price&&(isNaN(_.market_price)||!g.digit.test(_.market_price))){d({type:"warning",message:`${n("marketPriceTips")}`});return}if(_.cost_price&&(isNaN(_.cost_price)||!g.digit.test(_.cost_price))){d({type:"warning",message:`${n("costPriceTips")}`});return}if(_.stock&&(isNaN(_.stock)||!g.number.test(_.stock))){d({type:"warning",message:`${n("stockTips")}`});return}for(let e in y){let r=g[y[e].regExp],t=y[e].message;if(_[e]&&(isNaN(_[e])||!r.test(_[e]))){d({type:"warning",message:t});return}}o.skuCheckedCities.forEach(e=>{_.price&&(p[e].price=_.price),_.market_price&&(p[e].market_price=_.market_price),_.cost_price&&(p[e].cost_price=_.cost_price),_.stock&&(p[e].stock=_.stock);for(let r in y)_[r]&&(p[e][r]=_[r]);_.sku_no&&(p[e].sku_no=_.sku_no)}),_.price="",_.market_price="",_.cost_price="",_.stock="",_.sku_no="";for(let e in y)_[e]=""},g={required:/[\S]+/,number:/^\d{0,10}$/,digit:/^\d{0,10}(.?\d{0,2})$/,special:/^\d{0,10}(.?\d{0,3})$/},je=Ye(()=>{let e={goods_name:[{required:!0,trigger:"blur",validator:(r,t,s)=>{t===""&&s(new Error(n("goodsNamePlaceholder"))),t.length>60?s(new Error(n("goodsNameMaxLengthTips"))):s()}}],sub_title:[{trigger:"blur",validator:(r,t,s)=>{t.length>80?s(new Error(n("subTitleMaxLengthTips"))):s()}}],goods_image:[{required:!0,message:n("goodsImagePlaceholder"),trigger:"blur"}],goods_category:[{required:!0,message:n("goodsCategoryPlaceholder"),trigger:"blur"}],sort:[{trigger:"blur",validator:(r,t,s)=>{isNaN(t)||!g.number.test(t)?s(new Error(n("sortTips"))):s()}}],price:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="single"?t===""?s(new Error(n("pricePlaceholder"))):isNaN(t)||!g.digit.test(t)?s(new Error(n("priceTips"))):t<0?s(new Error(n("priceNotZeroTips"))):s():s()}}],market_price:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="single"?isNaN(t)||!g.digit.test(t)?s(new Error(n("marketPriceTips"))):t<0?s(new Error(n("marketPriceNotZeroTips"))):s():s()}}],cost_price:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="single"?isNaN(t)||!g.digit.test(t)?s(new Error(n("costPriceTips"))):t<0?s(new Error(n("costPriceNotZeroTips"))):s():s()}}],stock:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="single"?t===""?s(new Error(n("stockPlaceholder"))):isNaN(t)||!g.number.test(t)?s(new Error(n("stockTips"))):t<0?s(new Error(n("stockNotZeroTips"))):s():s()}}],virtual_sale_num:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="single"?isNaN(t)||!g.number.test(t)?s(new Error(n("virtualSaleNumTips"))):t<0?s(new Error(n("virtualSaleNumNotZeroTips"))):s():s()}}],spec_type:[{trigger:"blur",validator:(r,t,s)=>{o.spec_type=="multi"&&Object.keys(p).length==0&&s(new Error(n("pleaseEditSpecPlaceholder"))),s()}}],max_buy:[{trigger:"blur",validator:(r,t,s)=>{t===""?s(new Error(n("maxBuyPlaceholder"))):isNaN(t)||!g.number.test(t)?s(new Error(n("maxBuyTips"))):t<1?s(new Error(n("maxBuyNotZeroTips"))):s()}}],min_buy:[{trigger:"blur",validator:(r,t,s)=>{isNaN(t)||!g.number.test(t)?s(new Error(n("minBuyFormatErrorTips"))):t<0?s(new Error(n("minBuyNotZeroTips"))):o.is_limit==1&&t>Number(o.max_buy)?s(new Error(n("minBuyGreaterThanMaxBuyTips"))):s()}}],goods_desc:[{required:!0,trigger:["blur","change"],validator:(r,t,s)=>{if(t==="")s(new Error(n("goodsDescPlaceholder")));else{if(t.length<5||t.length>5e4)return s(new Error(n("goodsDescMaxTips"))),!1;s()}}}]};return J&&Object.assign(e,J(o,g)),e}),$e=()=>[{trigger:"blur",validator:(e,r,t)=>{o.spec_type=="multi"?r.length==0?t(n("pricePlaceholder")):isNaN(r)||!g.digit.test(r)?t(n("priceTips")):r<0?t(n("priceNotZeroTips")):t():t()}}],Ge=()=>[{trigger:"blur",validator:(e,r,t)=>{o.spec_type=="multi"?isNaN(r)||!g.digit.test(r)?t(n("marketPriceTips")):r<0?t(n("marketPriceNotZeroTips")):t():t()}}],Le=()=>[{trigger:"blur",validator:(e,r,t)=>{o.spec_type=="multi"?isNaN(r)||!g.digit.test(r)?t(n("costPriceTips")):r<0?t(n("costPriceNotZeroTips")):t():t()}}],Ve=()=>[{trigger:"blur",validator:(e,r,t)=>{o.spec_type=="multi"?r.length==0?t(n("stockPlaceholder")):isNaN(r)||!g.number.test(r)?t(n("stockTips")):r<0?t(n("stockNotZeroTips")):t():t()}}],Be=e=>{let r=[{key:"basic",verify:!1,ref:w.basicFormRef},{key:"price_stock",verify:!1,ref:w.priceStockFormRef},{key:"price_stock",verify:!1,ref:w.skuFormRef},{key:"price_stock",verify:!1,ref:w.priceStockCommonFormRef}];r=r.concat(A);let t={key:"detail",verify:!1,ref:w.detailFormRef};if(r.push(t),r.forEach((s,l)=>{s.ref.validate(i=>{s.verify=i})}),o.spec_type=="multi"){let s=!0,l=[],i=[];for(let c=0;c<f.length;c++){const u=f[c];if(le.require(u.spec_name)){s=!1,d({type:"warning",message:`${n("specNameRequire")}`});break}if(l.indexOf(u.spec_name)>-1){s=!1,d({type:"warning",message:`${n("specNameRepeat")}`});break}else l.push(u.spec_name);if(u.values.length)for(let k=0;k<u.values.length;k++){const S=u.values[k];if(le.require(S.spec_value_name)){s=!1,d({type:"warning",message:`${n("specValueRequire")}`});break}if(i.indexOf(S.spec_value_name)>-1){s=!1,d({type:"warning",message:`${n("specValueNameRepeat")}`});break}else i.push(S.spec_value_name)}else s=!1,d({type:"warning",message:`${n("specValueRequire")}`});if(!s)break}if(!s){O.value="price_stock";return}let a=!1;for(const c in p)p[c].is_default&&(a=!0);if(!a){O.value="price_stock",d({type:"warning",message:`${n("lackDefaultSpec")}`});return}}setTimeout(()=>{let s=!0;for(let l=0;l<r.length;l++)if(r[l].verify==!1){O.value=r[l].key,s=!1;break}s&&e&&e()},10)},qe=(e=null)=>{Be(()=>{if(D.value)return;D.value=!0;const r=o.goods_id?ue:fe,t=M(o);if(t.spec_type=="multi"){t.stock=0;for(const i in p)p[i].stock&&(t.stock+=parseInt(p[i].stock))}const s=[];t.goods_category.forEach(i=>{typeof i=="object"?i.forEach(a=>{s.indexOf(a)==-1&&s.push(a)}):s.indexOf(i)==-1&&s.push(i)}),t.goods_category=s,t.goods_sku_data=p,t.goods_spec_format=f,t.attr_format=[],H(m).forEach((i,a)=>{if(i.attr_value_name&&i.select_child_val||i.attr_value_id>0){let c={};c.attr_id=i.attr_id,c.attr_value_id=i.attr_value_id,c.attr_value_name=i.attr_value_name,c.type=i.type,c.sort=i.sort?i.sort:0,c.attr_child_value_id=i.select_child_name,c.attr_child_value_name=i.select_child_val,t.attr_format.push(c)}}),t.attr_format.sort((i,a)=>a.sort-i.sort),e&&Object.assign(t,e(t)),r(t).then(i=>{D.value=!1,b.push("/shop/goods/list")}).catch(()=>{D.value=!1})})},Ze=()=>{b.push("/shop/goods/list")},Me=e=>{e.target.value=e.target.value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g,""),e.target.value=e.target.value.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g,"")},He=e=>{var r;(r=w.detailFormRef)==null||r.validateField("goods_desc")},ze=e=>{if(!e.target.value)return!1;const r={goods_id:o.goods_id,sku_no:e.target.value};rt(r).then(t=>{})},q=h([]),ie=()=>{ce({}).then(e=>{q.splice(0,q.length,...e.data)})};ie();const m=h([]),ne=(e=[],r=!1)=>{if(e.length==0){let t=H(m);t=t.filter(s=>s.attr_value_id<0),m.splice(0,m.length,...t);return}ce({attr_id_arr:e}).then(t=>{let s=t.data;if(s&&Object.keys(s).length){let l=H(m);l=l.filter(a=>a.attr_value_id>0?e.indexOf(a.attr_id)!=-1:!0);let i=l.map(a=>a.attr_value_id);if(s.forEach(a=>{if(a.attr_value_format){let c=JSON.parse(a.attr_value_format);i.length&&i.forEach(u=>{c=c.filter(k=>k.attr_value_id!=u)}),c.filter(u=>{u.attr_id=a.attr_id,u.select_child_name=u.type=="checkbox"?[]:"",u.select_child_val=u.type=="checkbox"?[]:""}),c.sort((u,k)=>k.sort-u.sort),l=l.concat(c),m.splice(0,m.length,...l)}}),r){m.forEach(a=>{for(let c=0;c<o.attr_format.length;c++)if(o.attr_format[c].attr_value_id==a.attr_value_id){a.select_child_name=o.attr_format[c].attr_child_value_id,a.select_child_val=o.attr_format[c].attr_child_value_name,a.sort=o.attr_format[c].sort;break}});for(let a=0;a<o.attr_format.length;a++)if(o.attr_format[a].attr_value_id<0){let c=o.attr_format[a],u={attr_id:c.attr_id,attr_value_id:c.attr_value_id,attr_value_name:c.attr_value_name,sort:c.sort?c.sort:0,type:"text",select_child_name:c.attr_child_value_id,select_child_val:c.attr_child_value_name};m.push(u)}}}})};return{formData:o,activeName:O,tabHandleClick:de,goodsType:K,changeGoodsType:ge,goodsCategoryOptions:P,goodsCategoryProps:me,categoryHandleChange:he,toGoodsCategoryEvent:ke,refreshGoodsCategory:Y,brandOptions:I,toGoodsBrandEvent:ye,refreshGoodsBrand:Q,posterOptions:F,toPosterEvent:ve,refreshGoodsPoster:U,diyFormOptions:j,toDiyFormEvent:be,refreshDiyForm:W,labelOptions:$,toGoodsLabelEvent:we,refreshGoodsLabel:X,serviceOptions:G,toGoodsServiceEvent:Ne,refreshGoodsService:ee,supplierOptions:L,toSupplierEvent:Ee,refreshSupplier:te,goodsSpecFormat:f,goodsSkuData:p,specData:V,generateRandom:E,isDisabledPrice:N,addSpec:Te,deleteSpec:Ce,addSpecValue:De,specValueNameInputListener:xe,deleteSpecValue:Oe,specValueIsDefaultChangeListener:Re,specStockSum:B,batchOperation:_,skuHandleCheckAllChange:Pe,handleCheckedCitiesChange:Ie,saveBatch:Fe,regExp:g,formRules:je,skuPriceRules:$e,skuMarketPriceRules:Ge,skuCostPriceRules:Le,skuStockRules:Ve,handleGoodsInit:Se,save:qe,back:Ze,filterSpecial:Me,handleBlur:He,attrOptions:q,attrChange:ne,getAttrListFn:ie,attrTableData:m,addAttr:()=>{let e={attr_id:"",attr_value_id:"",attr_value_name:"",child:{id:1,name:""},sort:0,type:"text",select_child_name:"",select_child_val:""};e.attr_value_id=-Math.floor(new Date().getSeconds()+Math.floor(new Date().getMilliseconds())),e.sort=m.length+1,m.push(e)},delAttr:e=>{m.splice(e,1)},attrRadioChange:(e,r)=>{m.forEach((t,s,l)=>(t.type=="radio"&&t.child.forEach((i,a)=>{i.id==r&&(l[s].select_child_name=i.id,l[s].select_child_val=i.name)}),t))},attrCheckboxChange:(e,r)=>{m[e].select_child_val=[],m[e].child.forEach((t,s)=>{r.indexOf(t.id)>-1&&m[e].select_child_val.push(t.name)})},goodsVerifyFn:ze}}export{ht as u};
