import{d as P,y as C,r as f,m as L,n as V,q as o,h as g,c as x,Z as v,s as b,w as s,e as d,a as i,t as m,u as t,C as h,_ as S,i as B,cu as M,cv as T,cw as D,G as R,j as q,L as W,M as j,a9 as A,N as G,E as O,a3 as Z}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                *//* empty css                */import $ from"./index-e3ceb692.js";import"./el-form-item-4ed993c7.js";/* empty css                 */import{_ as z}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";/* empty css                  *//* empty css                   */import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./sortable.esm-be94e56d.js";const H={class:"main-container"},J={class:"text-[16px] text-[#1D1F3A] font-bold mb-4"},K={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},Q={class:"text-[12px] text-[#a9a9a9]"},X={class:"text-[12px] text-[#a9a9a9]"},Y={class:"text-[12px] text-[#a9a9a9]"},ee={class:"text-[12px] text-[#a9a9a9]"},le={class:"mt-[20px]"},oe={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},te={key:2,class:"mt-[20px]"},ae={class:"panel-title !text-[14px] bg-[#F4F5F7] p-3 border-[#E6E6E6] border-solid border-b-[1px]"},de={class:"fixed-footer-wrap"},se={class:"fixed-footer"},ie=P({__name:"system",setup(ne){const y=C().meta.title,p=f(!0),c=f(L()),l=V({site_name:"",logo:"",desc:"",latitude:"",keywords:"",longitude:"",province_id:"",city_id:"",district_id:"",address:"",full_address:"",business_hours:"",phone:"",front_end_name:"",front_end_logo:"",front_end_icon:"",icon:"",wechat_code:"",enterprise_wechat:"",tel:"",site_login_logo:"",site_login_bg_img:""});(async()=>{const _=await(await M()).data;Object.keys(l).forEach(r=>{_[r]!=null&&(l[r]=_[r])});const e=await(await T()).data;l.wechat_code=e.wechat_code,l.enterprise_wechat=e.enterprise_wechat,l.tel=e.tel,l.site_login_logo=e.site_login_logo,l.site_login_bg_img=e.site_login_bg_img,p.value=!1})();const w=f(),E=V({site_name:[{required:!0,message:o("siteNamePlaceholder"),trigger:"blur"}],front_end_name:[{required:!0,message:o("frontEndNamePlaceholder"),trigger:"blur"}]}),U=async _=>{p.value||!_||await _.validate(async e=>{e&&(p.value=!0,D(l).then(()=>{p.value=!1,c.value=="admin"?R().getWebsiteInfo():q().getSiteInfo()}).catch(()=>{p.value=!1}))})};return(_,e)=>{const r=W,n=j,u=$,F=A,k=G,N=O,I=Z;return g(),x("div",H,[v((g(),b(k,{class:"page-form loading-box",model:l,"label-width":"150px",ref_key:"formRef",ref:w,rules:E},{default:s(()=>[d(F,{class:"box-card !border-none",shadow:"never"},{default:s(()=>[i("h3",J,m(t(y)),1),i("h3",K,m(t(o)("websiteInfo")),1),d(n,{label:t(o)("siteName"),prop:"site_name"},{default:s(()=>[d(r,{modelValue:l.site_name,"onUpdate:modelValue":e[0]||(e[0]=a=>l.site_name=a),modelModifiers:{trim:!0},placeholder:t(o)("siteNamePlaceholder"),class:"input-width",clearable:"",maxlength:"20","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:t(o)("logo")},{default:s(()=>[i("div",null,[d(u,{modelValue:l.logo,"onUpdate:modelValue":e[1]||(e[1]=a=>l.logo=a)},null,8,["modelValue"]),i("p",Q,m(t(o)("logoPlaceholder")),1)])]),_:1},8,["label"]),d(n,{label:t(o)("icon")},{default:s(()=>[i("div",null,[d(u,{modelValue:l.icon,"onUpdate:modelValue":e[2]||(e[2]=a=>l.icon=a)},null,8,["modelValue"]),i("p",X,m(t(o)("iconPlaceholder")),1)])]),_:1},8,["label"]),c.value=="admin"?(g(),b(n,{key:0,label:t(o)("siteLoginLogo")},{default:s(()=>[i("div",null,[d(u,{modelValue:l.site_login_logo,"onUpdate:modelValue":e[3]||(e[3]=a=>l.site_login_logo=a)},null,8,["modelValue"]),i("p",Y,m(t(o)("siteLoginLogoTips")),1)])]),_:1},8,["label"])):h("",!0),c.value=="admin"?(g(),b(n,{key:1,label:t(o)("siteLoginBgImg")},{default:s(()=>[i("div",null,[d(u,{modelValue:l.site_login_bg_img,"onUpdate:modelValue":e[4]||(e[4]=a=>l.site_login_bg_img=a)},null,8,["modelValue"]),i("p",ee,m(t(o)("siteLoginBgImgTips")),1)])]),_:1},8,["label"])):h("",!0),d(n,{label:t(o)("keywords")},{default:s(()=>[d(r,{modelValue:l.keywords,"onUpdate:modelValue":e[5]||(e[5]=a=>l.keywords=a),modelModifiers:{trim:!0},placeholder:t(o)("keywordsPlaceholder"),class:"input-width",clearable:"",maxlength:"20","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:t(o)("desc")},{default:s(()=>[d(r,{modelValue:l.desc,"onUpdate:modelValue":e[6]||(e[6]=a=>l.desc=a),modelModifiers:{trim:!0},type:"textarea",rows:4,clearable:"",placeholder:t(o)("descPlaceholder"),class:"input-width",maxlength:"100","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),v(i("div",le,[i("h3",oe,m(t(o)("frontEndInfo")),1),d(n,{label:t(o)("frontEndName")},{default:s(()=>[d(r,{modelValue:l.front_end_name,"onUpdate:modelValue":e[7]||(e[7]=a=>l.front_end_name=a),modelModifiers:{trim:!0},placeholder:t(o)("frontEndNamePlaceholder"),class:"input-width",clearable:"",maxlength:"20","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:t(o)("phone")},{default:s(()=>[d(r,{modelValue:l.phone,"onUpdate:modelValue":e[8]||(e[8]=a=>l.phone=a),modelModifiers:{trim:!0},placeholder:t(o)("phonePlaceholder"),class:"input-width",clearable:"",maxlength:"20","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:t(o)("logo")},{default:s(()=>[d(u,{modelValue:l.front_end_logo,"onUpdate:modelValue":e[9]||(e[9]=a=>l.front_end_logo=a)},null,8,["modelValue"])]),_:1},8,["label"]),d(n,{label:t(o)("icon")},{default:s(()=>[d(u,{modelValue:l.front_end_icon,"onUpdate:modelValue":e[10]||(e[10]=a=>l.front_end_icon=a)},null,8,["modelValue"])]),_:1},8,["label"])],512),[[S,c.value=="site"]]),c.value=="admin"?(g(),x("div",te,[i("h3",ae,m(t(o)("serviceInformation")),1),d(n,{label:t(o)("contactsTel")},{default:s(()=>[d(r,{modelValue:l.tel,"onUpdate:modelValue":e[11]||(e[11]=a=>l.tel=a),modelModifiers:{trim:!0},placeholder:t(o)("contactsTelPlaceholder"),class:"input-width",clearable:"",maxlength:"20","show-word-limit":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(n,{label:t(o)("wechatCode")},{default:s(()=>[d(u,{modelValue:l.wechat_code,"onUpdate:modelValue":e[12]||(e[12]=a=>l.wechat_code=a)},null,8,["modelValue"])]),_:1},8,["label"]),d(n,{label:t(o)("customerServiceCode")},{default:s(()=>[d(u,{modelValue:l.enterprise_wechat,"onUpdate:modelValue":e[13]||(e[13]=a=>l.enterprise_wechat=a)},null,8,["modelValue"])]),_:1},8,["label"])])):h("",!0)]),_:1})]),_:1},8,["model","rules"])),[[I,p.value]]),i("div",de,[i("div",se,[d(N,{type:"primary",loading:p.value,onClick:e[14]||(e[14]=a=>U(w.value))},{default:s(()=>[B(m(t(o)("save")),1)]),_:1},8,["loading"])])])])}}});const Ge=z(ie,[["__scopeId","data-v-a4802dc6"]]);export{Ge as default};
