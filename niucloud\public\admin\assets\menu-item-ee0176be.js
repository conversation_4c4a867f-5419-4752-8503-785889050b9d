import{d as y,l as h,u as s,h as n,c as o,s as r,w as c,C as l,a as i,v as m,t as d,F as x,W as w,i as k,d3 as g,cY as N}from"./index-30109030.js";import"./el-menu-item-4ed993c7.js";import"./el-sub-menu-4ed993c7.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import{_ as V}from"./_plugin-vue_export-helper-c27b6911.js";const B={key:0,class:"w-[13px] h-[13px] mr-[10rpx] relative flex justify-center items-center"},C={key:0,class:"w-[13px] h-[13px] mr-[10rpx] relative flex justify-center items-center"},I={key:0,class:"w-[7px] h-[7px] bg-[#DA203E] absolute flex items-center justify-center rounded-full top-[10px] right-[65px]"},j=y({__name:"menu-item",props:{routes:{type:Object,required:!0},level:{type:Number,default:1},isNewVersion:{type:Boolean,default:!1}},setup(e){const a=e,t=h(()=>a.routes.meta);return(E,F)=>{const u=b,p=g,_=N;return s(t).show?(n(),o(x,{key:0},[e.routes.children?(n(),r(p,{key:0,index:String(e.routes.name)},{title:c(()=>[s(t).icon&&a.level!=2?(n(),o("div",B,[s(t).icon&&a.level!=2?(n(),r(u,{key:0,name:s(t).icon,color:"#1D1F3A",class:"absolute !w-auto"},null,8,["name"])):l("",!0)])):l("",!0),i("span",{class:m(["using-hidden",["ml-[10px]",{"text-[15px]":e.routes.meta.class==1},{"text-[14px]":e.routes.meta.class!=1}]])},d(s(t).title),3)]),default:c(()=>[(n(!0),o(x,null,w(e.routes.children,(f,v)=>(n(),r(D,{routes:f,level:a.level+1,key:v,isNewVersion:a.isNewVersion},null,8,["routes","level","isNewVersion"]))),128))]),_:1},8,["index"])):(n(),r(_,{key:1,index:String(e.routes.name),route:e.routes.path},{title:c(()=>[s(t).icon&&a.level!=2?(n(),o("div",C,[s(t).icon&&a.level!=2?(n(),r(u,{key:0,color:"#1D1F3A",name:s(t).icon,class:"absolute !w-auto"},null,8,["name"])):l("",!0)])):l("",!0),i("span",{class:m(["using-hidden",[{"text-[15px]":e.routes.meta.class==1},{"text-[14px]":e.routes.meta.class!=1},{"ml-[10px]":e.routes.meta.class==2,"ml-[15px]":e.routes.meta.class==3}]])},[k(d(s(t).title)+" ",1),s(t).view=="app/upgrade"&&a.isNewVersion?(n(),o("div",I)):l("",!0)],2)]),_:1},8,["index","route"]))],64)):l("",!0)}}});const D=V(j,[["__scopeId","data-v-010c39cf"]]);export{D as default};
