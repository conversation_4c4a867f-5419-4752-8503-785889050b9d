import{u as r}from"./poster-97e95d37.js";import{d as u,l,h as f,c as s,x as c,u as p}from"./index-30109030.js";const v=u({__name:"preview-nickname",props:{value:{type:Object,default:{}}},setup(a,{expose:n}){const e=a;r(),l(()=>e.value);const i=l(()=>{var t="";t+=`font-size: ${e.value.fontSize}px;color: ${e.value.fontColor};line-height: ${e.value.lineHeight+e.value.fontSize}px;`,(e.value.x=="left"||e.value.x=="center"||e.value.x=="right")&&(t+=`text-align: ${e.value.x};`),e.value.weight&&(t+="font-weight: bold;"),(!e.value.fontFamily||e.value.fontFamily=="static/font/SourceHanSansCN-Regular.ttf")&&(t+="font-family: poster_default_font;");let o=document.getElementById(e.value.id);return o?t+=`width:${o.offsetWidth}px;height:${o.offsetHeight}px;`:t+=`width:${e.value.width}px;height:${e.value.height}px;`,t});return n({}),(t,o)=>(f(),s("div",{class:"overflow-hidden",style:c(p(i))},"会员昵称",4))}}),m=Object.freeze(Object.defineProperty({__proto__:null,default:v},Symbol.toStringTag,{value:"Module"}));export{m as _};
