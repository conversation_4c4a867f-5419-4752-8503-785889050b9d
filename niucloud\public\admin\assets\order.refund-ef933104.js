const e="商品名称",o="订单编号",r="退款编号",s="退款时间",n="开始时间",d="结束时间",t="请输入商品名称",c="请输入订单编号",a="请输入退款编号",l="商品信息",i="订单金额",u="实付金额",y="商品金额",f="买家",m="退款金额",p="申请时间",T="退款状态",h="全部",R="退款方式",v="申请退款",N="售后结束",g="买家待退货",P="卖家待收货",M="卖家拒绝",b="订单信息",D="交易流水号",I="订单类型",k="订单来源",A="收货人",F="收货人手机号",S="收货地址",x="商品信息",G="价格",C="数量",E="优惠金额",B="配送金额",L="订单日志",O="订单状态",V="请选择订单状态",_="订单类型",j="请选择订单类型",q="支付时间",w="商品",z="单价（元）/数量",H="实付金额（元）",J="买家/收货人",K="配送方式",Q="件",U="支付方式",W="备注",X="修改地址",Y="提醒",Z="如果未发货，请点击同意退款给买家。",$="如果实际已发货，请主动与买家联系。",ee="如果订单整体退款后，优惠券和余额会退还给买家。",oe="关闭订单",re="确认收货",se="订单发货",ne="请选择配送方式",de="物流公司",te="请选择物流公司",ce="物流单号",ae="请输入物流单号",le="请选择订单项",ie="虚拟发货",ue="关闭订单后该订单将无法支付，是否确认关闭？",ye="是否确认用户已经收货？",fe="请选择要发货的商品",me="买家留言",pe="订单详情",Te="物流信息",he="发货时间",Re="物流公司",ve="物流单号",Ne="物流包裹信息",ge="发货信息",Pe="物流信息",Me="自提点名称",be="自提点地址",De="自提点电话",Ie="营业时间",ke="发货状态",Ae="退款原因",Fe="售后信息",Se="退款拒绝",xe="同意退款",Ge="同意",Ce="申请金额",Ee="拒绝",Be="转账",Le="拒绝原因",Oe="请输入拒绝原因",Ve="确认收货",_e="确定商品收到了吗？",je="同意买家收货",qe="退货地址",we="申请凭证",ze="退款描述",He="退款金额",Je="请输入退款金额",Ke="请输入退货地址",Qe="物流公司",Ue="物流说明",We="暂无数据",Xe="关闭售后",Ye="建议先与用户沟通协商，确认后再关闭售后流程。请谨慎操作，确定要关闭售后吗？",Ze={goodsName:e,orderNo:o,orderRefundNo:r,refundTime:s,startDate:n,endDate:d,goodsNamePlaceholder:t,orderNoPlaceholder:c,orderRefundNoPlaceholder:a,goodsInfo:l,orderMoney:i,realityMoney:u,goodsMoney:y,buyMember:f,refundMoney:m,createTime:p,refundStatus:T,all:h,refundType:R,applyForRefund:v,refundEnd:N,toBeReturned:g,receivedGoods:P,refundRefuse:M,orderInfo:b,outTradeNo:D,orderType:I,orderForm:k,takerName:A,takerMobile:F,takerFullAddress:S,goodsDetail:x,price:G,num:C,preferentialMoney:E,deliveryMoney:B,operateLog:L,orderStatus:O,orderStatusPlaceholder:V,orderFrom:_,orderFromPlaceholder:j,payTime:q,orderGoods:w,goodsPriceNumber:z,detailOrderMoney:H,buyInfo:J,deliveryType:K,piece:Q,payType:U,notes:W,editAddress:X,remind:Y,remindTips1:Z,remindTips2:$,remindTips3:ee,close:oe,finish:re,delivery:se,deliveryTypePlaceholder:ne,company:de,companyPlaceholder:te,expressNumber:ce,expressNumberPlaceholder:ae,orderGoodsIdsPlaceholder:le,virtualDelivery:ie,orderCloseTips:ue,orderFinishTips:ye,orderGoodsPlaceholder:fe,memberRemark:me,discountMoney:pe,orderDelivery:Te,devliveryTime:he,companyName:Re,logisticNo:ve,packageInfo:Ne,deliveryInfo:ge,logisticInfo:Pe,storeName:Me,storeAddress:be,storeMobile:De,tradeTime:Ie,deliveryStatusName:ke,refundReason:Ae,afterSales:Fe,orderRefundRefuse:Se,orderRefundAgree:xe,agree:Ge,applyMoney:Ce,refuse:Ee,transferAccounts:Be,refuseReason:Le,shopReasonPlaceholder:Oe,confirmDelivery:Ve,orderDeliveryTips:_e,agreeRefundDelivery:je,refundDeliveryAddress:qe,refundVoucher:we,refundRemark:ze,agreeMoney:He,moneyPlaceholder:Je,refundAddressPlaceholder:Ke,expressCompany:Qe,expressRemark:Ue,orderInfoEmpty:We,closeRefund:Xe,closeRefundTips:Ye};export{Fe as afterSales,Ge as agree,He as agreeMoney,je as agreeRefundDelivery,h as all,v as applyForRefund,Ce as applyMoney,J as buyInfo,f as buyMember,oe as close,Xe as closeRefund,Ye as closeRefundTips,de as company,Re as companyName,te as companyPlaceholder,Ve as confirmDelivery,p as createTime,Ze as default,se as delivery,ge as deliveryInfo,B as deliveryMoney,ke as deliveryStatusName,K as deliveryType,ne as deliveryTypePlaceholder,H as detailOrderMoney,he as devliveryTime,pe as discountMoney,X as editAddress,d as endDate,Qe as expressCompany,ce as expressNumber,ae as expressNumberPlaceholder,Ue as expressRemark,re as finish,x as goodsDetail,l as goodsInfo,y as goodsMoney,e as goodsName,t as goodsNamePlaceholder,z as goodsPriceNumber,Pe as logisticInfo,ve as logisticNo,me as memberRemark,Je as moneyPlaceholder,W as notes,C as num,L as operateLog,ue as orderCloseTips,Te as orderDelivery,_e as orderDeliveryTips,ye as orderFinishTips,k as orderForm,_ as orderFrom,j as orderFromPlaceholder,w as orderGoods,le as orderGoodsIdsPlaceholder,fe as orderGoodsPlaceholder,b as orderInfo,We as orderInfoEmpty,i as orderMoney,o as orderNo,c as orderNoPlaceholder,xe as orderRefundAgree,r as orderRefundNo,a as orderRefundNoPlaceholder,Se as orderRefundRefuse,O as orderStatus,V as orderStatusPlaceholder,I as orderType,D as outTradeNo,Ne as packageInfo,q as payTime,U as payType,Q as piece,E as preferentialMoney,G as price,u as realityMoney,P as receivedGoods,Ke as refundAddressPlaceholder,qe as refundDeliveryAddress,N as refundEnd,m as refundMoney,Ae as refundReason,M as refundRefuse,ze as refundRemark,T as refundStatus,s as refundTime,R as refundType,we as refundVoucher,Ee as refuse,Le as refuseReason,Y as remind,Z as remindTips1,$ as remindTips2,ee as remindTips3,Oe as shopReasonPlaceholder,n as startDate,be as storeAddress,De as storeMobile,Me as storeName,S as takerFullAddress,F as takerMobile,A as takerName,g as toBeReturned,Ie as tradeTime,Be as transferAccounts,ie as virtualDelivery};
