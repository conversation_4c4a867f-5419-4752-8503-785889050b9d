const e="会员编号生成规则",o="编码前缀",t="请输入编码前缀",n="编码前缀只能为英文字母",r="编码长度包含前缀，去除前缀最少不能低于4位,最多不能超过30位",l="编码格式如：",i="编码长度",s="请输入编码长度",c="编码长度包含前缀，去除前缀最少不能低于4位,最多不能超过30位",d="万能表单",p="请选择万能表单",h="添加表单",m="个人资料",a={memberNoRule:e,prefix:o,prefixPlaceholder:t,prefixHint:n,lengthHint:r,prefixTips:l,length:i,lengthPlaceholder:s,lengthTips:c,diyForm:d,diyFormPlaceholder:p,addDiyForm:h,personalForm:m};export{h as addDiyForm,a as default,d as diyForm,p as diyFormPlaceholder,i as length,r as lengthHint,s as lengthPlaceholder,c as lengthTips,e as memberNoRule,m as personalForm,o as prefix,n as prefixHint,t as prefixPlaceholder,l as prefixTips};
