import{d as z,r as y,n as O,l as P,h as g,s as U,w as a,a as t,e as s,i as d,t as m,u as c,q as v,Z as T,c as D,F as W,W as A,E as G,L as M,M as Z,au as H,av as J,N as Q,U as X,V as Y,a3 as ee}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                *//* empty css                       *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                  */import{_ as L}from"./setting-list.vue_vue_type_script_setup_true_lang-b2ae77c6.js";import{f as te,h as le}from"./weapp-e4b43176.js";const se={class:"h-[60vh]"},oe={class:"text-center text-xl font-bold my-[20px]"},ae=t("h4",{class:"text-lg my-[10px]"},"1. 开发者处理的信息",-1),ie=t("div",{class:"mb-[8px]"},"根据法律规定，开发者仅处理实现小程序功能所必要的信息。",-1),ne={class:"setting-list"},de=t("h4",{class:"text-lg my-[10px]"},"2. 第三方插件信息/SDK信息",-1),_e={class:"mb-[8px]"},re={class:"mb-[15px]"},pe=t("h4",{class:"text-lg my-[10px]"},"3. 你的权益",-1),me=t("div",{class:"mb-[8px]"},"3.1 关于收集你的位置信息，你可以通过以下路径：小程序主页右上角“…”—“设置”—点击特定信息—点击“不允许”，撤回对开发者的授权。",-1),ce=t("div",{class:"mb-[8px]"},"3.2 关于收集你的微信昵称、头像、收集你的手机号，你可以通过以下路径：小程序主页右上角“...” — “设置” — “小程序已获取的信息” — 点击特定信息 — 点击“通知开发者删除”，开发者承诺收到通知后将删除信息。法律法规另有规定的，开发者承诺将停止除存储和采取必要的安全保护措施之外的处理。",-1),ue=t("div",{class:"mb-[8px]"},"3.3 关于你的个人信息，你可以通过以下方式与开发者联系，行使查阅、复制、更正、删除等法定权利。",-1),fe=t("div",{class:"mb-[8px]"},"3.4 若你在小程序中注册了账号，你可以通过以下方式与开发者联系，申请注销你在小程序中使用的账号。在受理你的申请后，开发者承诺在十五个工作日内完成核查和处理，并按照法律法规要求处理你的相关信息。",-1),ve=t("div",{class:"form-tip"},"信息收集方（开发者）的联系方式，4种联系方式至少要填一种",-1),xe=t("h4",{class:"text-lg my-[10px]"},"4. 开发者对信息的存储",-1),ye={class:"!w-[200px] inline-block mx-[5px]"},ge=t("h4",{class:"text-lg my-[10px]"},"5. 信息的使用规则",-1),ke=t("div",{class:"mb-[8px]"},"5.1 开发者将会在本指引所明示的用途内使用收集的信息",-1),Ve={class:"mb-[8px]"},be={class:"!w-[180px] inline-block"},he=t("h4",{class:"text-lg my-[10px]"},"6. 信息对外提供",-1),we=t("div",{class:"mb-[8px]"},"6.1 开发者承诺，不会主动共享或转让你的信息至任何第三方，如存在确需共享或转让时，开发者应当直接征得或确认第三方征得你的单独同意。",-1),Se=t("div",{class:"mb-[8px]"},"6.2 开发者承诺，不会对外公开披露你的信息，如必须公开披露时，开发者应当向你告知公开披露的目的、披露信息的类型及可能涉及的信息，并征得你的单独同意。",-1),Ue=t("h4",{class:"text-lg my-[10px]"},"7. 你认为开发者未遵守上述约定，或有其他的投诉建议、或未成年人个人信息保护相关问题，可通过以下方式与开发者联系；或者向微信进行投诉。",-1),De={class:"dialog-footer"},Pe=z({__name:"modify-privacy-agreement",props:{config:{type:Object,default:()=>{}}},emits:["complete"],setup(k,{expose:C,emit:E}){const x=y(!1),r=y(!1),V=y(null),b=y(null),h={setting_list:[{privacy_key:"UserInfo",privacy_text:""},{privacy_key:"Location",privacy_text:""},{privacy_key:"PhoneNumber",privacy_text:""}],owner_setting:{notice_method:""},sdk_privacy_info_list:[],store_expire_type:0,store_expire_timestamp:""},e=O({...h}),w=y(),q=P(()=>({})),R=()=>{e.sdk_privacy_info_list.push({sdk_name:"",sdk_biz_name:"",sdk_list:[]})},K=n=>{e.sdk_privacy_info_list.splice(n,1)},F=async n=>{r.value||!n||await n.validate(async l=>{if(l){if(r.value)return;r.value=!0;const i=e;i.store_expire_type||(i.owner_setting.store_expire_timestamp=i.store_expire_timestamp),te(i).then(_=>{r.value=!1,x.value=!1,E("complete",i)}).catch(()=>{r.value=!1})}})};return C({showDialog:x,setFormData:async()=>{le().then(({data:n})=>{r.value=!1,Object.assign(e,h),n&&(Object.keys(e).forEach(l=>{n[l]!=null&&(e[l]=n[l])}),n.owner_setting.store_expire_timestamp&&(e.store_expire_type=1,e.store_expire_timestamp=n.owner_setting.store_expire_timestamp)),x.value=!0})}}),(n,l)=>{const i=G,_=M,u=Z,S=H,$=J,B=Q,N=X,I=Y,j=ee;return g(),U(I,{modelValue:x.value,"onUpdate:modelValue":l[11]||(l[11]=o=>x.value=o),title:c(v)("privacyAgreementTitle"),width:"900px","destroy-on-close":!0},{footer:a(()=>[t("span",De,[s(i,{onClick:l[9]||(l[9]=o=>x.value=!1)},{default:a(()=>[d(m(c(v)("cancel")),1)]),_:1}),s(i,{type:"primary",loading:r.value,onClick:l[10]||(l[10]=o=>F(w.value))},{default:a(()=>[d(m(c(v)("confirm")),1)]),_:1},8,["loading"])])]),default:a(()=>[t("div",se,[s(N,null,{default:a(()=>[T((g(),U(B,{model:e,"label-width":"auto","label-position":"left",ref_key:"formRef",ref:w,rules:c(q),class:"page-form w-[700px] mx-auto"},{default:a(()=>[t("h3",oe,m(k.config.weapp_name)+" 小程序隐私保护指引",1),ae,ie,t("div",ne,[s(L,{modelValue:e.setting_list,"onUpdate:modelValue":l[0]||(l[0]=o=>e.setting_list=o),ref_key:"settingListRef",ref:V},null,8,["modelValue"])]),t("div",null,[s(i,{type:"primary",link:"",onClick:l[1]||(l[1]=o=>V.value.addSettingList())},{default:a(()=>[d(m(c(v)("addSettingType")),1)]),_:1})]),de,t("div",_e," 为实现特定功能，开发者可能会接入由第三方提供的插件/SDK。第三方插件/SDK的个人信息处理规则，请以其公示的官方说明为准。"+m(k.config.weapp_name)+"小程序接入的第三方插件信息/SDK信息如下： ",1),t("div",null,[(g(!0),D(W,null,A(e.sdk_privacy_info_list,(o,p)=>(g(),D("div",re,[s(u,{label:"SDK名称",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.sdk_privacy_info_list[p].sdk_name,"onUpdate:modelValue":f=>e.sdk_privacy_info_list[p].sdk_name=f,class:"input-width",placeholder:"请输入SDK名称"},null,8,["modelValue","onUpdate:modelValue"]),s(i,{type:"primary",class:"ml-[10px]",link:"",onClick:f=>K(p)},{default:a(()=>[d(m(c(v)("delete")),1)]),_:2},1032,["onClick"])]),_:2},1024),s(u,{label:"SDK提供方名称",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.sdk_privacy_info_list[p].sdk_biz_name,"onUpdate:modelValue":f=>e.sdk_privacy_info_list[p].sdk_biz_name=f,class:"input-width",placeholder:"请输入SDK提供方名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(L,{modelValue:e.sdk_privacy_info_list[p].sdk_list,"onUpdate:modelValue":f=>e.sdk_privacy_info_list[p].sdk_list=f,ref_for:!0,ref_key:"sdkSettingListRef",ref:b},null,8,["modelValue","onUpdate:modelValue"]),s(u,{label:"",class:"!mb-[8px]"},{default:a(()=>[s(i,{type:"primary",link:"",onClick:f=>b.value[p].addSettingList()},{default:a(()=>[d(m(c(v)("addSdkSettingList")),1)]),_:2},1032,["onClick"])]),_:2},1024)]))),256))]),t("div",null,[s(i,{type:"primary",link:"",onClick:R},{default:a(()=>[d(m(c(v)("addSdkInfo")),1)]),_:1})]),pe,me,ce,ue,fe,t("div",null,[s(u,{label:"电话",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.owner_setting.contact_phone,"onUpdate:modelValue":l[2]||(l[2]=o=>e.owner_setting.contact_phone=o),class:"input-width",placeholder:"请输入开发者的手机号"},null,8,["modelValue"])]),_:1}),s(u,{label:"邮箱",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.owner_setting.contact_email,"onUpdate:modelValue":l[3]||(l[3]=o=>e.owner_setting.contact_email=o),class:"input-width",placeholder:"请输入开发者的邮箱"},null,8,["modelValue"])]),_:1}),s(u,{label:"微信号",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.owner_setting.contact_weixin,"onUpdate:modelValue":l[4]||(l[4]=o=>e.owner_setting.contact_weixin=o),class:"input-width",placeholder:"请输入开发者的微信号"},null,8,["modelValue"])]),_:1}),s(u,{label:"qq号",class:"!mb-[8px]"},{default:a(()=>[s(_,{modelValue:e.owner_setting.contact_qq,"onUpdate:modelValue":l[5]||(l[5]=o=>e.owner_setting.contact_qq=o),class:"input-width",placeholder:"请输入开发者的qq号"},null,8,["modelValue"])]),_:1}),ve]),xe,t("div",null,[s($,{modelValue:e.store_expire_type,"onUpdate:modelValue":l[7]||(l[7]=o=>e.store_expire_type=o)},{default:a(()=>[t("div",null,[s(S,{label:1},{default:a(()=>[d(" 固定存储期限 "),t("div",ye,[s(_,{modelValue:e.store_expire_timestamp,"onUpdate:modelValue":l[6]||(l[6]=o=>e.store_expire_timestamp=o)},null,8,["modelValue"])])]),_:1})]),t("div",null,[s(S,{label:0},{default:a(()=>[d("开发者承诺，除法律法规另有规定外，开发者对你的信息的保存期限应当为实现处理目的所必要的最短时间。")]),_:1})])]),_:1},8,["modelValue"])]),ge,ke,t("div",Ve,[d(" 5.2 如开发者使用你的信息超出本指引目的或合理范围，开发者必须在变更使用目的或范围前，再次以 "),t("div",be,[s(_,{modelValue:e.owner_setting.notice_method,"onUpdate:modelValue":l[8]||(l[8]=o=>e.owner_setting.notice_method=o)},null,8,["modelValue"])]),d(" 方式告知并征得你的明示同意。")]),he,we,Se,Ue]),_:1},8,["model","rules"])),[[j,r.value]])]),_:1})])]),_:1},8,["modelValue","title"])}}});export{Pe as _};
