import{d as D,r as w,n as Y,aN as E,h as o,c as a,e as i,w as l,i as y,a as t,F as r,W as v,v as f,cB as A,b4 as I,aY as N,ap as T,bL as M,E as S,V as U,aq as L,p as P,g as R}from"./index-30109030.js";/* empty css                    *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  */import"./el-tooltip-4ed993c7.js";import{i as q}from"./index-d2519496.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";const s=C=>(P("data-v-1a139d6f"),C=C(),R(),C),W={class:"min-w-[100px] min-h-[650px] p-[15px] bg-white tab-index"},G={class:"flex w-full justify-between flex-wrap"},J=["onClick"],K={class:"flex items-center"},Q=s(()=>t("div",{class:"text-[14px]"},"预计收入",-1)),X=s(()=>t("div",{class:"ml-[5px] text-[14px]"},"(元)",-1)),Z=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),tt=s(()=>t("div",{class:"text-[25px] font-bold"},"0.00",-1)),et=["onClick"],st={class:"flex items-center"},lt=s(()=>t("div",{class:"text-[14px]"},"访客人数",-1)),dt=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),ot=s(()=>t("div",{class:"text-[25px] font-bold"},"0",-1)),at={class:"flex w-full justify-between flex-wrap"},it=["onClick"],nt={class:"flex items-center"},ct=s(()=>t("div",{class:"text-[14px]"},"预计收入",-1)),xt=s(()=>t("div",{class:"ml-[5px] text-[14px]"},"(元)",-1)),pt=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),rt=s(()=>t("div",{class:"text-[25px] font-bold"},"0.00",-1)),vt=["onClick"],ft={class:"flex items-center"},_t=s(()=>t("div",{class:"text-[14px]"},"访客人数",-1)),ut=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),mt=s(()=>t("div",{class:"text-[25px] font-bold"},"0",-1)),ht={class:"flex w-full justify-between flex-wrap"},bt=["onClick"],wt={class:"flex items-center"},Ct=s(()=>t("div",{class:"text-[14px]"},"预计收入",-1)),gt=s(()=>t("div",{class:"ml-[5px] text-[14px]"},"(元)",-1)),yt=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),Bt=s(()=>t("div",{class:"text-[25px] font-bold"},"0.00",-1)),kt=["onClick"],Ft={class:"flex items-center"},Vt=s(()=>t("div",{class:"text-[14px]"},"访客人数",-1)),jt=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),zt=s(()=>t("div",{class:"text-[25px] font-bold"},"0",-1)),Ht={class:"flex w-full justify-between flex-wrap"},$t=["onClick"],Dt={class:"flex items-center"},Yt=s(()=>t("div",{class:"text-[14px]"},"预计收入",-1)),Et=s(()=>t("div",{class:"ml-[5px] text-[14px]"},"(元)",-1)),At=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),It=s(()=>t("div",{class:"text-[25px] font-bold"},"0.00",-1)),Nt=["onClick"],Tt={class:"flex items-center"},Mt=s(()=>t("div",{class:"text-[14px]"},"访客人数",-1)),St=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),Ut=s(()=>t("div",{class:"text-[25px] font-bold"},"0",-1)),Lt=s(()=>t("div",{class:"text-[14px] color-[#333] h-[42px] leading-[42px]"},"自定义时间选择",-1)),Pt={class:"flex items-center"},Rt=s(()=>t("span",{class:"mr-[10px]"},"选择时间:",-1)),qt={class:"dialog-footer"},Ot={class:"flex w-full justify-between flex-wrap"},Wt=["onClick"],Gt={class:"flex items-center"},Jt=s(()=>t("div",{class:"text-[14px]"},"预计收入",-1)),Kt=s(()=>t("div",{class:"ml-[5px] text-[14px]"},"(元)",-1)),Qt=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),Xt=s(()=>t("div",{class:"text-[25px] font-bold"},"0.00",-1)),Zt=["onClick"],te={class:"flex items-center"},ee=s(()=>t("div",{class:"text-[14px]"},"访客人数",-1)),se=s(()=>t("div",{class:"nc-iconfont nc-icon-bangzhuV6xx wenHao text-[#666] ml-[12px]"},null,-1)),le=s(()=>t("div",{class:"text-[25px] font-bold"},"0",-1)),de=A('<div class="w-full flex justify-between mt-[20px]" data-v-1a139d6f><div class="w-[68%] h-[115px] bg-[#f2f3f5] flex justify-between" data-v-1a139d6f><div class="flex flex-col justify-center h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px]" data-v-1a139d6f>433.00</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px text-[18px]" data-v-1a139d6f>=</div><div class="flex flex-col justify-center h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px" data-v-1a139d6f>+</div><div class="flex flex-col justify-center h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px text-[18px]" data-v-1a139d6f>+</div><div class="flex flex-col justify-center h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px text-[18px]" data-v-1a139d6f>+</div><div class="flex flex-col justify-center h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px text-[18px]" data-v-1a139d6f>+</div><div class="flex flex-col justify-center h-76px mr-[20px]" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div></div><div class="w-[30%] h-[115px] bg-[#f2f3f5] flex" data-v-1a139d6f><div class="flex flex-col justify-center w-[149px] h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px]" data-v-1a139d6f>433.00</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px text-[18px]" data-v-1a139d6f>=</div><div class="flex flex-col justify-center w-[149px] h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div><div class="flex flex-col justify-center items-center w-[50px] h-76px" data-v-1a139d6f>+</div><div class="flex flex-col justify-center w-[149px] h-76px" data-v-1a139d6f><div class="text-[14px] ml-[15px] mt-[10px]" data-v-1a139d6f>收入总额</div><div class="text-[24px] ml-[15px] mb-[10px] mt-[5px]" data-v-1a139d6f>433.00</div><div class="text-[#105CFB] text-[14px] cursor-pointer ml-[15px] mt-[-5px]" data-v-1a139d6f>明细</div></div></div></div>',1),oe=D({__name:"order",setup(C){const B=w("first"),b=w(!1),k=w(""),j=(m,n)=>{m.props.name=="fifth"&&(b.value=!0),I(()=>{F()})},x=Y([{content:"盒子 1",id:1},{content:"盒子 2",id:2},{content:"盒子 3",id:3},{content:"盒子 1",id:4},{content:"盒子 2",id:5},{content:"盒子 3",id:6},{content:"盒子 3",id:8},{content:"盒子 3",id:9},{content:"盒子 3",id:10},{content:"盒子 3",id:11}]),d=w(-1),p=m=>{d.value=m.id},g=w(null),F=()=>{if(g.value!==null){const m=q(g.value),h={tooltip:{trigger:"axis"},legend:{data:["预计收入"]},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"预计收入",type:"line",data:[0,-20,-50,134,90,230,210]}]};m.setOption(h),m.resize({width:"auto"})}};return E(()=>{F()}),(m,n)=>{const c=N,h=T,z=M,V=S,H=U,$=L;return o(),a("div",W,[i($,{modelValue:B.value,"onUpdate:modelValue":n[4]||(n[4]=e=>B.value=e),class:"demo-tabs",onTabClick:j,type:"card"},{default:l(()=>[i(h,{name:"first"},{label:l(()=>[y("今日")]),default:l(()=>[t("div",G,[(o(!0),a(r,null,v(x.slice(0,3),(e,_)=>(o(),a("div",{class:f(["w-[32%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",K,[Q,X,i(c,{class:"box-item",effect:"light",content:"统计时间内，店铺收入金额减去支出的金额",placement:"top-start"},{default:l(()=>[Z]),_:1})]),tt],10,J))),256)),(o(!0),a(r,null,v(x.slice(3),(e,_)=>(o(),a("div",{class:f(["w-[13.5%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] mt-[20px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",st,[lt,i(c,{class:"box-item",effect:"light",content:"统计时间内，站点访问人数",placement:"top-start"},{default:l(()=>[dt]),_:1})]),ot],10,et))),256))])]),_:1}),i(h,{label:"昨日",name:"second"},{default:l(()=>[t("div",at,[(o(!0),a(r,null,v(x.slice(0,3),(e,_)=>(o(),a("div",{class:f(["w-[32%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",nt,[ct,xt,i(c,{class:"box-item",effect:"light",content:"统计时间内，店铺收入金额减去支出的金额",placement:"top-start"},{default:l(()=>[pt]),_:1})]),rt],10,it))),256)),(o(!0),a(r,null,v(x.slice(3),(e,_)=>(o(),a("div",{class:f(["w-[13.5%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] mt-[20px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",ft,[_t,i(c,{class:"box-item",effect:"light",content:"统计时间内，站点访问人数",placement:"top-start"},{default:l(()=>[ut]),_:1})]),mt],10,vt))),256))])]),_:1}),i(h,{label:"7日内",name:"third"},{default:l(()=>[t("div",ht,[(o(!0),a(r,null,v(x.slice(0,3),(e,_)=>(o(),a("div",{class:f(["w-[32%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",wt,[Ct,gt,i(c,{class:"box-item",effect:"light",content:"统计时间内，店铺收入金额减去支出的金额",placement:"top-start"},{default:l(()=>[yt]),_:1})]),Bt],10,bt))),256)),(o(!0),a(r,null,v(x.slice(3),(e,_)=>(o(),a("div",{class:f(["w-[13.5%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] mt-[20px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",Ft,[Vt,i(c,{class:"box-item",effect:"light",content:"统计时间内，站点访问人数",placement:"top-start"},{default:l(()=>[jt]),_:1})]),zt],10,kt))),256))])]),_:1}),i(h,{label:"30日内",name:"fourth"},{default:l(()=>[t("div",Ht,[(o(!0),a(r,null,v(x.slice(0,3),(e,_)=>(o(),a("div",{class:f(["w-[32%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",Dt,[Yt,Et,i(c,{class:"box-item",effect:"light",content:"统计时间内，店铺收入金额减去支出的金额",placement:"top-start"},{default:l(()=>[At]),_:1})]),It],10,$t))),256)),(o(!0),a(r,null,v(x.slice(3),(e,_)=>(o(),a("div",{class:f(["w-[13.5%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] mt-[20px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",Tt,[Mt,i(c,{class:"box-item",effect:"light",content:"统计时间内，站点访问人数",placement:"top-start"},{default:l(()=>[St]),_:1})]),Ut],10,Nt))),256))])]),_:1}),i(h,{label:"自定义",name:"fifth"},{default:l(()=>[i(H,{modelValue:b.value,"onUpdate:modelValue":n[3]||(n[3]=e=>b.value=e),width:"520",draggable:"true"},{header:l(()=>[Lt]),footer:l(()=>[t("div",qt,[i(V,{type:"primary",onClick:n[1]||(n[1]=e=>b.value=!1)},{default:l(()=>[y("确认")]),_:1}),i(V,{onClick:n[2]||(n[2]=e=>b.value=!1)},{default:l(()=>[y("取消")]),_:1})])]),default:l(()=>[t("div",Pt,[Rt,i(z,{modelValue:k.value,"onUpdate:modelValue":n[0]||(n[0]=e=>k.value=e),class:"w-[100px]",type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","date-format":"YYYY/MM/DD ddd","time-format":"A hh:mm:ss"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),t("div",Ot,[(o(!0),a(r,null,v(x.slice(0,3),(e,_)=>(o(),a("div",{class:f(["w-[32%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",Gt,[Jt,Kt,i(c,{class:"box-item",effect:"light",content:"统计时间内，店铺收入金额减去支出的金额",placement:"top-start"},{default:l(()=>[Qt]),_:1})]),Xt],10,Wt))),256)),(o(!0),a(r,null,v(x.slice(3),(e,_)=>(o(),a("div",{class:f(["w-[13.5%] border-[#eee] border-solid border-[1px] h-[90px] p-[15px] mt-[20px] cursor-pointer",{"border-[#105CFB]":d.value==e.id,"text-[#105CFB]":d.value==e.id}]),onClick:u=>p(e)},[t("div",te,[ee,i(c,{class:"box-item",effect:"light",content:"统计时间内，站点访问人数",placement:"top-start"},{default:l(()=>[se]),_:1})]),le],10,Zt))),256))])]),_:1})]),_:1},8,["modelValue"]),de,t("div",{ref_key:"incomeChartRef",ref:g,class:"h-[400px] mt-[40px] ml-[-90px]"},null,512)])}}});const he=O(oe,[["__scopeId","data-v-1a139d6f"]]);export{he as default};
