import{d as G,y as L,r as g,n as U,h as u,c as w,a as c,e as o,w as i,i as m,t as p,u as l,q as t,Z as B,s as k,C as R,F as q,af as A,E as M,a$ as Z,ag as H,ah as J,a2 as K,a9 as O,V as Q,U as X,a3 as Y,p as ee,g as te}from"./index-30109030.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                 *//* empty css                  */import{c as ae,w as oe,d as le,e as ie,f as ne}from"./wxoplatform-89e3ebb7.js";import{_ as se}from"./_plugin-vue_export-helper-c27b6911.js";const re=f=>(ee("data-v-ff285e0d"),f=f(),te(),f),pe={class:"main-container"},de={class:"flex justify-between items-center"},me={class:"mt-[20px]"},ce={class:"mt-[20px]"},ue={key:0},_e={key:0,class:"text-error"},ge=re(()=>c("div",null,"--",-1)),fe={class:"flex flex-col items-end"},ve={class:"mt-[16px] flex justify-end"},ye={class:"mt-[16px] flex justify-end"},be=G({__name:"weapp_version",setup(f){L().meta.title;const v=g(!1),$=g({}),y=g(!1),n=U({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{site_group_id:""}}),s=U({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{}}),V=g(""),b=g(!1),W=r=>{V.value=r.fail_reason,b.value=!0};ae().then(({data:r})=>{$.value=r});const S=(r="")=>{v.value||(v.value=!0,oe({site_group_id:r}).then(()=>{v.value=!1}).catch(()=>{v.value=!1}))},h=(r=1)=>{n.loading=!0,n.page=r,le({page:n.page,limit:n.limit,site_group_id:n.searchParam.site_group_id}).then(a=>{n.loading=!1,n.data=a.data.data,n.total=a.data.total}).catch(()=>{n.loading=!1})},C=(r=1)=>{s.loading=!0,s.page=r,ie({page:s.page,limit:s.limit}).then(a=>{s.loading=!1,s.data=a.data.data,s.total=a.data.total}).catch(()=>{s.loading=!1})};C();const j=r=>{n.searchParam.site_group_id=r,h(),y.value=!0},I=r=>{A.confirm(t("syncSiteWeappTips"),t("warning"),{confirmButtonText:t("confirm"),cancelButtonText:t("cancel"),type:"warning"}).then(()=>{ne({site_group_id:r})})};return(r,a)=>{const _=M,N=Z,d=H,T=J,z=K,P=O,D=Q,F=X,E=Y;return u(),w(q,null,[c("div",pe,[o(P,{class:"box-card !border-none p-[20px]",shadow:"never"},{default:i(()=>[c("div",de,[o(_,{type:"primary",class:"w-[100px]",onClick:a[0]||(a[0]=e=>S())},{default:i(()=>[m(p(l(t)("oneClickSync")),1)]),_:1})]),c("div",me,[o(N,{title:l(t)("updateTips"),type:"info","show-icon":""},null,8,["title"])]),c("div",ce,[B((u(),k(T,{data:s.data,size:"large"},{empty:i(()=>[c("span",null,p(s.loading?"":l(t)("emptyData")),1)]),default:i(()=>[o(d,{prop:"group_name",label:l(t)("groupName")},null,8,["label"]),o(d,{label:l(t)("userVersion"),"show-overflow-tooltip":!0},{default:i(({row:e})=>[m(p(e.commit_record.user_version||"--"),1)]),_:1},8,["label"]),o(d,{prop:"create_time",label:l(t)("createTime")},{default:i(({row:e})=>[m(p(e.commit_record.create_time||"--"),1)]),_:1},8,["label"]),o(d,{prop:"create_time",label:l(t)("status")},{default:i(({row:e})=>[e.commit_record.user_version?(u(),w("div",ue,[m(p(e.commit_record.status_name)+" ",1),e.commit_record.status==1&&e.commit_record.template_id=="0"?(u(),w("div",_e,p(l(t)("syncTemplateError")),1)):R("",!0)])):R("",!0),ge]),_:1},8,["label"]),o(d,{prop:"create_time",label:l(t)("templateID")},{default:i(({row:e})=>[m(p(e.commit_record.template_id||"--"),1)]),_:1},8,["label"]),o(d,{label:l(t)("operation"),align:"right",fixed:"right",width:"330"},{default:i(({row:e})=>[c("div",fe,[o(_,{type:"primary",link:"",onClick:x=>S(e.group_id)},{default:i(()=>[m(p(l(t)("weappVersionUpdate")),1)]),_:2},1032,["onClick"]),o(_,{type:"primary",link:"",onClick:x=>j(e.group_id)},{default:i(()=>[m(p(l(t)("weappVersionUpdateRecord")),1)]),_:2},1032,["onClick"]),o(_,{type:"primary",link:"",onClick:x=>I(e.group_id)},{default:i(()=>[m(p(l(t)("siteWeappSync")),1)]),_:2},1032,["onClick"])])]),_:1},8,["label"])]),_:1},8,["data"])),[[E,s.loading]])]),c("div",ve,[o(z,{"current-page":s.page,"onUpdate:current-page":a[1]||(a[1]=e=>s.page=e),"page-size":s.limit,"onUpdate:page-size":a[2]||(a[2]=e=>s.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:s.total,onSizeChange:a[3]||(a[3]=e=>C()),onCurrentChange:C},null,8,["current-page","page-size","total"])])]),_:1})]),o(D,{modelValue:y.value,"onUpdate:modelValue":a[7]||(a[7]=e=>y.value=e),title:l(t)("weappVersionUpdateRecord"),width:"1000px","destroy-on-close":!0},{default:i(()=>[B((u(),k(T,{data:n.data,size:"large"},{empty:i(()=>[c("span",null,p(n.loading?"":l(t)("emptyData")),1)]),default:i(()=>[o(d,{prop:"user_version",label:l(t)("userVersion")},null,8,["label"]),o(d,{label:l(t)("status"),prop:"status_name"},null,8,["label"]),o(d,{prop:"create_time",label:l(t)("createTime")},null,8,["label"]),o(d,{label:l(t)("operation"),align:"right",fixed:"right",width:"130"},{default:i(({row:e})=>[e.status==-1?(u(),k(_,{key:0,type:"primary",link:"",onClick:x=>W(e)},{default:i(()=>[m(p(l(t)("failReason")),1)]),_:2},1032,["onClick"])):R("",!0)]),_:1},8,["label"])]),_:1},8,["data"])),[[E,n.loading]]),c("div",ye,[o(z,{"current-page":n.page,"onUpdate:current-page":a[4]||(a[4]=e=>n.page=e),"page-size":n.limit,"onUpdate:page-size":a[5]||(a[5]=e=>n.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:n.total,onSizeChange:a[6]||(a[6]=e=>h()),onCurrentChange:h},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"]),o(D,{modelValue:b.value,"onUpdate:modelValue":a[8]||(a[8]=e=>b.value=e),title:l(t)("failReason"),width:"60%"},{default:i(()=>[o(F,{class:"h-[60vh] w-full whitespace-pre-wrap p-[20px]"},{default:i(()=>[m(p(V.value),1)]),_:1})]),_:1},8,["modelValue","title"])],64)}}});const Ge=se(be,[["__scopeId","data-v-ff285e0d"]]);export{Ge as default};
