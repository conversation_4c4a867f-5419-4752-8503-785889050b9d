const e="开放平台设置",o="请输入微信第三方平台AppId",p="请输入微信第三方平台AppSecret",t="请输入消息校验Token",s="请输入消息加解密Key",a="开放平台通信",n="授权发起页域名",c="授权事件接收URL",i="消息校验Token",r="消息加解密Key",l="消息与事件接收URL",d="公众号开发域名",m="小程序服务器域名",u="小程序业务域名",y="开发者设置",T="开发者邮箱",g="开发者手机号",D="开发者QQ",R="开发者微信",S="在代替公众号或小程序收发消息过程中使用。必须是长度为43位的字符串，只能是字母和数字。",b="重新生成",h="消息与事件接收",v="域名配置",w="开发小程序配置",P="开发小程序appid",U="代码上传密钥",f="",K="开发小程序APPID",A="请输入开发小程序APPID",I="如果小程序代码上传开启了ip白名单设置，在ip白名单中添加ip：",k="站点套餐",V="上次同步时间",W="同步模板库",Q="模板库同步记录",B="提交时间",C="版本号",E="失败原因",N=` 1、同步小程序时系统通过已绑定的开发小程序同步至微信第三方平台的普通模板库中。
 2、同步完成后，系统将自动为站点套餐下已授权的小程序提交代码。
 3、一键同步功能支持按所有站点套餐进行批量同步，同时也可针对单个站点套餐单独操作。
 4、使用此功能前，请确保已启动消息队列服务。`,x="查看同步记录",q="同步记录",L="一键同步",M="未能同步到模板库",_="模板ID",j="站点小程序同步",z="是否要给该套餐下已授权小程序的站点提交代码？",F="公众平台信息",G="公众平台类型",H="站点名称",J="授权时间",O="二维码",X={oplatformSetting:e,appidPlaceholder:o,appSecretPlaceholder:p,tokenPlaceholder:t,aesKeyPlaceholder:s,oplatformComm:a,empowerStartDomain:n,empowerReceiveUrl:c,messageValidationToken:i,messageDecryptKey:r,messageReceiveUrl:l,wechatDomain:d,weappDomain:m,weappBusinessDomain:u,oplatformBuilder:y,builderEmail:T,builderMobile:g,builderQQ:D,builderWx:R,messageDecryptKeyTips:S,regenerate:b,messagesReceiving:h,domainSetting:v,developerWeappUpload:w,developerAppid:P,uploadKey:U,uploadKeyTips:f,developAppid:K,developAppidPlaceholder:A,uploadIpTips:I,groupName:k,lastTime:V,weappVersionUpdate:W,weappVersionUpdateRecord:Q,createTime:B,userVersion:C,failReason:E,updateTips:N,seeUpdateRecord:x,commitRecord:q,oneClickSync:L,syncTemplateError:M,templateID:_,siteWeappSync:j,syncSiteWeappTips:z,publicInfo:F,publicType:G,siteName:H,authTime:J,qrcode:O};export{s as aesKeyPlaceholder,p as appSecretPlaceholder,o as appidPlaceholder,J as authTime,T as builderEmail,g as builderMobile,D as builderQQ,R as builderWx,q as commitRecord,B as createTime,X as default,K as developAppid,A as developAppidPlaceholder,P as developerAppid,w as developerWeappUpload,v as domainSetting,c as empowerReceiveUrl,n as empowerStartDomain,E as failReason,k as groupName,V as lastTime,r as messageDecryptKey,S as messageDecryptKeyTips,l as messageReceiveUrl,i as messageValidationToken,h as messagesReceiving,L as oneClickSync,y as oplatformBuilder,a as oplatformComm,e as oplatformSetting,F as publicInfo,G as publicType,O as qrcode,b as regenerate,x as seeUpdateRecord,H as siteName,j as siteWeappSync,z as syncSiteWeappTips,M as syncTemplateError,_ as templateID,t as tokenPlaceholder,N as updateTips,I as uploadIpTips,U as uploadKey,f as uploadKeyTips,C as userVersion,u as weappBusinessDomain,m as weappDomain,W as weappVersionUpdate,Q as weappVersionUpdateRecord,d as wechatDomain};
