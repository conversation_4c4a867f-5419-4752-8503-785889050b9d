import{d as N,y as B,r as D,n as P,h as p,c as m,e as a,w as o,a as s,t as _,u as i,q as n,Z as S,s as v,B as b,C as w,i as y,ap as j,K as q,ag as R,ah as A,a2 as U,a9 as L,aq as $,a3 as K,p as Z,g as F}from"./index-30109030.js";/* empty css                   *//* empty css                    *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                 *//* empty css                        */import{_ as z}from"./member_head-d9fd7b2c.js";import{_ as G}from"./category_default-6c62f083.js";import H from"./weapp_version-5c648ebe.js";import J from"./setting-ce03f21e.js";import{b as M}from"./wxoplatform-89e3ebb7.js";import{_ as O}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                   *//* empty css                 *//* empty css                  *//* empty css                */import"./index.vue_vue_type_style_index_0_lang-9be1835f.js";/* empty css                  *//* empty css                    */import"./el-form-item-4ed993c7.js";const k=c=>(Z("data-v-e56deee2"),c=c(),F(),c),Q={class:"main-container"},W={class:"flex justify-between items-center p-[20px]"},X={class:"text-page-title"},Y={class:"flex items-center"},ee={class:"mr-[10px] rounded-full w-[60px] h-[60px] flex items-center justify-center"},te=k(()=>s("div",{class:"image-slot"},[s("img",{class:"w-[60px] h-[60px]",src:z,fit:"contain"})],-1)),ae={key:1,class:"max-w-[60px] max-h-[60px]",src:z,alt:""},oe={class:"flex flex-col"},ie={class:"text-info text-sm"},se={key:0},le={key:1},ne=k(()=>s("div",{class:"image-slot"},[s("img",{class:"w-[60px] h-[60px]",src:G,fit:"contain"})],-1)),re={class:"mt-[16px] flex justify-end"},pe=N({__name:"oplatform",setup(c){const C=B().meta.title,f=D("xcx"),t=P({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{}}),d=(g=1)=>{t.loading=!0,t.page=g,M({page:t.page,limit:t.limit}).then(l=>{t.loading=!1,t.data=l.data.data,t.total=l.data.total}).catch(()=>{t.loading=!1})};return d(),(g,l)=>{const u=j,h=q,r=R,E=A,T=U,x=L,V=$,I=K;return p(),m("div",Q,[a(x,{class:"box-card !border-none setting-card",shadow:"never"},{default:o(()=>[s("div",W,[s("span",X,_(i(C)),1)]),a(V,{modelValue:f.value,"onUpdate:modelValue":l[3]||(l[3]=e=>f.value=e)},{default:o(()=>[a(u,{label:i(n)("小程序同步"),name:"xcx"},{default:o(()=>[a(H)]),_:1},8,["label"]),a(u,{label:i(n)("开放平台配置"),name:"setting"},{default:o(()=>[a(J)]),_:1},8,["label"]),a(u,{label:i(n)("授权记录"),name:"auth_record"},{default:o(()=>[a(x,{class:"box-card !border-none p-[20px]",shadow:"never"},{default:o(()=>[S((p(),v(E,{data:t.data,size:"large"},{empty:o(()=>[s("span",null,_(t.loading?"":i(n)("emptyData")),1)]),default:o(()=>[a(r,{prop:"user_version",label:i(n)("publicInfo"),"show-overflow-tooltip":!0,"min-width":"150px"},{default:o(({row:e})=>[s("div",Y,[s("div",ee,[e.value.authorizer_info.head_img?(p(),v(h,{key:0,class:"w-[60px] h-[60px]",src:i(b)(e.value.authorizer_info.head_img),fit:"contain"},{error:o(()=>[te]),_:2},1032,["src"])):(p(),m("img",ae))]),s("div",oe,[s("span",null,_(e.value.authorizer_info.nick_name||""),1),s("span",ie,_(e.value.authorizer_info.principal_name||""),1)])])]),_:1},8,["label"]),a(r,{prop:"user_version",label:i(n)("publicType")},{default:o(({row:e})=>[e.config_key=="wechat_authorization_info"?(p(),m("div",se,"微信公众号")):w("",!0),e.config_key=="weapp_authorization_info"?(p(),m("div",le,"微信小程序")):w("",!0)]),_:1},8,["label"]),a(r,{prop:"user_version",label:i(n)("qrcode")},{default:o(({row:e})=>[a(h,{class:"w-[60px] h-[60px]",src:i(b)(e.value.authorizer_info.qrcode_url),fit:"contain"},{error:o(()=>[ne]),_:2},1032,["src"])]),_:1},8,["label"]),a(r,{label:i(n)("APPID"),"show-overflow-tooltip":!0},{default:o(({row:e})=>[y(_(e.value.authorization_info.authorizer_appid),1)]),_:1},8,["label"]),a(r,{label:i(n)("siteName")},{default:o(({row:e})=>[y(_(e.site.site_name),1)]),_:1},8,["label"]),a(r,{prop:"create_time",label:i(n)("authTime")},null,8,["label"])]),_:1},8,["data"])),[[I,t.loading]]),s("div",re,[a(T,{"current-page":t.page,"onUpdate:current-page":l[0]||(l[0]=e=>t.page=e),"page-size":t.limit,"onUpdate:page-size":l[1]||(l[1]=e=>t.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:l[2]||(l[2]=e=>d()),onCurrentChange:d},null,8,["current-page","page-size","total"])])]),_:1})]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})])}}});const Ze=O(pe,[["__scopeId","data-v-e56deee2"]]);export{Ze as default};
