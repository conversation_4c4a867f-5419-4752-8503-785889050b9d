import{d as Y,y as I,n as V,q as l,r as L,h as u,c as k,e as a,w as n,a as b,t as f,u as r,F,W as N,s as E,i as x,Z as K,L as O,M as H,a4 as q,cp as A,a1 as G,bL as W,E as Z,N as J,a9 as Q,ag as X,ah as ee,a2 as te,a3 as ae}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                *//* empty css                  *//* empty css                       */import"./el-form-item-4ed993c7.js";import{A as le,I as oe}from"./notice-9865bf12.js";import{_ as re}from"./sms-records-info.vue_vue_type_script_setup_true_lang-cdc57b63.js";/* empty css                  *//* empty css                   */const ne={class:"main-container"},se={class:"flex justify-between items-center"},ie={class:"text-page-title"},me={class:"mt-[10px]"},pe={class:"mt-[16px] flex justify-end"},$e=Y({__name:"sms_records",setup(ce){const T=I().meta.title,t=V({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{key:"",mobile:"",create_time:[]}}),p=V({buyer:{label:l("buyerNotice"),list:[]},seller:{label:l("sellerNotice"),list:[]}});(async()=>{le({}).then(s=>{p.buyer.list=[],p.seller.list=[],s.data.forEach(e=>{if(e.notice.length){const d=[],i=[];Object.keys(e.notice).forEach((h,D)=>{const m=e.notice[h];m.addon_name=e.title,m.receiver_type==1?d.push({name:m.name,value:m.key}):i.push({name:m.name,value:m.key})}),d.length&&(d.unshift({name:e.title,value:e.key,disabled:!0}),p.buyer.list=p.buyer.list.concat(d)),i.length&&(i.unshift({name:e.title,value:e.key,disabled:!0}),p.seller.list=p.seller.list.concat(i))}})}).catch(s=>{})})();const w=L(),c=(s=1)=>{t.loading=!0,t.page=s,oe({page:t.page,limit:t.limit,...t.searchParam}).then(e=>{t.loading=!1,t.data=e.data.data,t.total=e.data.total}).catch(()=>{t.loading=!1})};c();const z=s=>{s&&(s.resetFields(),c())},g=L(null),B=s=>{g.value.setFormData(s),g.value.showDialog=!0};return(s,e)=>{const d=O,i=H,h=q,D=A,m=G,U=W,y=Z,$=J,P=Q,_=X,j=ee,M=te,R=ae;return u(),k("div",ne,[a(P,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[b("div",se,[b("span",ie,f(r(T)),1)]),a(P,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:n(()=>[a($,{inline:!0,model:t.searchParam,ref_key:"searchFormRef",ref:w},{default:n(()=>[a(i,{label:r(l)("searchReceiver"),prop:"mobile"},{default:n(()=>[a(d,{modelValue:t.searchParam.mobile,"onUpdate:modelValue":e[0]||(e[0]=o=>t.searchParam.mobile=o),modelModifiers:{trim:!0},placeholder:r(l)("receiverPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),a(i,{label:r(l)("noticeKey"),prop:"key"},{default:n(()=>[a(m,{modelValue:t.searchParam.key,"onUpdate:modelValue":e[1]||(e[1]=o=>t.searchParam.key=o),clearable:"",placeholder:r(l)("noticeKeyPlaceholder"),class:"input-width"},{default:n(()=>[(u(!0),k(F,null,N(p,(o,C)=>(u(),E(D,{key:C,label:o.label},{default:n(()=>[(u(!0),k(F,null,N(o.list,(v,S)=>(u(),E(h,{label:v.name,value:v.value,disabled:v.disabled??!1,key:S},null,8,["label","value","disabled"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),a(i,{label:r(l)("createTime"),prop:"create_time"},{default:n(()=>[a(U,{modelValue:t.searchParam.create_time,"onUpdate:modelValue":e[2]||(e[2]=o=>t.searchParam.create_time=o),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","start-placeholder":r(l)("startDate"),"end-placeholder":r(l)("endDate")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1},8,["label"]),a(i,null,{default:n(()=>[a(y,{type:"primary",onClick:e[3]||(e[3]=o=>c())},{default:n(()=>[x(f(r(l)("search")),1)]),_:1}),a(y,{onClick:e[4]||(e[4]=o=>z(w.value))},{default:n(()=>[x(f(r(l)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),b("div",me,[K((u(),E(j,{data:t.data,size:"large"},{empty:n(()=>[b("span",null,f(t.loading?"":r(l)("emptyData")),1)]),default:n(()=>[a(_,{prop:"name",label:r(l)("noticeKey"),"min-width":"120"},null,8,["label"]),a(_,{prop:"sms_type_name",label:r(l)("smsType"),"min-width":"120"},null,8,["label"]),a(_,{prop:"mobile",label:r(l)("receiver"),"min-width":"120"},null,8,["label"]),a(_,{prop:"create_time",label:r(l)("createTime"),"min-width":"140"},null,8,["label"]),a(_,{label:r(l)("operation"),align:"right",fixed:"right",width:"100"},{default:n(({row:o})=>[a(y,{type:"primary",link:"",onClick:C=>B(o)},{default:n(()=>[x(f(r(l)("info")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[R,t.loading]]),b("div",pe,[a(M,{"current-page":t.page,"onUpdate:current-page":e[5]||(e[5]=o=>t.page=o),"page-size":t.limit,"onUpdate:page-size":e[6]||(e[6]=o=>t.limit=o),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:e[7]||(e[7]=o=>c()),onCurrentChange:c},null,8,["current-page","page-size","total"])])]),a(re,{ref_key:"recordsDialog",ref:g,onComplete:c},null,512)]),_:1})])}}});export{$e as default};
