import{d as P,r as v,n as F,l as B,I as C,q as t,h as w,s as _,w as i,a as N,e as n,i as b,t as h,u as d,Z as k,L as I,M as R,N as j,E as L,V as O,a3 as S}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                 */import{e as W}from"./weapp-e4b43176.js";const $={class:"dialog-footer"},Y=P({__name:"modify-domain",emits:["complete"],setup(M,{expose:y,emit:q}){const p=v(!1),u=v(!1),g={requestdomain:"",wsrequestdomain:"",uploaddomain:"",downloaddomain:"",tcpdomain:"",udpdomain:""},a=F({...g}),V=v(),x=B(()=>({requestdomain:[{validator:(o,e,l)=>m(o,e,l,"https://"),trigger:"blur"}],uploaddomain:[{validator:(o,e,l)=>m(o,e,l,"https://"),trigger:"blur"}],downloaddomain:[{validator:(o,e,l)=>m(o,e,l,"https://"),trigger:"blur"}],wsrequestdomain:[{validator:(o,e,l)=>m(o,e,l,"wss://"),trigger:"blur"}],tcpdomain:[{validator:(o,e,l)=>m(o,e,l,"tcp://"),trigger:"blur"}],udpdomain:[{validator:(o,e,l)=>m(o,e,l,"udp://"),trigger:"blur"}]})),m=(o,e,l,s)=>{if(C.empty(e))l();else{let f=!0;e.split(";").forEach(c=>{c.startsWith(s)||(f=!1,l(new Error(t("domainError"))))}),f&&l()}},U=async o=>{u.value||!o||await o.validate(async e=>{if(e){if(u.value)return;u.value=!0;const l=a;W(l).then(s=>{u.value=!1,p.value=!1,q("complete",l)}).catch(()=>{u.value=!1})}})};return y({showDialog:p,setFormData:async(o=null)=>{u.value=!1,Object.assign(a,g),o&&Object.keys(a).forEach(e=>{o[e]!=null&&(a[e]=o[e])})}}),(o,e)=>{const l=I,s=R,f=j,c=L,D=O,E=S;return w(),_(D,{modelValue:p.value,"onUpdate:modelValue":e[8]||(e[8]=r=>p.value=r),title:d(t)("functionSetting"),width:"700px","destroy-on-close":!0},{footer:i(()=>[N("span",$,[n(c,{onClick:e[6]||(e[6]=r=>p.value=!1)},{default:i(()=>[b(h(d(t)("cancel")),1)]),_:1}),n(c,{type:"primary",loading:u.value,onClick:e[7]||(e[7]=r=>U(V.value))},{default:i(()=>[b(h(d(t)("confirm")),1)]),_:1},8,["loading"])])]),default:i(()=>[k((w(),_(f,{model:a,"label-width":"180px",ref_key:"formRef",ref:V,rules:d(x),class:"page-form pr-[100px]"},{default:i(()=>[n(s,{label:d(t)("requestUrl"),prop:"requestdomain"},{default:i(()=>[n(l,{modelValue:a.requestdomain,"onUpdate:modelValue":e[0]||(e[0]=r=>a.requestdomain=r),placeholder:d(t)("requestdomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(s,{label:d(t)("socketUrl"),prop:"wsrequestdomain"},{default:i(()=>[n(l,{modelValue:a.wsrequestdomain,"onUpdate:modelValue":e[1]||(e[1]=r=>a.wsrequestdomain=r),placeholder:d(t)("wsrequestdomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(s,{label:d(t)("uploadUrl"),prop:"uploaddomain"},{default:i(()=>[n(l,{modelValue:a.uploaddomain,"onUpdate:modelValue":e[2]||(e[2]=r=>a.uploaddomain=r),placeholder:d(t)("uploaddomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(s,{label:d(t)("downloadUrl"),prop:"downloaddomain"},{default:i(()=>[n(l,{modelValue:a.downloaddomain,"onUpdate:modelValue":e[3]||(e[3]=r=>a.downloaddomain=r),placeholder:d(t)("downloaddomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(s,{label:d(t)("udpUrl"),prop:"udpdomain"},{default:i(()=>[n(l,{modelValue:a.udpdomain,"onUpdate:modelValue":e[4]||(e[4]=r=>a.udpdomain=r),placeholder:d(t)("udpdomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),n(s,{label:d(t)("tcpUrl"),prop:"tcpdomain"},{default:i(()=>[n(l,{modelValue:a.tcpdomain,"onUpdate:modelValue":e[5]||(e[5]=r=>a.tcpdomain=r),placeholder:d(t)("tcpdomainPlaceholder"),type:"textarea"},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[E,u.value]])]),_:1},8,["modelValue","title"])}}});export{Y as _};
