import{d as A,y as $,f as R,r as x,n as U,h as u,c as w,e as t,w as s,a as _,t as d,u as e,q as a,Z as W,s as C,C as z,F as I,W as M,i as v,a_ as O,ap as P,aq as Y,a$ as Z,ag as G,aY as H,E as J,ah as K,a9 as Q,a3 as X}from"./index-30109030.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                  */import{_ as ee}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";/* empty css                        *//* empty css               *//* empty css                 *//* empty css                    */import{i as ae,j as te}from"./weapp-e4b43176.js";import{e as ne}from"./notice-9865bf12.js";const le={class:"main-container"},oe={class:"flex justify-between items-center"},se={class:"text-page-title"},ie={class:"mt-[20px]"},ce={class:"flex items-center"},pe={class:"mr-[5px]"},Ne=A({__name:"template",setup(re){const E=$(),T=R(),N=E.meta.title,b=x("/channel/weapp/message"),V=l=>{T.push({path:b.value})},i=U({loading:!0,data:[]}),f=(l=1)=>{i.loading=!0,ae().then(o=>{i.loading=!1;let c=[];o.data.forEach(m=>{if(m.notice.length){const r=[];Object.keys(m.notice).forEach((p,y)=>{const h=m.notice[p];h.addon_name=m.title,r.push(h)}),r.length&&(r[0].rowspan=r.length,c=c.concat(r))}}),i.data=c}).catch(()=>{i.loading=!1})};f();const B=l=>{if(l.columnIndex===0)return l.row.rowspan?{rowspan:l.row.rowspan,colspan:1}:{rowspan:0,colspan:0}},D=(l=null)=>{const o=O.service({lock:!0,background:"rgba(0, 0, 0, 0)"});te({keys:l?[l.key]:[]}).then(()=>{f(),o.close()}).catch(()=>{o.close()})},F=l=>{const o=x({key:"",type:"",status:0});o.value.status=l.is_weapp?0:1,o.value.key=l.key,o.value.type="weapp",i.loading=!0,ne(o.value).then(c=>{f()}).catch(()=>{i.loading=!1})};return(l,o)=>{const c=P,m=Y,r=Z,p=G,y=ee,h=H,k=J,L=K,S=Q,j=X;return u(),w("div",le,[t(S,{class:"card !border-none",shadow:"never"},{default:s(()=>[_("div",oe,[_("span",se,d(e(N)),1)]),t(m,{modelValue:b.value,"onUpdate:modelValue":o[0]||(o[0]=n=>b.value=n),class:"my-[20px]",onTabChange:V},{default:s(()=>[t(c,{label:e(a)("weappAccessFlow"),name:"/channel/weapp"},null,8,["label"]),t(c,{label:e(a)("subscribeMessage"),name:"/channel/weapp/message"},null,8,["label"]),t(c,{label:e(a)("weappRelease"),name:"/channel/weapp/code"},null,8,["label"])]),_:1},8,["modelValue"]),t(r,{title:e(a)("operationTipTwo"),type:"info","show-icon":""},null,8,["title"]),_("div",ie,[W((u(),C(L,{data:i.data,"span-method":B,size:"large"},{empty:s(()=>[_("span",null,d(i.loading?"":e(a)("emptyData")),1)]),default:s(()=>[t(p,{prop:"addon_name",label:e(a)("addon"),"min-width":"120"},null,8,["label"]),t(p,{prop:"name","show-overflow-tooltip":!0,label:e(a)("name"),"min-width":"150"},{default:s(({row:n})=>[_("div",ce,[_("span",pe,d(n.name),1),n.weapp.tips?(u(),C(h,{key:0,content:n.weapp.tips,placement:"top"},{default:s(()=>[t(y,{name:"element WarningFilled"})]),_:2},1032,["content"])):z("",!0)])]),_:1},8,["label"]),t(p,{label:e(a)("response"),"min-width":"180"},{default:s(({row:n})=>[(u(!0),w(I,null,M(n.weapp.content,(g,q)=>(u(),w("div",{key:"a"+q,class:"text-left"},d(g.join(":")),1))),128))]),_:1},8,["label"]),t(p,{label:e(a)("isStart"),"min-width":"100",align:"center"},{default:s(({row:n})=>[v(d(n.is_weapp==1?e(a)("startUsing"):e(a)("statusDeactivate")),1)]),_:1},8,["label"]),t(p,{prop:"weapp_template_id",label:e(a)("serialNumber"),"min-width":"180"},null,8,["label"]),t(p,{label:e(a)("operation"),fixed:"right",align:"right",width:"200"},{default:s(({row:n})=>[t(k,{type:"primary",link:"",onClick:g=>F(n)},{default:s(()=>[v(d(n.is_weapp==1?e(a)("close"):e(a)("open")),1)]),_:2},1032,["onClick"]),t(k,{type:"primary",link:"",onClick:g=>D(n)},{default:s(()=>[v(d(e(a)("regain")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[j,i.loading]])])]),_:1})])}}});export{Ne as default};
