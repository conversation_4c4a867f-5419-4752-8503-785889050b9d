import{P as t}from"./index-30109030.js";function u(e){return t.get("site/site",{params:e})}function n(e){return t.get(`site/site/${e}`)}function i(e){return t.post("site/site",e,{showSuccessMessage:!0})}function r(e){return t.put(`site/site/${e.site_id}`,e,{showSuccessMessage:!0})}function o(e){return t.delete(`site/site/${e}`)}function c(e){return t.put(`site/closesite/${e.site_id}`,e,{showSuccessMessage:!0})}function g(e){return t.post("site/init",e,{showSuccessMessage:!0})}function a(e){return t.put(`site/opensite/${e.site_id}`,e,{showSuccessMessage:!0})}function f(){return t.get("site/statuslist")}function p(e){return t.get("site/group",{params:e})}function S(e){return t.get(`site/group/${e}`)}function l(e){return t.post("site/group",e,{showSuccessMessage:!0})}function d(e){return t.put(`site/group/${e.group_id}`,e,{showSuccessMessage:!0})}function h(e){return t.delete(`site/group/${e}`,{showSuccessMessage:!0})}function w(e={}){return t.get("site/group/all")}function $(e={}){return t.get("site/group/user",{params:e})}function M(e){return t.get("site/user",{params:e})}function A(e){return t.get(`site/user/${e}`)}function L(e){return t.post("site/user",e,{showSuccessMessage:!0})}function U(e){return t.put(`site/user/${e.uid}`,e,{showSuccessMessage:!0})}function k(e){return t.put(`site/user/lock/${e}`)}function G(e){return t.put(`site/user/unlock/${e}`)}function y(e){return t.delete(`site/user/${e}`)}function I(e){return t.get("site/log",{params:e})}function _(e){return t.get(`site/log/${e}`)}function q(){return t.delete("site/log/destroy")}function x(e){return t.get("site/account",{params:e})}function D(){return t.get("site/account/stat")}function b(){return t.get("site/account/type")}function j(){return t.get("site/showApp")}function v(){return t.get("site/showMarketing")}export{g as A,c as B,a as C,o as D,$ as E,v as a,I as b,_ as c,M as d,k as e,y as f,j as g,U as h,L as i,A as j,x as k,q as l,b as m,D as n,w as o,n as p,r as q,i as r,p as s,h as t,G as u,S as v,d as w,l as x,f as y,u as z};
