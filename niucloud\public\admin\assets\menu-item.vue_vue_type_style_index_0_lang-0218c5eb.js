import{d as C,f as h,l as v,u as t,h as s,c as r,s as o,w as u,a as m,t as i,F as c,W as S,v as d,C as y,d3 as p,cY as B}from"./index-30109030.js";import"./el-menu-item-4ed993c7.js";import"./el-sub-menu-4ed993c7.js";const w={class:d(["ml-[10px]"])},E={key:2,class:"!border-0 !border-t-[1px] border-solid mx-[25px] bg-[#f7f7f7] my-[5px]"},F=C({__name:"menu-item",props:{routes:{type:Object,required:!0}},setup(e){const f=e,x=h(),n=v(()=>f.routes.meta);return(I,a)=>{const b=p,k=B;return t(n).show?(s(),r(c,{key:0},[e.routes.children?(s(),o(b,{key:0,index:String(e.routes.name)},{title:u(()=>[m("span",w,i(t(n).title),1)]),default:u(()=>[(s(!0),r(c,null,S(e.routes.children,(l,g)=>(s(),o(F,{routes:l,key:g},null,8,["routes"]))),128))]),_:1},8,["index"])):(s(),r(c,{key:1},[t(n).addon&&t(n).parent_route&&t(n).parent_route.addon==""?(s(),o(k,{key:0,index:String(e.routes.name),onClick:a[0]||(a[0]=l=>t(x).push({name:e.routes.name}))},{title:u(()=>[m("span",{class:d([{"text-[15px]":e.routes.meta.class==1},{"text-[14px]":e.routes.meta.class!=1},{"ml-[10px]":e.routes.meta.class==2,"ml-[15px]":e.routes.meta.class==3}])},i(t(n).title),3)]),_:1},8,["index"])):(s(),o(k,{key:1,index:String(e.routes.name),onClick:a[1]||(a[1]=l=>t(x).push({name:e.routes.name}))},{title:u(()=>[m("span",{class:d([{"text-[15px]":e.routes.meta.class==1},{"text-[14px]":e.routes.meta.class!=1},{"ml-[10px]":e.routes.meta.class==2,"ml-[15px]":e.routes.meta.class==3}])},i(t(n).title),3)]),_:1},8,["index"]))],64)),e.routes.is_border?(s(),r("div",E)):y("",!0)],64)):y("",!0)}}});export{F as _};
