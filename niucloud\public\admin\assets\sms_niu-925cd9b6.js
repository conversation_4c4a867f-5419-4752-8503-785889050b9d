import{d as ie,f as ue,y as re,r as c,n as me,aN as ce,l as pe,Z as q,h as v,c as D,e,w as l,u,aT as de,a as s,t as m,q as r,i as f,C as k,_ as j,s as $,aU as _e,a9 as ve,a$ as fe,E as be,M as he,aQ as ge,aR as we,aW as xe,ap as ye,aq as ke,N as Ve,L as Ce,V as Ee,a3 as Re}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                *//* empty css                    *//* empty css                  *//* empty css               */import"./el-form-item-4ed993c7.js";/* empty css                  *//* empty css                 *//* empty css                *//* empty css                       */import{D as $e,E as Se,F as Fe,G,d as Ie,f as De}from"./notice-9865bf12.js";import{_ as Ne}from"./sms_niu_login.vue_vue_type_script_setup_true_lang-31c62dc4.js";import{_ as Ue}from"./sms_template.vue_vue_type_script_setup_true_lang-fc02c345.js";import{_ as Le}from"./sms_recharge_record.vue_vue_type_script_setup_true_lang-c1a0ef0b.js";import Be from"./sms_recharge-0759faec.js";import{_ as qe}from"./sms_send.vue_vue_type_script_setup_true_lang-51dd610f.js";import{_ as Ae}from"./sms_signature.vue_vue_type_script_setup_true_lang-f7805acb.js";import"./index-e3ceb692.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                 *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";/* empty css                       *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                   */const Te={class:"main-container"},Me={key:0},Pe={key:0},je=s("p",{class:"mb-[5px]"},"牛云短信操作指引",-1),Ge=s("p",{class:"mb-[5px]"},"* 开启准备：若要开启牛云短信功能，需登录对应账号，并配置可用的短信签名。",-1),He=s("p",{class:"mb-[5px]"},"* 审核说明：短信签名设置与模板消息开启均需经过审核。审核时间为周一至周日的 9:30 - 22:00（法定节假日审核时间顺延）。工作日内，审核预计耗时 2 小时；非工作日，预计耗时 4 小时。",-1),Oe=s("p",{class:"mb-[5px]"},"* 签名报备要求：签名可使用公司全称或简称（简称需为公司全称的一部分，不能增减或跳字，且签名需具备唯一性）。若使用非公司简称作为签名，需提供 app 在 ICP 备案截图或商标证明 ，且签名必须与资质名称完全一致。",-1),Qe=s("p",{class:"mb-[5px]"},"* 模版报备要点：报备模板时，请确认模板中变量对应的类型。若模板某变量内容超出长度限制，系统将自动截取，以确保短信正常发出。",-1),We=s("p",{class:"mb-[5px]"},"* 短信发送条件：短信成功发送需满足两个条件，一是签名审核通过且在运营商处实名认证成功；二是模板审核通过。",-1),Ze=s("p",{class:"mb-[5px]"},"* 其他事项：短信数量不足时，请及时进行充值。如有任何疑问，可联系客服，客服电话：400 - 886 - 7993（服务时间为 9:00 - 18:00 ）。",-1),ze={class:"panel-title"},Je={class:"input-width"},Ke={class:"input-width"},Xe={class:"input-width"},Ye={class:"input-width"},ea={class:"input-width"},aa={class:"panel-title"},la=s("div",{class:"mb-[10px] text-[12px] ml-[30px] text-[#999] leading-[20px]"},"是否开启牛云短信模版",-1),ta={class:"panel-title"},oa={class:"flex justify-between items-center"},sa={class:"text-primary text-[20px] mx-[5px]"},na={class:"flex items-center"},ia=["src"],ua={class:"flex items-center"},ra={key:1},ul=ie({__name:"sms_niu",setup(ma){const H=ue(),O=re().meta.title,S=c(!0),i=me({mobiles:"",sms_count:"",username:"",company:"",signature:"",status_name:""}),F=c(!1),A=c("template"),b=c(!0),x=c(!1),T=c(!0),I=c(0),_=c("");ce(()=>{V()});const Q=()=>{x.value=!1,V()},V=()=>{b.value=!0,$e().then(o=>{T.value=o.data.is_login,S.value=o.data.is_login,_.value=o.data.username,I.value=o.data.is_enable,o.data.is_login?Se(_.value).then(a=>{Object.assign(i,a.data),b.value=!1,F.value=!0}):(b.value=!1,F.value=!0)}).catch(o=>{b.value=!1,F.value=!0})},M=c(null),W=()=>{var o;(o=M.value)==null||o.open()},Z=o=>{if(!F.value)return!1;let a=I.value==1?0:1;return new Promise((d,y)=>{Fe({is_enable:a}).then(()=>{d(!0)}).catch(()=>{y(!1)})})},z=o=>{b.value=!0,G(_.value,{signature:o.sign}).then(a=>{V()})},N=c(),t=c({new_mobile:"",captcha_key:"",captcha_code:"",captcha_img:"",code:"",key:""}),J=async()=>{t.value.new_mobile="",t.value.captcha_key="",t.value.captcha_code="",t.value.captcha_img="",t.value.code="",t.value.key="";try{await U(),C.value=!0}catch{}},U=()=>Ie().then(o=>{t.value.captcha_key=o.data.captcha_key,t.value.captcha_img=o.data.img}).catch(o=>{}),C=c(!1),K=pe(()=>({new_mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:["blur","change"]}],captcha_code:[{required:!0,message:"请输入验证码",trigger:"blur"}],code:[{required:!0,message:"请输入动态码",trigger:"blur"}]})),E=c(!1),h=c(0),X=()=>{h.value>0||E.value||N.value.validateField(["captcha_code"],o=>{if(!o)return;E.value=!0;const a={mobile:i.mobiles,captcha_key:t.value.captcha_key,captcha_code:t.value.captcha_code};De(a).then(d=>{t.value.key=d.data.key,Y(60)}).catch(d=>{U(),E.value=!1}).finally(()=>{E.value=!1})})},Y=o=>{h.value=o;const a=setInterval(()=>{h.value--,h.value<=0&&clearInterval(a)},1e3)},ee=async()=>{var o;await((o=N.value)==null?void 0:o.validate(async a=>{if(a){let d={};i.mobiles?d={mobile:i.mobiles,new_mobile:t.value.new_mobile,key:t.value.key,code:t.value.code}:d={new_mobile:t.value.new_mobile},G(_.value,d).then(y=>{C.value=!1,V()})}}))},ae=()=>{H.push("/setting/sms/setting")};return(o,a)=>{const d=_e,y=ve,le=fe,g=be,p=he,w=ge,L=we,te=xe,B=ye,oe=ke,P=Ve,R=Ce,se=Ee,ne=Re;return q((v(),D("div",Te,[b.value?(v(),D("div",ra,[e(y,{class:"box-card !border-none mb-[15px] min-h-[100vh]",shadow:"never"})])):(v(),D("div",Me,[S.value&&!x.value?(v(),D("div",Pe,[e(y,{class:"box-card !border-none mb-[15px]",shadow:"never"},{default:l(()=>[e(d,{content:u(O),icon:u(de),onBack:a[0]||(a[0]=n=>ae())},null,8,["content","icon"])]),_:1}),e(P,{"label-width":"100px",ref:"formRef",class:"page-form"},{default:l(()=>[e(y,{class:"box-card !border-none relative",shadow:"never"},{default:l(()=>[e(le,{type:"warning",closable:!1,class:"!mb-[30px]"},{default:l(()=>[je,Ge,He,Oe,Qe,We,Ze]),_:1}),s("h3",ze,m(u(r)("短信信息")),1),e(L,{class:"row-bg px-[30px] mb-[20px]"},{default:l(()=>[e(w,{span:8},{default:l(()=>[e(p,{label:u(r)("用户名")},{default:l(()=>[s("div",Je,[s("span",null,m(i.username),1),e(g,{type:"primary",link:"",onClick:a[1]||(a[1]=n=>S.value=!1),class:"ml-[10px]"},{default:l(()=>[f(m(u(r)("切换账户")),1)]),_:1})])]),_:1},8,["label"])]),_:1}),e(w,{span:8},{default:l(()=>[e(p,{label:u(r)("公司名称")},{default:l(()=>[s("div",Ke,m(i.company),1)]),_:1},8,["label"])]),_:1}),e(w,{span:8},{default:l(()=>[e(p,{label:u(r)("账户状态")},{default:l(()=>[s("div",Xe,m(i.status_name),1)]),_:1},8,["label"])]),_:1}),e(w,{span:8},{default:l(()=>[e(p,{label:u(r)("手机号")},{default:l(()=>[s("div",Ye,[s("span",null,m(i.mobiles?i.mobiles:"暂无"),1),e(g,{type:"primary",link:"",onClick:a[2]||(a[2]=n=>J()),class:"ml-[10px]"},{default:l(()=>[f(m(u(r)("更换手机号")),1)]),_:1})])]),_:1},8,["label"])]),_:1}),e(w,{span:8},{default:l(()=>[e(p,{label:u(r)("签名")},{default:l(()=>[s("div",ea,[s("span",null,m(i.signature?i.signature:"暂无"),1),e(g,{type:"primary",link:"",onClick:W,class:"ml-[10px]"},{default:l(()=>[f(m(u(r)("更换签名")),1)]),_:1})])]),_:1},8,["label"])]),_:1})]),_:1}),s("h3",aa,m(u(r)("短信权限")),1),e(L,{class:"row-bg px-[30px] mb-[20px]"},{default:l(()=>[e(w,{span:24},{default:l(()=>[e(p,{label:u(r)("是否开启")},{default:l(()=>[e(te,{modelValue:I.value,"onUpdate:modelValue":a[3]||(a[3]=n=>I.value=n),"active-value":1,"inactive-value":0,"before-change":Z},null,8,["modelValue"])]),_:1},8,["label"]),la]),_:1})]),_:1}),s("h3",ta,m(u(r)("短信条数")),1),e(L,{class:"row-bg px-[30px] mb-[20px]"},{default:l(()=>[e(w,{span:24},{default:l(()=>[e(p,{label:u(r)("短信")},{default:l(()=>[s("div",oa,[s("span",sa,m(i.sms_count),1),f("条 "),e(g,{onClick:a[4]||(a[4]=n=>x.value=!0),class:"ml-[30px]"},{default:l(()=>[f(m(u(r)("短信充值")),1)]),_:1})])]),_:1},8,["label"])]),_:1})]),_:1}),e(oe,{modelValue:A.value,"onUpdate:modelValue":a[5]||(a[5]=n=>A.value=n),class:"demo-tabs"},{default:l(()=>[e(B,{label:"短信模版",name:"template"},{default:l(()=>[e(Ue,{username:_.value,signature:i.signature},null,8,["username","signature"])]),_:1}),e(B,{label:"充值记录",name:"recharge"},{default:l(()=>[e(Le,{username:_.value},null,8,["username"])]),_:1}),e(B,{label:"发送记录",name:"send"},{default:l(()=>[e(qe,{username:_.value},null,8,["username"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},512)])):k("",!0),q(e(Ne,{info:i,isLogin:T.value,onComplete:V},null,8,["info","isLogin"]),[[j,!S.value]]),q(e(Be,{onBack:a[6]||(a[6]=n=>x.value=!1),onComplete:Q,username:_.value,isRecharge:x.value},null,8,["username","isRecharge"]),[[j,x.value]]),e(Ae,{ref_key:"signatureDialogRef",ref:M,username:_.value,onSelect:z},null,8,["username"]),e(se,{modelValue:C.value,"onUpdate:modelValue":a[14]||(a[14]=n=>C.value=n),title:u(r)("更换手机号"),width:"600px","destroy-on-close":""},{footer:l(()=>[e(g,{onClick:a[12]||(a[12]=n=>C.value=!1)},{default:l(()=>[f(m(u(r)("cancel")),1)]),_:1}),e(g,{type:"primary",onClick:a[13]||(a[13]=n=>ee())},{default:l(()=>[f(m(u(r)("confirm")),1)]),_:1})]),default:l(()=>[e(P,{"label-width":"120px",model:t.value,ref_key:"changeFormRef",ref:N,rules:u(K),class:"page-form ml-[20px]"},{default:l(()=>[i.mobiles?(v(),$(p,{key:0,label:"手机号",prop:"mobile"},{default:l(()=>[e(R,{placeholder:"请输入手机号",disabled:"",class:"input-width",maxlength:"11","show-word-limit":"",modelValue:i.mobiles,"onUpdate:modelValue":a[7]||(a[7]=n=>i.mobiles=n),clearable:""},null,8,["modelValue"])]),_:1})):k("",!0),i.mobiles?(v(),$(p,{key:1,label:"新手机号",prop:"new_mobile"},{default:l(()=>[e(R,{placeholder:"请输入新手机号",class:"input-width",maxlength:"11","show-word-limit":"",modelValue:t.value.new_mobile,"onUpdate:modelValue":a[8]||(a[8]=n=>t.value.new_mobile=n),clearable:""},null,8,["modelValue"])]),_:1})):k("",!0),i.mobiles?k("",!0):(v(),$(p,{key:2,label:"手机号",prop:"new_mobile"},{default:l(()=>[e(R,{placeholder:"请输入手机号",class:"input-width",maxlength:"11","show-word-limit":"",modelValue:t.value.new_mobile,"onUpdate:modelValue":a[9]||(a[9]=n=>t.value.new_mobile=n),clearable:""},null,8,["modelValue"])]),_:1})),i.mobiles?(v(),$(p,{key:3,label:"验证码",prop:"captcha_code"},{default:l(()=>[s("div",na,[e(R,{placeholder:"请输入验证码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:t.value.captcha_code,"onUpdate:modelValue":a[10]||(a[10]=n=>t.value.captcha_code=n),clearable:""},null,8,["modelValue"]),s("img",{src:t.value.captcha_img,alt:"验证码",class:"w-[100px] h-[32px] cursor-pointer ml-[10px]",onClick:U},null,8,ia)])]),_:1})):k("",!0),i.mobiles?(v(),$(p,{key:4,label:"动态码",prop:"code"},{default:l(()=>[s("div",ua,[e(R,{placeholder:"请输入动态码",class:"input-width",maxlength:"4","show-word-limit":"",modelValue:t.value.code,"onUpdate:modelValue":a[11]||(a[11]=n=>t.value.code=n),clearable:""},null,8,["modelValue"]),e(g,{class:"ml-[10px]",onClick:X,disabled:h.value>0,loading:E.value},{default:l(()=>[f(m(h.value>0?`${h.value}秒后重新获取`:"获取动态码"),1)]),_:1},8,["disabled","loading"])])]),_:1})):k("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])]))])),[[ne,b.value]])}}});export{ul as default};
