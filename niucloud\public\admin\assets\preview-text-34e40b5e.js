import{u as f}from"./poster-97e95d37.js";import{d as s,l,h as c,c as p,t as v,u as a,x as d}from"./index-30109030.js";const h=s({__name:"preview-text",props:{value:{type:Object,default:{}}},setup(n,{expose:i}){const e=n;f();const r=l(()=>e.value),u=l(()=>{var t="";t+=`font-size: ${e.value.fontSize}px;color: ${e.value.fontColor};line-height: ${e.value.lineHeight+e.value.fontSize}px;`,(e.value.x=="left"||e.value.x=="center"||e.value.x=="right")&&(t+=`text-align: ${e.value.x};`),e.value.weight&&(t+="font-weight: bold;"),(!e.value.fontFamily||e.value.fontFamily=="static/font/SourceHanSansCN-Regular.ttf")&&(t+="font-family: poster_default_font;");let o=document.getElementById(e.value.id);return o?t+=`width:${o.offsetWidth}px;height:${o.offsetHeight}px;`:t+=`width:${e.value.width}px;height:${e.value.height}px;`,t});return i({}),(t,o)=>(c(),p("div",{class:"overflow-hidden",style:d(a(u))},v(a(r).value),5))}}),g=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));export{g as _};
