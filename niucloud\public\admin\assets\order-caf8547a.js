import{P as r}from"./index-30109030.js";function n(){return r.get("shop/order/config")}function s(e){return r.post("shop/order/config",e,{showSuccessMessage:!0})}function u(e){return r.get("shop/order/list",{params:e})}function i(e){return r.get(`shop/order/detail/${e}`)}function d(){return r.get("shop/order/status")}function c(e){return r.put(`shop/order/close/${e}`)}function a(e){return r.get("shop/order/delivery_type",{params:e})}function p(e){return r.put("shop/order/delivery",e)}function f(e){return r.put("shop/order/shop_remark",e)}function h(e){return r.put(`shop/order/finish/${e}`)}function g(e){return r.get("shop/order/delivery/package",{params:e})}function l(e){return r.get("shop/order/delivery/package/list",{params:e})}function v(e){return r.get("shop/order/refund",{params:e})}function y(e){return r.get(`shop/order/refund/${e}`)}function _(e){return r.put(`shop/order/refund/audit/${e.order_refund_no}`,e)}function D(e){return r.put(`shop/order/refund/delivery/${e.order_refund_no}`,e)}function O(e){return r.post("shop/order/refund/active",e,{showSuccessMessage:!0})}function $(e){return r.get("shop/invoice",{params:e})}function k(e){return r.get(`shop/invoice/${e}`)}function S(e,t){return r.put(`shop/invoice/${e}`,t,{showSuccessMessage:!0})}function b(){return r.get("shop/order/pay/type")}function R(){return r.get("shop/order/from")}function m(e){return r.put("shop/order/edit_price",e,{showSuccessMessage:!0})}function w(e){return r.get("shop/order/edit_delivery",{params:e})}function B(){return r.get("shop/delivery/store/list")}function L(e){return r.put("shop/order/edit_delivery",e)}function P(e){return r.get("shop/order_batch_delivery",{params:e})}function A(e){return r.put("shop/order_batch_delivery/add_batch_order_delivery",e)}function C(){return r.get("shop/order_batch_delivery/get_status")}function E(){return r.get("shop/order_batch_delivery/get_type")}function M(e){return r.put(`shop/order/refund/close/${e}`)}export{y as A,n as B,s as C,$ as D,v as E,b as a,R as b,u as c,E as d,C as e,P as f,d as g,A as h,p as i,a as j,g as k,l,k as m,c as n,m as o,h as p,i as q,f as r,S as s,L as t,B as u,w as v,O as w,_ as x,D as y,M as z};
