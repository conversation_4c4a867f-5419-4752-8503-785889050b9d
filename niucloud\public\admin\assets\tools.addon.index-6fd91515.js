const t="开发流程",e="插件列表",s="新建插件",o="插件名称",n="请输入插件名称",c="作者",d="请输入作者",i="插件标识",r="插件类型",l="版本号",b="状态",p="删除插件后对应文件会一并删除，是否确认",a="新建一个插件",u="点击新建插件，生成插件后系统会生成对应插件的基础代码",h="新建插件",P="安装插件",v="插件创建之后处于未安装状态，点击进入插件列表选择对应新建插件点击安装",x="插件列表",y="开发插件",D="插件安装成功之后就可以进行开发，具体查看开发教程",T="查看教程",g="打包插件",k="插件开发的前端代码是直接在对应开发环境运行的，并没有放入插件对应目录，点击打包后会将对应插件的代码整合到插件目录方便后期安装与打包发行版本",m="插件列表",w="上传到云市场",A="插件打包成功之后可以上传到官方云市场进行销售，可以打包后选择下载代码zip格式，然后在官网开发商引用选择上传版本",L="官方市场",_="插件打包成功，是否下载",f={developmentProcess:t,pluginList:e,addAddon:s,title:o,titlePlaceholder:n,author:c,authorPlaceholder:d,key:i,type:r,version:l,status:b,codeDeleteTips:p,step1:a,describe1:u,btn1:h,step2:P,describe2:v,btn2:x,step3:y,describe3:D,btn3:T,step4:g,describe4:k,btn4:m,step5:w,describe5:A,btn5:L,addonDownloadText:_};export{s as addAddon,_ as addonDownloadText,c as author,d as authorPlaceholder,h as btn1,x as btn2,T as btn3,m as btn4,L as btn5,p as codeDeleteTips,f as default,u as describe1,v as describe2,D as describe3,k as describe4,A as describe5,t as developmentProcess,i as key,e as pluginList,b as status,a as step1,P as step2,y as step3,g as step4,w as step5,o as title,n as titlePlaceholder,r as type,l as version};
