import{d as E,r as _,h as f,s as b,w as a,a as d,e,i as n,t as s,u as l,q as t,Z as k,ai as B,aj as C,U as L,E as N,V as F,a3 as I}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                     *//* empty css                             */import{c as S}from"./site-0a26f9e1.js";const U={class:"break-all"},j={class:"break-all"},q={class:"dialog-footer"},O=E({__name:"user-log-detail",setup(T,{expose:v}){const r=_(!1),c=_(!1),o=_({username:"",ip:"",url:"",type:"",params:""}),h=async()=>{o.value=await(await S(m)).data,c.value=!1};let m=0;return v({showDialog:r,setFormData:async(p=null)=>{c.value=!0,p&&(m=p.id,h())}}),(p,u)=>{const i=B,D=C,y=L,V=N,w=F,x=I;return f(),b(w,{modelValue:r.value,"onUpdate:modelValue":u[1]||(u[1]=g=>r.value=g),title:l(t)("detail"),width:"500px","destroy-on-close":!0},{footer:a(()=>[d("span",q,[e(V,{onClick:u[0]||(u[0]=g=>r.value=!1)},{default:a(()=>[n(s(l(t)("cancel")),1)]),_:1})])]),default:a(()=>[k((f(),b(y,{height:"400px"},{default:a(()=>[e(D,{column:1},{default:a(()=>[e(i,{label:l(t)("username"),"label-align":"right"},{default:a(()=>[n(s(o.value.username),1)]),_:1},8,["label"]),e(i,{label:l(t)("ip"),"label-align":"right"},{default:a(()=>[n(s(o.value.ip),1)]),_:1},8,["label"]),e(i,{label:l(t)("operation"),"label-align":"right"},{default:a(()=>[n(s(o.value.operation),1)]),_:1},8,["label"]),e(i,{label:l(t)("url"),"label-align":"right"},{default:a(()=>[d("span",U,s(o.value.url),1)]),_:1},8,["label"]),e(i,{label:l(t)("type"),"label-align":"right"},{default:a(()=>[n(s(o.value.type),1)]),_:1},8,["label"]),e(i,{label:l(t)("params"),"label-align":"right"},{default:a(()=>[d("span",j,s(o.value.params),1)]),_:1},8,["label"])]),_:1})]),_:1})),[[x,c.value]])]),_:1},8,["modelValue","title"])}}});export{O as _};
