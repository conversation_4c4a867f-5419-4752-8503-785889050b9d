import{d as N,r as b,n as M,aN as S,h as _,c as w,e,w as o,a as s,i as p,F as U,W as R,s as v,Z as I,t as u,af as q,q as c,$ as O,E as W,a4 as Z,a1 as A,M as G,L as H,N as J,a9 as K,ag as Q,ao as X,ah as Y,a2 as ee,a3 as te}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  */import{_ as ae}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import{l as oe,b as le,m as ne}from"./project-8517d3f3.js";import{_ as re}from"./reward-edit.vue_vue_type_script_setup_true_lang-8ae699cb.js";/* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                        */import"./index-e3ceb692.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";const ie={class:"main-container"},se={class:"flex justify-between items-center"},pe=s("span",{class:"text-page-title"},"奖励管理",-1),me={class:"mt-[10px]"},de={class:"mt-[16px] flex justify-end"},Ye=N({__name:"reward",setup(ce){const m=b(null),t=M({page:1,limit:10,total:0,loading:!1,data:[],searchParam:{project_id:"",name:""}}),g=b([]),i=(r=1)=>{t.loading=!0,t.page=r,oe({page:t.page,limit:t.limit,...t.searchParam}).then(l=>{t.loading=!1,t.data=l.data.data,t.total=l.data.total}).catch(()=>{t.loading=!1})},E=()=>{le({}).then(r=>{g.value=r.data})},y=()=>{t.searchParam={project_id:"",name:""},i()},x=()=>{m.value.setFormData(),m.value.showEdit=!0},C=r=>{m.value.setFormData(r),m.value.showEdit=!0},k=r=>{q.confirm(c("rewardDeleteTip"),c("warning"),{confirmButtonText:c("confirm"),cancelButtonText:c("cancel"),type:"warning"}).then(()=>{ne(r).then(()=>{i(),O.success(c("deleteSuccess"))})})};return S(()=>{i(),E()}),(r,l)=>{const j=ae,d=W,P=Z,V=A,f=G,B=H,L=J,h=K,n=Q,T=X,D=Y,F=ee,$=te;return _(),w("div",ie,[e(h,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[s("div",se,[pe,e(d,{type:"primary",onClick:x},{icon:o(()=>[e(j,{name:"el-icon-Plus"})]),default:o(()=>[p(" 添加奖励 ")]),_:1})]),e(h,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:o(()=>[e(L,{model:t.searchParam,"label-width":"90px",inline:!0},{default:o(()=>[e(f,{label:"项目名称"},{default:o(()=>[e(V,{modelValue:t.searchParam.project_id,"onUpdate:modelValue":l[0]||(l[0]=a=>t.searchParam.project_id=a),placeholder:"请选择项目",clearable:"",filterable:""},{default:o(()=>[(_(!0),w(U,null,R(g.value,a=>(_(),v(P,{key:a.project_id,label:a.name,value:a.project_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"奖励名称"},{default:o(()=>[e(B,{modelValue:t.searchParam.name,"onUpdate:modelValue":l[1]||(l[1]=a=>t.searchParam.name=a),placeholder:"请输入奖励名称"},null,8,["modelValue"])]),_:1}),e(f,null,{default:o(()=>[e(d,{type:"primary",onClick:l[2]||(l[2]=a=>i())},{default:o(()=>[p("搜索")]),_:1}),e(d,{onClick:y},{default:o(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),s("div",me,[I((_(),v(D,{data:t.data,size:"large"},{empty:o(()=>[s("span",null,u(t.loading?"":"暂无数据"),1)]),default:o(()=>[e(n,{prop:"name",label:"奖励名称","min-width":"200"}),e(n,{prop:"project_name",label:"项目名称","min-width":"200"}),e(n,{prop:"amount",label:"支持金额","min-width":"120"},{default:o(({row:a})=>[s("span",null,"¥"+u(a.amount),1)]),_:1}),e(n,{prop:"limit_count",label:"限制数量","min-width":"100"},{default:o(({row:a})=>[s("span",null,u(a.limit_count||"不限制"),1)]),_:1}),e(n,{prop:"support_count",label:"支持数量","min-width":"100"}),e(n,{prop:"delivery_time",label:"发货时间","min-width":"180"}),e(n,{prop:"status_name",label:"状态","min-width":"100"},{default:o(({row:a})=>[e(T,{type:a.status==1?"success":"danger"},{default:o(()=>[p(u(a.status_name),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"create_time",label:"创建时间","min-width":"180"}),e(n,{label:"操作",fixed:"right",align:"right","min-width":"120"},{default:o(({row:a})=>[e(d,{type:"primary",link:"",onClick:z=>C(a)},{default:o(()=>[p("编辑")]),_:2},1032,["onClick"]),e(d,{type:"primary",link:"",onClick:z=>k(a.reward_id)},{default:o(()=>[p("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[$,t.loading]]),s("div",de,[e(F,{"current-page":t.page,"onUpdate:current-page":l[3]||(l[3]=a=>t.page=a),"page-size":t.limit,"onUpdate:page-size":l[4]||(l[4]=a=>t.limit=a),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:l[5]||(l[5]=a=>i()),onCurrentChange:i},null,8,["current-page","page-size","total"])])])]),_:1}),e(re,{ref_key:"rewardEditDialog",ref:m,onComplete:i},null,512)])}}});export{Ye as default};
