import{d as W,n as X,r as _,aN as ee,h as b,c as te,e,w as o,a as f,u as ae,cR as le,i as n,Z as oe,s as V,t as A,C as $,af as w,$ as d,a0 as ie,E as ne,cS as se,L as re,M as de,a4 as ue,a1 as ce,bL as pe,N as me,a9 as _e,ag as fe,ao as ge,ah as he,a2 as be,a3 as we,p as ve,g as ye}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                *//* empty css                       *//* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                 */import{c as ke,d as Ce,q as De,e as Ve,f as xe,h as Pe}from"./update-1cbb2a0f.js";import Ee from"./update-detail-dialog-586bb8b7.js";import Ue from"./batch-audit-dialog-f0358562.js";import{_ as Ie}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                             *//* empty css                 */const Be=g=>(ve("data-v-431908b3"),g=g(),ye(),g),Se={class:"main-container"},Ae={class:"flex justify-between items-center"},$e=Be(()=>f("span",{class:"text-page-title"},"项目动态审核",-1)),je={class:"flex items-center space-x-2"},Me={class:"mt-[10px] mb-[10px]"},ze={class:"mt-[10px]"},Te={class:"flex justify-end mt-[16px]"},Ne=W({__name:"update-audit",setup(g){const a=X({page:1,limit:20,total:0,loading:!1,data:[],searchParam:{project_id:"",member_id:"",audit_status:"",is_public:"",keyword:"",create_time:[]},selectData:[]}),v=_(!1),h=_(!1),x=_(0),P=_(0),y=_([]),k=_(0),u=async()=>{a.loading=!0;try{const i=await ke({page:a.page,limit:a.limit,...a.searchParam});a.data=i.data,a.total=i.total}catch(i){console.error("获取动态列表失败:",i)}finally{a.loading=!1}},E=async()=>{try{const i=await Ce();k.value=i.count||0}catch(i){console.error("获取待审核数量失败:",i)}},j=()=>{a.searchParam={project_id:"",member_id:"",audit_status:"",is_public:"",keyword:"",create_time:[]},u()},m=()=>{u(),E()},M=i=>({0:"warning",1:"success",2:"danger"})[i]||"info",z=i=>({0:"待审核",1:"审核通过",2:"审核拒绝"})[i]||"未知",T=i=>{a.selectData=i,y.value=i.map(t=>t.update_id)},N=i=>{x.value=i.update_id,v.value=!0},q=async i=>{try{await w.confirm("确定要通过这条动态吗？","确认操作",{type:"warning"}),await De(i.update_id),d.success("审核通过成功"),m()}catch(t){t!=="cancel"&&d.error(t.msg||"操作失败")}},L=async i=>{try{await w.confirm("确定要拒绝这条动态吗？","确认操作",{type:"warning"}),await Ve(i.update_id),d.success("审核拒绝成功"),m()}catch(t){t!=="cancel"&&d.error(t.msg||"操作失败")}},R=async i=>{try{await w.confirm("确定要删除这条动态吗？删除后无法恢复！","确认删除",{type:"warning"}),await xe(i.update_id),d.success("删除成功"),m()}catch(t){t!=="cancel"&&d.error(t.msg||"删除失败")}},U=i=>{if(!a.selectData.length){d.warning("请先选择要审核的动态");return}P.value=i,h.value=!0},Y=()=>{h.value=!1,m()},F=async()=>{if(!a.selectData.length){d.warning("请先选择要删除的动态");return}try{await w.confirm(`确定要删除选中的 ${a.selectData.length} 条动态吗？删除后无法恢复！`,"确认删除",{type:"warning"}),await Pe(y.value),d.success("批量删除成功"),m()}catch(i){i!=="cancel"&&d.error(i.msg||"批量删除失败")}};return ee(()=>{u(),E()}),(i,t)=>{const H=ie,s=ne,O=se,C=re,c=de,p=ue,I=ce,Z=pe,G=me,B=_e,r=fe,S=ge,J=he,K=be,Q=we;return b(),te("div",Se,[e(B,{class:"box-card !border-none",shadow:"never"},{default:o(()=>[f("div",Ae,[$e,f("div",je,[e(O,{value:k.value,hidden:k.value===0,type:"warning"},{default:o(()=>[e(s,{type:"primary",onClick:m},{icon:o(()=>[e(H,null,{default:o(()=>[e(ae(le))]),_:1})]),default:o(()=>[n(" 刷新 ")]),_:1})]),_:1},8,["value","hidden"])])]),e(B,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:o(()=>[e(G,{model:a.searchParam,"label-width":"90px",inline:!0},{default:o(()=>[e(c,{label:"项目ID"},{default:o(()=>[e(C,{modelValue:a.searchParam.project_id,"onUpdate:modelValue":t[0]||(t[0]=l=>a.searchParam.project_id=l),placeholder:"请输入项目ID",clearable:"",class:"input-width"},null,8,["modelValue"])]),_:1}),e(c,{label:"发布者ID"},{default:o(()=>[e(C,{modelValue:a.searchParam.member_id,"onUpdate:modelValue":t[1]||(t[1]=l=>a.searchParam.member_id=l),placeholder:"请输入发布者ID",clearable:"",class:"input-width"},null,8,["modelValue"])]),_:1}),e(c,{label:"审核状态"},{default:o(()=>[e(I,{modelValue:a.searchParam.audit_status,"onUpdate:modelValue":t[2]||(t[2]=l=>a.searchParam.audit_status=l),placeholder:"请选择审核状态",clearable:"",class:"input-width"},{default:o(()=>[e(p,{label:"全部",value:""}),e(p,{label:"待审核",value:"0"}),e(p,{label:"审核通过",value:"1"}),e(p,{label:"审核拒绝",value:"2"})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"公开状态"},{default:o(()=>[e(I,{modelValue:a.searchParam.is_public,"onUpdate:modelValue":t[3]||(t[3]=l=>a.searchParam.is_public=l),placeholder:"请选择公开状态",clearable:"",class:"input-width"},{default:o(()=>[e(p,{label:"全部",value:""}),e(p,{label:"公开",value:"1"}),e(p,{label:"仅支持者可见",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"关键词"},{default:o(()=>[e(C,{modelValue:a.searchParam.keyword,"onUpdate:modelValue":t[4]||(t[4]=l=>a.searchParam.keyword=l),placeholder:"请输入动态标题关键词",clearable:"",class:"input-width"},null,8,["modelValue"])]),_:1}),e(c,{label:"创建时间"},{default:o(()=>[e(Z,{modelValue:a.searchParam.create_time,"onUpdate:modelValue":t[5]||(t[5]=l=>a.searchParam.create_time=l),type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",class:"input-width"},null,8,["modelValue"])]),_:1}),e(c,null,{default:o(()=>[e(s,{type:"primary",onClick:t[6]||(t[6]=l=>u())},{default:o(()=>[n("搜索")]),_:1}),e(s,{onClick:j},{default:o(()=>[n("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),f("div",Me,[e(s,{type:"success",disabled:!a.selectData.length,onClick:t[7]||(t[7]=l=>U(1))},{default:o(()=>[n(" 批量通过 ")]),_:1},8,["disabled"]),e(s,{type:"danger",disabled:!a.selectData.length,onClick:t[8]||(t[8]=l=>U(2))},{default:o(()=>[n(" 批量拒绝 ")]),_:1},8,["disabled"]),e(s,{type:"warning",disabled:!a.selectData.length,onClick:F},{default:o(()=>[n(" 批量删除 ")]),_:1},8,["disabled"])]),f("div",ze,[oe((b(),V(J,{data:a.data,size:"large",onSelectionChange:T},{default:o(()=>[e(r,{type:"selection",width:"55"}),e(r,{prop:"update_id",label:"ID","min-width":"80"}),e(r,{prop:"title",label:"动态标题","min-width":"200","show-overflow-tooltip":""}),e(r,{prop:"project_name",label:"所属项目","min-width":"150","show-overflow-tooltip":""}),e(r,{prop:"member_nickname",label:"发布者","min-width":"120"}),e(r,{prop:"audit_status",label:"审核状态","min-width":"100"},{default:o(l=>[e(S,{type:M(l.row.audit_status),size:"small"},{default:o(()=>[n(A(z(l.row.audit_status)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"is_public",label:"公开状态","min-width":"100"},{default:o(l=>[e(S,{type:l.row.is_public?"success":"info",size:"small"},{default:o(()=>[n(A(l.row.is_public?"公开":"仅支持者"),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"create_time_text",label:"创建时间","min-width":"160"}),e(r,{prop:"audit_time_text",label:"审核时间","min-width":"160"}),e(r,{label:"操作",fixed:"right","min-width":"200"},{default:o(l=>[e(s,{type:"primary",link:"",onClick:D=>N(l.row)},{default:o(()=>[n(" 查看详情 ")]),_:2},1032,["onClick"]),l.row.audit_status===0?(b(),V(s,{key:0,type:"success",link:"",onClick:D=>q(l.row)},{default:o(()=>[n(" 快速通过 ")]),_:2},1032,["onClick"])):$("",!0),l.row.audit_status===0?(b(),V(s,{key:1,type:"warning",link:"",onClick:D=>L(l.row)},{default:o(()=>[n(" 快速拒绝 ")]),_:2},1032,["onClick"])):$("",!0),e(s,{type:"danger",link:"",onClick:D=>R(l.row)},{default:o(()=>[n(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Q,a.loading]]),f("div",Te,[e(K,{"current-page":a.page,"onUpdate:current-page":t[9]||(t[9]=l=>a.page=l),"page-size":a.limit,"onUpdate:page-size":t[10]||(t[10]=l=>a.limit=l),layout:"total, sizes, prev, pager, next, jumper",total:a.total,onSizeChange:u,onCurrentChange:u},null,8,["current-page","page-size","total"])])])]),_:1}),e(Ee,{modelValue:v.value,"onUpdate:modelValue":t[11]||(t[11]=l=>v.value=l),"update-id":x.value,onRefresh:u},null,8,["modelValue","update-id"]),e(Ue,{modelValue:h.value,"onUpdate:modelValue":t[12]||(t[12]=l=>h.value=l),"audit-status":P.value,"selected-ids":y.value,onConfirm:Y},null,8,["modelValue","audit-status","selected-ids"])])}}});const ht=Ie(Ne,[["__scopeId","data-v-431908b3"]]);export{ht as default};
