const e="是否开票",o="请输入开票类型",n="开始时间",c="结束时间",a="抬头类型",t="抬头类型",s="个人",r="企业",i="请输入抬头类型",d="发票抬头",m="请输入发票抬头",l="发票内容",h="发票类型",v="发票名称",p="抬头类型",N="纳税人识别号",T="手机号",u="邮件",b="电话号",y="地址",P="开户银行",I="开户行账号",k="开票金额",D="发票号码",V="发票凭证",f="备注",x="创建时间",w="开票时间",C="状态",O="操作",_="详情",g="已开票",j="未开票",q="全部",z="开票",A="查看订单",B="请输入发票号码",E="请输入发票凭证",F={isInvoice:e,isInvoicePlaceholder:o,startDate:n,endDate:c,headType:a,headerTypeName:t,person:s,firm:r,headTypePlaceholder:i,headerName:d,headerNamePlaceholder:m,name:l,tradeType:h,typeName:v,headTypeName:p,taxNumber:N,mobile:T,email:u,telephone:b,address:y,bankTame:P,bankCardNumber:I,money:k,invoiceNumber:D,invoiceVoucher:V,remark:f,createTime:x,invoiceTime:w,status:C,operation:O,detail:_,hasInvoice:g,noInvoice:j,all:q,invoice:z,viewOrder:A,invoiceNumberPlaceholder:B,invoiceVoucherPlaceholder:E};export{y as address,q as all,I as bankCardNumber,P as bankTame,x as createTime,F as default,_ as detail,u as email,c as endDate,r as firm,g as hasInvoice,a as headType,p as headTypeName,i as headTypePlaceholder,d as headerName,m as headerNamePlaceholder,t as headerTypeName,z as invoice,D as invoiceNumber,B as invoiceNumberPlaceholder,w as invoiceTime,V as invoiceVoucher,E as invoiceVoucherPlaceholder,e as isInvoice,o as isInvoicePlaceholder,T as mobile,k as money,l as name,j as noInvoice,O as operation,s as person,f as remark,n as startDate,C as status,N as taxNumber,b as telephone,h as tradeType,v as typeName,A as viewOrder};
