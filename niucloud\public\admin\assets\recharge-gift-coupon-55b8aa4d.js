import{d as P,r as k,n as q,l as G,R as C,h as _,s as E,w as l,e as n,u as a,q as i,a as f,t as m,c as y,i as N,C as M,aB as U,M as D,ag as F,L as z,E as L,ah as Z,N as A}from"./index-30109030.js";/* empty css                *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css               */import"./el-form-item-4ed993c7.js";import H from"./coupon-select-popup-b64aeeb9.js";import{_ as J}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                  *//* empty css                  */import"./marketing-9899dda3.js";const K={class:"mt-[-10px] mb-[10px] text-[#999] ml-[120px] text-[12px]"},Q={class:"coupon_list"},W={key:0},X={key:1},Y={key:0},ee={key:1},te=P({__name:"recharge-gift-coupon",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(w,{expose:I,emit:B}){const T=w,t=k({coupon_id:[],value:[]}),x=k(null),O={required:/[\S]+/,number:/^\d{0,10}$/,digit:/^\d{0,10}(.?\d{0,2})$/,special:/^\d{0,10}(.?\d{0,3})$/},S=q({}),$=(o,u)=>{let p=[];for(let r in o){let d=o[r],s={price:d.price,title:d.title,type_name:d.type_name,coupon_id:d.id,min_condition_money:d.min_condition_money,valid_type:d.valid_type,valid_end_time:d.valid_end_time,length:d.length,num:1};t.value.value.length&&t.value.value.forEach(v=>{v.coupon_id==s.coupon_id&&(s=Object.assign(s,v))}),p.push(U(s))}t.value.value=p},R=(o,u)=>{const p=t.value.value.findIndex(d=>d.coupon_id===o.coupon_id);p!==-1&&t.value.value.splice(p,1);const r=t.value.coupon_id.indexOf(o.coupon_id);p!==-1&&t.value.coupon_id.splice(r,1)},g=G({get(){return T.modelValue},set(o){B("update:modelValue",o)}});return C(()=>g.value,(o,u)=>{(!u||!Object.keys(u).length)&&Object.keys(o).length&&(t.value=g.value)},{immediate:!0}),C(()=>t.value,()=>{g.value=t.value},{deep:!0}),I({verify:async()=>{var u;let o=!0;return await((u=x.value)==null?void 0:u.validate(p=>{o=p})),o}}),(o,u)=>{const p=D,r=F,d=z,s=L,v=Z,j=A;return _(),E(j,{ref_key:"formRef",ref:x,"label-width":"120px",model:t.value,rules:S},{default:l(()=>[n(p,{label:a(i)("coupon"),prop:"coupon"},{default:l(()=>[n(H,{ref:"couponSelectPopupRef",onCouponSelect:u[0]||(u[0]=e=>$(e,o.index)),modelValue:t.value.coupon_id,"onUpdate:modelValue":u[1]||(u[1]=e=>t.value.coupon_id=e),min:1,max:99},null,8,["modelValue"])]),_:1},8,["label"]),f("div",K,m(a(i)("giveCouponTips")),1),t.value.value&&t.value.value.length?(_(),E(p,{key:0,class:"mt-[15px]"},{default:l(()=>[f("div",Q,[n(v,{data:t.value.value,size:"large","max-height":"400","row-style":{height:"80px"}},{default:l(()=>[n(r,{prop:"title",label:a(i)("name"),"min-width":"120"},{default:l(({row:e})=>[f("div",null,m(e.title),1)]),_:1},8,["label"]),n(r,{prop:"type_name",label:a(i)("type"),"min-width":"120"},{default:l(({row:e})=>[f("div",null,m(e.type_name),1)]),_:1},8,["label"]),n(r,{prop:"price",label:a(i)("couponPrice"),"min-width":"120"},{default:l(({row:e})=>[f("div",null,"￥"+m(e.price),1)]),_:1},8,["label"]),n(r,{label:a(i)("useThreshold"),"min-width":"130"},{default:l(({row:e})=>[e.min_condition_money=="0.00"?(_(),y("span",W,"无门槛")):(_(),y("span",X,"满"+m(e.min_condition_money)+"元可用",1))]),_:1},8,["label"]),n(r,{label:a(i)("termOfValidity"),"min-width":"210"},{default:l(({row:e})=>[e.valid_type==1?(_(),y("span",Y," 领取之日起"+m(e.length||"")+" 天内有效",1)):(_(),y("span",ee," 使用截止时间至"+m(e.valid_end_time||""),1))]),_:1},8,["label"]),n(r,{prop:"num",label:a(i)("giveNum"),"min-width":"180"},{default:l(({row:e,$index:V})=>[n(p,{prop:`value.${V}.num`,rules:[{required:!0,trigger:"blur",validator:(b,c,h)=>{c===null||c===""?h(a(i)("giveNumPlaceholder")):isNaN(c)||!O.number.test(c)?h(a(i)("limitTips")):c<=0?h(a(i)("giveNumMustBeGreaterThanZero")):h()}}]},{default:l(()=>[n(d,{modelValue:e.num,"onUpdate:modelValue":b=>e.num=b,modelModifiers:{number:!0},class:"w-[70px]",maxlength:"6",clearable:""},{append:l(()=>[N("张")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1},8,["label"]),n(r,{label:a(i)("operation"),align:"right","min-width":"160"},{default:l(({row:e,$index:V})=>[n(s,{type:"primary",link:"",onClick:b=>R(e,o.index)},{default:l(()=>[N(m(a(i)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])])]),_:1})):M("",!0)]),_:1},8,["model","rules"])}}});const Ce=J(te,[["__scopeId","data-v-75df4048"]]);export{Ce as default};
