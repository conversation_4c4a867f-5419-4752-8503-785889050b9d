import{P as o}from"./index-30109030.js";function s(){return o.get("niucloud/authinfo",{showErrorMessage:!1})}function t(e){return o.post("niucloud/authinfo",e,{showSuccessMessage:!0})}function r(e){return o.post(`addon/download/${e.addon}`,e,{showSuccessMessage:!0})}function u(){return o.get("niucloud/framework/newversion")}function i(){return o.get("niucloud/framework/version/list")}function a(e){return o.get("niucloud/app_version/list",{params:e})}export{i as a,u as b,a as c,r as d,s as g,t as s};
