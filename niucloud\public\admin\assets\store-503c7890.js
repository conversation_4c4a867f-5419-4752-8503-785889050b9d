import{d as z,y as F,n as V,r as I,bI as L,f as R,h as b,c as U,e as t,w as l,u as n,aT as D,a as m,q as a,i as c,t as i,Z as M,s as j,B as q,bJ as A,af as H,aU as J,a9 as K,L as Z,M as G,E as O,N as Q,K as W,ag as X,ah as Y,a2 as ee,a3 as te}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                 *//* empty css                        *//* empty css                *//* empty css                  */import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{r as ae,t as oe}from"./delivery-ef62b210.js";const re=""+new URL("store_default-447f2c13.png",import.meta.url).href,le={class:"main-container"},ne={class:"flex justify-between items-center"},se={class:"mt-[10px]"},ie={class:"h-[50px] flex items-center"},me=m("div",{class:"image-slot"},[m("img",{class:"w-[50px] h-[50px]",src:re})],-1),de={class:"ml-[10px] text-[14px]"},pe={class:"mt-[16px] flex justify-end"},Ie=z({__name:"store",setup(ce){const v=F().meta.title,e=V({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{store_name:"",create_time:""}}),g=I(),d=(s=1)=>{e.loading=!0,e.page=s,ae({page:e.page,limit:e.limit,...e.searchParam}).then(o=>{e.loading=!1,e.data=o.data.data,e.total=o.data.total,A(e.page,e.limit,e.searchParam)}).catch(()=>{e.loading=!1})};d(L(e.searchParam).page);const f=R(),x=()=>{f.push("/shop/order/delivery/store/edit")},y=s=>{f.push("/shop/order/delivery/store/edit?id="+s.store_id)},w=s=>{H.confirm(a("storeDeleteTips"),a("warning"),{confirmButtonText:a("confirm"),cancelButtonText:a("cancel"),type:"warning"}).then(()=>{oe(s).then(()=>{d()}).catch(()=>{})})},k=s=>{s&&(s.resetFields(),d())};return(s,o)=>{const E=J,u=K,C=Z,h=G,_=O,P=Q,T=W,p=X,B=Y,S=ee,N=te;return b(),U("div",le,[t(u,{class:"box-card !border-none",shadow:"never"},{default:l(()=>[t(E,{content:n(v),icon:n(D),onBack:o[0]||(o[0]=r=>s.$router.back())},null,8,["content","icon"])]),_:1}),t(u,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:l(()=>[t(u,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:l(()=>[m("div",ne,[t(P,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:g},{default:l(()=>[t(h,{label:n(a)("storeName"),prop:"store_name"},{default:l(()=>[t(C,{modelValue:e.searchParam.store_name,"onUpdate:modelValue":o[1]||(o[1]=r=>e.searchParam.store_name=r),modelModifiers:{trim:!0},placeholder:n(a)("storeNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(h,null,{default:l(()=>[t(_,{type:"primary",onClick:o[2]||(o[2]=r=>d())},{default:l(()=>[c(i(n(a)("search")),1)]),_:1}),t(_,{onClick:o[3]||(o[3]=r=>k(g.value))},{default:l(()=>[c(i(n(a)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),t(_,{type:"primary",onClick:x},{default:l(()=>[c(i(n(a)("addStore")),1)]),_:1})])]),_:1}),m("div",se,[M((b(),j(B,{data:e.data,size:"large"},{empty:l(()=>[m("span",null,i(e.loading?"":n(a)("emptyData")),1)]),default:l(()=>[t(p,{label:n(a)("storeInfo"),"min-width":"170",align:"left"},{default:l(({row:r})=>[m("div",ie,[t(T,{class:"w-[50px] h-[50px]",src:n(q)(r.store_logo),fit:"contain"},{error:l(()=>[me]),_:2},1032,["src"]),m("p",de,i(r.store_name),1)])]),_:1},8,["label"]),t(p,{prop:"store_mobile",label:n(a)("storeMobile"),"min-width":"120"},null,8,["label"]),t(p,{prop:"full_address",label:n(a)("fullAddress"),"min-width":"180"},null,8,["label"]),t(p,{prop:"trade_time",label:n(a)("tradeTime"),"min-width":"120"},null,8,["label"]),t(p,{label:n(a)("createTime"),"min-width":"120"},{default:l(({row:r})=>[c(i(r.create_time||""),1)]),_:1},8,["label"]),t(p,{label:n(a)("operation"),fixed:"right",align:"right","min-width":"120"},{default:l(({row:r})=>[t(_,{type:"primary",link:"",onClick:$=>y(r)},{default:l(()=>[c(i(n(a)("edit")),1)]),_:2},1032,["onClick"]),t(_,{type:"primary",link:"",onClick:$=>w(r.store_id)},{default:l(()=>[c(i(n(a)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[N,e.loading]]),m("div",pe,[t(S,{"current-page":e.page,"onUpdate:current-page":o[4]||(o[4]=r=>e.page=r),"page-size":e.limit,"onUpdate:page-size":o[5]||(o[5]=r=>e.limit=r),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:o[6]||(o[6]=r=>d()),onCurrentChange:d},null,8,["current-page","page-size","total"])])])]),_:1})])}}});export{Ie as default};
