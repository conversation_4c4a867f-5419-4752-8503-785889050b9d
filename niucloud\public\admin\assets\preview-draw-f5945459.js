import{d as u,l,h as c,c as i,x as s,u as d}from"./index-30109030.js";const p=u({__name:"preview-draw",props:{value:{type:Object,default:{}}},setup(r,{expose:n}){const e=r;l(()=>e.value);const a=l(()=>{var t="";t+=`background-color: ${e.value.bgColor};`,t+=`width: ${e.value.width}px;height: ${e.value.height}px;`;let o=document.getElementById(e.value.id);return o&&(t+=`width:${o.offsetWidth}px;height:${o.offsetHeight}px;`),t});return n({}),(t,o)=>(c(),i("div",{class:"overflow-hidden",style:s(d(a))},null,4))}}),f=Object.freeze(Object.defineProperty({__proto__:null,default:p},Symbol.toStringTag,{value:"Module"}));export{f as _};
