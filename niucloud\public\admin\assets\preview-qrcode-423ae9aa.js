import{d as l,l as o,h as c,c as u,a as p,u as r,B as i,x as _}from"./index-30109030.js";const d=["src"],m=l({__name:"preview-qrcode",props:{value:{type:Object,default:{}}},setup(s,{expose:a}){const t=s;o(()=>t.value);const n=o(()=>{var e="";return e+=`width: ${t.value.width}px;`,e});return a({}),(e,f)=>(c(),u("div",{class:"pointer-events-none max-w-[720px]",style:_(r(n))},[p("img",{src:r(i)("static/resource/images/diy/qrcode.png"),class:"w-full h-full"},null,8,d)],4))}}),y=Object.freeze(Object.defineProperty({__proto__:null,default:m},Symbol.toStringTag,{value:"Module"}));export{y as _};
