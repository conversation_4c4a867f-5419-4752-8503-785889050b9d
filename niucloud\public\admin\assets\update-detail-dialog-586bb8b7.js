import{d as W,r as h,n as Z,R as F,$ as y,h as i,s as c,w as e,Z as $,c as w,e as a,i as o,t as n,C as p,a as s,F as z,W as J,ai as O,ao as P,aj as Q,a9 as X,K as Y,au as ee,av as te,M as ae,L as le,E as oe,N as se,V as de,a3 as ie,p as ne,g as ue}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                *//* empty css                  *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css                *//* empty css                             *//* empty css               */import{g as re,a as _e}from"./update-1cbb2a0f.js";import{_ as ce}from"./_plugin-vue_export-helper-c27b6911.js";const m=f=>(ne("data-v-5a12bd7b"),f=f(),ue(),f),pe={key:0,class:"update-detail"},me=m(()=>s("span",{class:"font-bold"},"基本信息",-1)),fe=m(()=>s("span",{class:"font-bold"},"动态内容",-1)),ve={class:"mb-4"},ge={class:"text-lg font-bold mb-2"},be=["innerHTML"],he={key:0,class:"images-section"},ye=m(()=>s("h4",{class:"text-md font-bold mb-2"},"动态图片",-1)),we={class:"image-grid"},ke=m(()=>s("span",{class:"font-bold"},"审核信息",-1)),xe={class:"audit-info"},Ve=m(()=>s("strong",null,"审核备注：",-1)),Ie=m(()=>s("span",{class:"font-bold"},"审核操作",-1)),Ee=W({__name:"update-detail-dialog",props:{modelValue:{type:Boolean,default:!1},updateId:{default:0}},setup(f,{emit:E}){const g=f,v=h(!1),k=h(!1),x=h(!1),t=h({}),d=Z({audit_status:0,audit_remark:""});F(()=>g.modelValue,l=>{v.value=l,l&&g.updateId&&S()}),F(v,l=>{E("update:modelValue",l)});const S=async()=>{k.value=!0;try{const l=await re(g.updateId);t.value=l,V()}catch(l){console.error("加载动态详情失败:",l),y.error("加载动态详情失败")}finally{k.value=!1}},T=l=>({0:"warning",1:"success",2:"danger"})[l]||"info",L=l=>({0:"待审核",1:"审核通过",2:"审核拒绝"})[l]||"未知",N=async()=>{if(!d.audit_status){y.warning("请选择审核结果");return}x.value=!0;try{await _e(g.updateId,d),y.success("审核成功"),E("refresh"),D()}catch(l){y.error(l.msg||"审核失败")}finally{x.value=!1}},V=()=>{d.audit_status=0,d.audit_remark=""},D=()=>{v.value=!1,V()};return(l,u)=>{const r=O,C=P,A=Q,b=X,R=Y,M=ee,j=te,I=ae,H=le,U=oe,q=se,G=de,K=ie;return i(),c(G,{modelValue:v.value,"onUpdate:modelValue":u[2]||(u[2]=_=>v.value=_),title:"动态详情",width:"800px","before-close":D},{default:e(()=>[$((i(),w("div",null,[t.value.update_id?(i(),w("div",pe,[a(b,{class:"mb-4",shadow:"never"},{header:e(()=>[me]),default:e(()=>[a(A,{column:2,border:""},{default:e(()=>[a(r,{label:"动态ID"},{default:e(()=>[o(n(t.value.update_id),1)]),_:1}),a(r,{label:"所属项目"},{default:e(()=>[o(n(t.value.project_name),1)]),_:1}),a(r,{label:"发布者"},{default:e(()=>[o(n(t.value.member_nickname),1)]),_:1}),a(r,{label:"发布时间"},{default:e(()=>[o(n(t.value.create_time_text),1)]),_:1}),a(r,{label:"审核状态"},{default:e(()=>[a(C,{type:T(t.value.audit_status)},{default:e(()=>[o(n(L(t.value.audit_status)),1)]),_:1},8,["type"])]),_:1}),a(r,{label:"公开状态"},{default:e(()=>[a(C,{type:t.value.is_public?"success":"info"},{default:e(()=>[o(n(t.value.is_public?"公开":"仅支持者可见"),1)]),_:1},8,["type"])]),_:1}),t.value.audit_time_text?(i(),c(r,{key:0,label:"审核时间"},{default:e(()=>[o(n(t.value.audit_time_text),1)]),_:1})):p("",!0),t.value.auditor_real_name?(i(),c(r,{key:1,label:"审核员"},{default:e(()=>[o(n(t.value.auditor_real_name),1)]),_:1})):p("",!0)]),_:1})]),_:1}),a(b,{class:"mb-4",shadow:"never"},{header:e(()=>[fe]),default:e(()=>[s("div",ve,[s("h3",ge,n(t.value.title),1),s("div",{class:"content-area",innerHTML:t.value.content},null,8,be)]),t.value.images&&t.value.images.length>0?(i(),w("div",he,[ye,s("div",we,[(i(!0),w(z,null,J(t.value.images,(_,B)=>(i(),c(R,{key:B,src:_,"preview-src-list":t.value.images,"initial-index":B,fit:"cover",class:"image-item","preview-teleported":""},null,8,["src","preview-src-list","initial-index"]))),128))])])):p("",!0)]),_:1}),t.value.audit_remark?(i(),c(b,{key:0,class:"mb-4",shadow:"never"},{header:e(()=>[ke]),default:e(()=>[s("div",xe,[s("p",null,[Ve,o(n(t.value.audit_remark),1)])])]),_:1})):p("",!0),t.value.audit_status===0?(i(),c(b,{key:1,shadow:"never"},{header:e(()=>[Ie]),default:e(()=>[a(q,{model:d,"label-width":"80px"},{default:e(()=>[a(I,{label:"审核结果",required:""},{default:e(()=>[a(j,{modelValue:d.audit_status,"onUpdate:modelValue":u[0]||(u[0]=_=>d.audit_status=_)},{default:e(()=>[a(M,{label:1},{default:e(()=>[o("审核通过")]),_:1}),a(M,{label:2},{default:e(()=>[o("审核拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(I,{label:"审核备注"},{default:e(()=>[a(H,{modelValue:d.audit_remark,"onUpdate:modelValue":u[1]||(u[1]=_=>d.audit_remark=_),type:"textarea",rows:3,placeholder:"请输入审核备注（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),a(I,null,{default:e(()=>[a(U,{type:"primary",onClick:N,loading:x.value,disabled:!d.audit_status},{default:e(()=>[o(" 提交审核 ")]),_:1},8,["loading","disabled"]),a(U,{onClick:V},{default:e(()=>[o("重置")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})):p("",!0)])):p("",!0)])),[[K,k.value]])]),_:1},8,["modelValue"])}}});const We=ce(Ee,[["__scopeId","data-v-5a12bd7b"]]);export{We as default};
