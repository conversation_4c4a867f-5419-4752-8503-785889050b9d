import{d as U,r as E,n as A,aN as I,h as f,c as P,e,w as n,a as u,t as i,u as l,q as t,i as d,F as R,W as q,s as j,Z as O,af as W,$ as Z,E as G,L as H,M as J,a4 as K,a1 as Q,N as X,a9 as Y,ag as ee,bR as te,ao as ae,ah as le,a2 as oe,a3 as ne}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                    *//* empty css                        *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  */import{_ as re}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import{b as se,a as ie,n as pe}from"./project-8517d3f3.js";import{_ as me}from"./project-edit.vue_vue_type_script_setup_true_lang-f050c413.js";/* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                        */import"./index-e3ceb692.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";import"./index.vue_vue_type_script_setup_true_lang-880b20ce.js";const ce={class:"main-container"},ue={class:"flex justify-between items-center"},de={class:"text-page-title"},_e={class:"mt-[10px]"},ge={class:"mt-[16px] flex justify-end"},lt=U({__name:"project",setup(be){const _=E(null),o=A({page:1,limit:10,total:0,loading:!1,data:[],searchParam:{name:"",status:"",category_id:""}}),h=E([]),m=(s=1)=>{o.loading=!0,o.page=s,se({page:o.page,limit:o.limit,...o.searchParam}).then(r=>{o.loading=!1,o.data=r.data.data,o.total=r.data.total}).catch(()=>{o.loading=!1})},w=()=>{ie({}).then(s=>{h.value=s.data})},x=()=>{o.searchParam={name:"",status:"",category_id:""},m()},C=()=>{_.value.setFormData(),_.value.showEdit=!0},k=s=>{_.value.setFormData(s),_.value.showEdit=!0},V=s=>{W.confirm(t("projectDeleteTip"),t("warning"),{confirmButtonText:t("confirm"),cancelButtonText:t("cancel"),type:"warning"}).then(()=>{pe(s).then(()=>{m(),Z.success(t("deleteSuccess"))})})},T=s=>({0:"info",1:"warning",2:"success",3:"success",4:"danger",5:"info"})[s]||"info";return I(()=>{m(),w()}),(s,r)=>{const D=re,g=G,B=H,b=J,c=K,y=Q,F=X,v=Y,p=ee,L=te,M=ae,N=le,S=oe,$=ne;return f(),P("div",ce,[e(v,{class:"box-card !border-none",shadow:"never"},{default:n(()=>[u("div",ue,[u("span",de,i(l(t)("projectManagement")),1),e(g,{type:"primary",onClick:C},{icon:n(()=>[e(D,{name:"element-Plus"})]),default:n(()=>[d(" "+i(l(t)("addProject")),1)]),_:1})]),e(v,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:n(()=>[e(F,{model:o.searchParam,"label-width":"90px",inline:!0},{default:n(()=>[e(b,{label:l(t)("projectName")},{default:n(()=>[e(B,{modelValue:o.searchParam.name,"onUpdate:modelValue":r[0]||(r[0]=a=>o.searchParam.name=a),placeholder:l(t)("projectNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(b,{label:l(t)("projectStatus")},{default:n(()=>[e(y,{modelValue:o.searchParam.status,"onUpdate:modelValue":r[1]||(r[1]=a=>o.searchParam.status=a),placeholder:l(t)("projectStatusPlaceholder"),clearable:""},{default:n(()=>[e(c,{label:l(t)("statusDraft"),value:"0"},null,8,["label"]),e(c,{label:l(t)("statusReview"),value:"1"},null,8,["label"]),e(c,{label:l(t)("statusActive"),value:"2"},null,8,["label"]),e(c,{label:l(t)("statusSuccess"),value:"3"},null,8,["label"]),e(c,{label:l(t)("statusFailed"),value:"4"},null,8,["label"]),e(c,{label:l(t)("statusClosed"),value:"5"},null,8,["label"])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),e(b,{label:l(t)("category")},{default:n(()=>[e(y,{modelValue:o.searchParam.category_id,"onUpdate:modelValue":r[2]||(r[2]=a=>o.searchParam.category_id=a),placeholder:l(t)("categoryPlaceholder"),clearable:""},{default:n(()=>[(f(!0),P(R,null,q(h.value,a=>(f(),j(c,{key:a.category_id,label:a.category_name,value:a.category_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),e(b,null,{default:n(()=>[e(g,{type:"primary",onClick:r[3]||(r[3]=a=>m())},{default:n(()=>[d(i(l(t)("search")),1)]),_:1}),e(g,{onClick:x},{default:n(()=>[d(i(l(t)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),u("div",_e,[O((f(),j(N,{data:o.data,size:"large"},{empty:n(()=>[u("span",null,i(o.loading?"":l(t)("emptyData")),1)]),default:n(()=>[e(p,{prop:"name",label:l(t)("projectName"),"min-width":"200"},null,8,["label"]),e(p,{prop:"category_name",label:l(t)("category"),"min-width":"120"},null,8,["label"]),e(p,{prop:"target_amount",label:l(t)("targetAmount"),"min-width":"120"},{default:n(({row:a})=>[u("span",null,"¥"+i(a.target_amount),1)]),_:1},8,["label"]),e(p,{prop:"current_amount",label:l(t)("currentAmount"),"min-width":"120"},{default:n(({row:a})=>[u("span",null,"¥"+i(a.current_amount),1)]),_:1},8,["label"]),e(p,{prop:"progress",label:l(t)("progress"),"min-width":"120"},{default:n(({row:a})=>[e(L,{percentage:Math.round(a.current_amount/a.target_amount*100)},null,8,["percentage"])]),_:1},8,["label"]),e(p,{prop:"status_name",label:l(t)("status"),"min-width":"100"},{default:n(({row:a})=>[e(M,{type:T(a.status)},{default:n(()=>[d(i(a.status_name),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(p,{prop:"create_time",label:l(t)("createTime"),"min-width":"180"},null,8,["label"]),e(p,{label:l(t)("operation"),fixed:"right",align:"right","min-width":"120"},{default:n(({row:a})=>[e(g,{type:"primary",link:"",onClick:z=>k(a)},{default:n(()=>[d(i(l(t)("edit")),1)]),_:2},1032,["onClick"]),e(g,{type:"primary",link:"",onClick:z=>V(a.project_id)},{default:n(()=>[d(i(l(t)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[$,o.loading]]),u("div",ge,[e(S,{"current-page":o.page,"onUpdate:current-page":r[4]||(r[4]=a=>o.page=a),"page-size":o.limit,"onUpdate:page-size":r[5]||(r[5]=a=>o.limit=a),layout:"total, sizes, prev, pager, next, jumper",total:o.total,onSizeChange:r[6]||(r[6]=a=>m()),onCurrentChange:m},null,8,["current-page","page-size","total"])])])]),_:1}),e(me,{ref_key:"projectEditDialog",ref:_,onComplete:m},null,512)])}}});export{lt as default};
