import{d as M,f as N,y as R,j as S,G as K,l as h,r as $,R as j,H as F,u as s,h as t,c as m,s as c,w as f,C as r,a as p,t as g,F as _,W as V,d3 as q,cY as A}from"./index-30109030.js";import"./el-menu-item-4ed993c7.js";import"./el-sub-menu-4ed993c7.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";const G={key:0,class:"w-[16px] h-[16px] relative flex items-center"},H={class:"ml-[10px]"},L={key:0,class:"w-[16px] h-[16px] relative flex items-center"},O={class:"ml-[10px]"},U={key:0,class:"w-[16px] h-[16px] relative flex items-center"},W={class:"ml-[10px]"},Y={key:2,class:"!border-0 !border-t-[1px] border-solid mx-[25px] bg-[#f7f7f7] my-[5px]"},C=M({__name:"menu-item",props:{routes:{type:Object,required:!0},level:{type:Number,default:1}},setup(a){const i=a,b=N(),l=R(),k=S(),E=S().routers;K();const n=h(()=>i.routes.meta);h(()=>{var o,u;const e={};return(o=k.siteInfo)==null||o.apps.forEach(v=>{e[v.key]=v}),(u=k.siteInfo)==null||u.site_addons.forEach(v=>{e[v.key]=v}),e});const I=h(()=>{var e;return(e=k.siteInfo)==null?void 0:e.site_addons.map(o=>o.key)}),y={};E.forEach(e=>{e.original_name=e.name,e.meta.addon&&(y[e.meta.addon]=e),e.meta.attr&&(y[e.meta.attr]=e)});const d=$(null);return j(l,()=>{var u;i.routes.name=="addon_list"&&(I.value.includes(l.meta.addon)&&y[l.meta.addon]?d.value=y[l.meta.addon]:l.meta.attr&&y[l.meta.attr]?d.value=y[l.meta.attr]:d.value=null);const e=F.get("darksideMarketingKeys"),o=(u=l.matched[1])==null?void 0:u.name;i.routes.name=="marketing_list"&&(e&&e.includes(o)?(d.value=l.matched[1]??[],d.value.meta.show=1):d.value=null)},{immediate:!0}),(e,o)=>{const u=D,v=q,w=A;return s(n).show?(t(),m(_,{key:0},[a.routes.children?(t(),c(v,{key:0,index:String(a.routes.name)},{title:f(()=>[i.level==1?(t(),m("div",G,[s(n).icon?(t(),c(u,{key:0,name:s(n).icon,class:"absolute !w-auto"},null,8,["name"])):r("",!0)])):r("",!0),p("span",H,g(s(n).title),1)]),default:f(()=>[(t(!0),m(_,null,V(a.routes.children,(x,B)=>(t(),c(C,{routes:x,key:B,level:i.level+1},null,8,["routes","level"]))),128)),a.routes.name=="addon_list"||a.routes.name=="marketing_list"?(t(),m(_,{key:0},[d.value?(t(),c(C,{routes:d.value,key:e.index,level:i.level+1},null,8,["routes","level"])):r("",!0)],64)):r("",!0)]),_:1},8,["index"])):(t(),m(_,{key:1},[s(n).addon&&s(n).parent_route&&s(n).parent_route.addon==""?(t(),c(w,{key:0,index:String(a.routes.name),onClick:o[0]||(o[0]=x=>s(b).push({name:a.routes.name}))},{title:f(()=>[i.level==1?(t(),m("div",L,[s(n).icon?(t(),c(u,{key:0,name:s(n).icon,class:"absolute !w-auto"},null,8,["name"])):r("",!0)])):r("",!0),p("span",O,g(s(n).title),1)]),_:1},8,["index"])):(t(),c(w,{key:1,index:String(a.routes.name),onClick:o[1]||(o[1]=x=>s(b).push({name:a.routes.name}))},{title:f(()=>[i.level==1?(t(),m("div",U,[s(n).icon?(t(),c(u,{key:0,name:s(n).icon,class:"absolute !w-auto"},null,8,["name"])):r("",!0)])):r("",!0),p("span",W,g(s(n).title),1)]),_:1},8,["index"]))],64)),a.routes.is_border?(t(),m("div",Y)):r("",!0)],64)):r("",!0)}}});export{C as _};
