import{d as ge,r as y,n as R,l as ce,q as n,a5 as fe,h as f,c as U,e as t,w as a,i as u,t as p,u as o,a as d,Z as _e,s as S,v as be,C as $,F,W as M,af as j,$ as xe,a$ as ye,E as he,aL as ve,ag as Ve,a0 as Ce,aY as we,ao as Se,ah as Ee,a2 as Te,V as Ne,L as ke,M as Ue,au as Le,av as Be,N as Re,a3 as $e}from"./index-30109030.js";/* empty css                   *//* empty css                */import De from"./index-e3ceb692.js";/* empty css                       *//* empty css                 */import"./el-form-item-4ed993c7.js";/* empty css                 *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                  *//* empty css                 */import{c as Ie,p as qe,q as Pe,t as A}from"./notice-9865bf12.js";/* empty css                       *//* empty css                 *//* empty css                   */const ze=d("p",{class:""},"签名数据的变更（新增 / 删除）需经过五分钟的生效周期，在此期间系统将完成数据同步与更新",-1),Fe={class:"flex justify-between items-center mb-[16px]"},Me={class:"mb-[10px] flex items-center"},je={key:0,class:"text-red-600"},Ae={style:{display:"inline-flex","align-items":"center"}},Ye={class:"mr-[5px]"},Qe=d("br",null,null,-1),Ge=d("br",null,null,-1),Oe=d("br",null,null,-1),We=d("br",null,null,-1),Xe={class:"flex gap-[5px]"},Ze={class:"mt-[16px] flex justify-end"},He=d("div",{class:"ml-[150px] text-[12px] text-[#999] leading-[20px]"},"必须由【】包裹，例如：【test】",-1),Je=d("div",{class:"my-[5px] ml-[150px] text-[12px] text-[#999] leading-[20px]"},"字数要求在2-20个字符，不能使用空格和特殊符号“ - + = * & % # @ ~等;",-1),Ke=d("div",{class:"ml-[150px] text-[12px] text-[#999] leading-[20px]"},"当签名来源为商标、APP、小程序、事业单位简称或企业名称简称时，需必填此字段",-1),el=d("div",{class:"my-[5px] ml-[150px] text-[12px] text-[#999] leading-[20px]"},"当签名来源为事业单位全称或企业名称全称时，选填此字段。",-1),Nl=ge({__name:"sms_signature",props:{username:{type:String,default:""}},emits:["select"],setup(Y,{expose:Q,emit:G}){const E=Y,V=y(!1),C=y(!1),D={defaultSign:0,imgUrl:"",contentExample:"",signType:"",signSource:"",principalIdCard:"",principalName:"",principalMobile:"",legalPerson:"",creditCode:"",companyName:"",signature:""},i=R({...D}),h=R({signTypeList:[],signSourceList:[]});(()=>{Ie().then(s=>{h.signTypeList=s.data.sign_type_list,h.signSourceList=s.data.sign_source_list,i.signSource=s.data.sign_source_list[0].type,i.signType=s.data.sign_type_list[0].type})})();const I=y(),O=ce(()=>({signature:[{required:!0,message:"请输入短信签名",trigger:"blur"},{validator:(s,l,m)=>{if(!/^【[^【】]*】$/.test(l))return m(new Error("短信签名必须被【】包裹"));const w=l.slice(1,-1);if(!(w.length>=2&&w.length<=20))return m(new Error("短信签名内容需在 2-20 个字符之间"));if(/[\s\-+=*&%#@~;]/.test(w))return m(new Error("短信签名不能包含空格或特殊字符 - + = * & % # @ ~ ;"));m()},trigger:"blur"}],principalMobile:[{required:!0,message:"请输入经办人手机号",trigger:"blur"},{validator:X,trigger:"blur"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur"}],contentExample:[{required:!0,message:"请输入短信示例内容",trigger:"blur"}],creditCode:[{required:!0,message:"请输入社会统一信用代码",trigger:"blur"}],legalPerson:[{required:!0,message:"请输入法人姓名",trigger:"blur"}],principalName:[{required:!0,message:"请输入经办人姓名",trigger:"blur"}],principalIdCard:[{required:!0,message:"请输入经办人身份证",trigger:"blur"},{validator:W,trigger:"blur"}],imgUrl:[{validator:(s,l,m)=>{([3,4,5].includes(i.signSource)||i.signType===1)&&(!l||l.length===0)?m(new Error("请上传图片")):m()},trigger:"blur"}]})),W=(s,l,m)=>{l&&!/^[1-9]\d{5}(19|20)\d{2}((0\d)|(1[0-2]))(([0-2]\d)|3[0-1])\d{3}([0-9Xx])$/.test(l)?m(new Error(n("请输入正确的身份证号码"))):m()},X=(s,l,m)=>{l&&!/^1[3-9]\d{9}$/.test(l)?m(new Error(n("请输入正确的手机号码"))):m()},Z=async()=>{var s;await((s=I.value)==null?void 0:s.validate(async l=>{l&&qe(E.username,i).then(m=>{setTimeout(()=>{C.value=!1,v()},500)})}))},r=R({page:1,limit:10,total:0,loading:!1,data:[],searchParam:{}}),H=()=>{V.value=!0,v()},v=()=>{r.loading=!0;let s={page:r.page,limit:r.limit,...r.searchParam};Pe(E.username,s).then(l=>{r.loading=!1,r.data=l.data.data,r.total=l.data.total}).catch(()=>{r.loading=!1})},J=()=>{Object.assign(i,D),i.signSource=h.signSourceList[0].type,i.signType=h.signTypeList[0].type,C.value=!0},K=s=>{j.confirm(n("确定删除该签名吗？"),n("提示"),{confirmButtonText:n("确定"),cancelButtonText:n("取消"),type:"warning"}).then(()=>{A(E.username,{signatures:[s.sign]}).then(l=>{r.loading=!0,setTimeout(()=>{v()},1e3)})})},T=y(),N=y(!1),ee=s=>{N.value=!1,q.value.toggleAllSelection()},q=y(),x=y([]),le=s=>{x.value=s,T.value=!1,x.value.length>0&&x.value.length<r.data.length?N.value=!0:N.value=!1,x.value.length==r.data.length&&r.data.length&&x.value.length&&(T.value=!0)},te=(s,l)=>!s.is_default,ae=()=>{if(x.value.length==0){xe({type:"warning",message:`${n("请选择要删除的签名")}`});return}j.confirm(n("确定删除选中的签名吗？"),n("warning"),{confirmButtonText:n("confirm"),cancelButtonText:n("cancel"),type:"warning"}).then(()=>{const s=[];x.value.forEach(l=>{s.push(l.sign)}),A(E.username,{signatures:s}).then(()=>{r.loading=!0,setTimeout(()=>{v()},1e3)}).catch(()=>{})})},ne=s=>{V.value=!1,G("select",s)};return Q({open:H}),(s,l)=>{const m=ye,c=he,w=ve,_=Ve,P=fe("QuestionFilled"),oe=Ce,ie=we,L=Se,se=Ee,re=Te,z=Ne,b=ke,g=Ue,k=Le,B=Be,de=De,ue=Re,pe=$e;return f(),U("div",null,[t(z,{modelValue:V.value,"onUpdate:modelValue":l[5]||(l[5]=e=>V.value=e),title:o(n)("选择签名"),width:"1200px","destroy-on-close":"","close-on-click-modal":!1},{footer:a(()=>[t(c,{onClick:l[4]||(l[4]=e=>V.value=!1)},{default:a(()=>[u(p(o(n)("cancel")),1)]),_:1})]),default:a(()=>[t(m,{type:"warning",closable:!1,class:"!mb-[10px]"},{default:a(()=>[ze]),_:1}),d("div",Fe,[t(c,{type:"primary",onClick:J},{default:a(()=>[u(p(o(n)("添加短信签名")),1)]),_:1})]),d("div",Me,[t(w,{modelValue:T.value,"onUpdate:modelValue":l[0]||(l[0]=e=>T.value=e),size:"large",class:"px-[14px]",onChange:ee,indeterminate:N.value},null,8,["modelValue","indeterminate"]),t(c,{onClick:ae,size:"small"},{default:a(()=>[u(p(o(n)("批量删除")),1)]),_:1})]),_e((f(),S(se,{data:r.data,size:"large",ref_key:"smsSignListTableRef",ref:q,onSelectionChange:le},{empty:a(()=>[d("span",null,p(r.loading?"":o(n)("emptyData")),1)]),default:a(()=>[t(_,{type:"selection",selectable:te,width:"55"}),t(_,{prop:"sign",label:o(n)("签名名称"),"min-width":"200"},null,8,["label"]),t(_,{prop:"is_default",label:o(n)("使用状态"),"min-width":"120"},{default:a(({row:e})=>[d("div",null,p(e.is_default?o(n)("使用中"):o(n)("未使用")),1)]),_:1},8,["label"]),t(_,{prop:"auditResultName",label:o(n)("审核状态"),"min-width":"200"},{default:a(({row:e})=>[d("div",null,[d("div",{class:be([e.auditResult==2?"text-green-600":""])},p(e.auditResultName),3),e.auditResult!=2?(f(),U("div",je,p(e.auditMsg),1)):$("",!0)])]),_:1},8,["label"]),t(_,{prop:"realNameDx",label:o(n)("实名状态"),"min-width":"200"},{header:a(()=>[d("div",Ae,[d("span",Ye,p(o(n)("实名状态")),1),t(ie,{class:"box-item",effect:"light",placement:"top"},{content:a(()=>[u(" 状态标识说明："),Qe,u("未实名状态显示为灰色；"),Ge,u("实名通过状态显示为绿色；"),Oe,u("实名失败状态显示为红色。"),We,u(" 短信接收条件：仅当手机号在对应运营商处实名通过后，才可接收短信。 ")]),default:a(()=>[t(oe,{color:"#666"},{default:a(()=>[t(P)]),_:1})]),_:1})])]),default:a(({row:e})=>[d("div",Xe,[t(L,{type:e.realNameLt==0?"info":e.realNameLt==1?"success":"danger"},{default:a(()=>[u(p(o(n)("联通")),1)]),_:2},1032,["type"]),t(L,{type:e.realNameYd==0?"info":e.realNameYd==1?"success":"danger"},{default:a(()=>[u(p(o(n)("移动")),1)]),_:2},1032,["type"]),t(L,{type:e.realNameDx==0?"info":e.realNameDx==1?"success":"danger"},{default:a(()=>[u(p(o(n)("电信")),1)]),_:2},1032,["type"])])]),_:1},8,["label"]),t(_,{prop:"create_time",label:o(n)("createTime"),"min-width":"120"},{default:a(({row:e})=>[d("div",null,p(e.createTime),1)]),_:1},8,["label"]),t(_,{label:o(n)("操作"),fixed:"right",align:"right","min-width":"120"},{default:a(({row:e})=>[!e.is_default&&e.auditResult==2?(f(),S(c,{key:0,type:"primary",link:"",onClick:me=>ne(e)},{default:a(()=>[u(p(o(n)("使用")),1)]),_:2},1032,["onClick"])):$("",!0),e.is_default?$("",!0):(f(),S(c,{key:1,type:"primary",link:"",onClick:me=>K(e)},{default:a(()=>[u(p(o(n)("删除")),1)]),_:2},1032,["onClick"]))]),_:1},8,["label"])]),_:1},8,["data"])),[[pe,r.loading]]),d("div",Ze,[t(re,{"current-page":r.page,"onUpdate:current-page":l[1]||(l[1]=e=>r.page=e),"page-size":r.limit,"onUpdate:page-size":l[2]||(l[2]=e=>r.limit=e),layout:"total, sizes, prev, pager, next, jumper",total:r.total,onSizeChange:l[3]||(l[3]=e=>v()),onCurrentChange:v},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"]),t(z,{modelValue:C.value,"onUpdate:modelValue":l[20]||(l[20]=e=>C.value=e),title:o(n)("添加签名"),width:"800px","destroy-on-close":"","close-on-click-modal":!1},{footer:a(()=>[t(c,{onClick:l[18]||(l[18]=e=>C.value=!1)},{default:a(()=>[u(p(o(n)("cancel")),1)]),_:1}),t(c,{type:"primary",onClick:l[19]||(l[19]=e=>Z())},{default:a(()=>[u(p(o(n)("confirm")),1)]),_:1})]),default:a(()=>[t(ue,{"label-width":"150px",model:i,ref_key:"formRef",ref:I,rules:o(O),class:"page-form ml-[20px]"},{default:a(()=>[t(g,{label:o(n)("短信签名"),prop:"signature"},{default:a(()=>[t(b,{modelValue:i.signature,"onUpdate:modelValue":l[6]||(l[6]=e=>i.signature=e),placeholder:"请输入短信签名",class:"input-width",maxlength:"20","show-word-limit":"",clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),He,Je,t(g,{label:o(n)("短信示例内容"),prop:"contentExample"},{default:a(()=>[t(b,{modelValue:i.contentExample,"onUpdate:modelValue":l[7]||(l[7]=e=>i.contentExample=e),placeholder:"请输入短信示例内容",clearable:"",maxlength:"50","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("企业名称"),prop:"companyName"},{default:a(()=>[t(b,{modelValue:i.companyName,"onUpdate:modelValue":l[8]||(l[8]=e=>i.companyName=e),placeholder:"请输入企业名称",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("社会统一信用代码"),prop:"creditCode"},{default:a(()=>[t(b,{modelValue:i.creditCode,"onUpdate:modelValue":l[9]||(l[9]=e=>i.creditCode=e),placeholder:"请输入社会统一信用代码",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("法人姓名"),prop:"legalPerson"},{default:a(()=>[t(b,{modelValue:i.legalPerson,"onUpdate:modelValue":l[10]||(l[10]=e=>i.legalPerson=e),placeholder:"请输入法人姓名",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("经办人姓名"),prop:"principalName"},{default:a(()=>[t(b,{modelValue:i.principalName,"onUpdate:modelValue":l[11]||(l[11]=e=>i.principalName=e),placeholder:"请输入经办人姓名",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("经办人手机号"),prop:"principalMobile"},{default:a(()=>[t(b,{modelValue:i.principalMobile,"onUpdate:modelValue":l[12]||(l[12]=e=>i.principalMobile=e),placeholder:"请输入经办人手机号",clearable:"",maxlength:"20","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("经办人身份证"),prop:"principalIdCard"},{default:a(()=>[t(b,{modelValue:i.principalIdCard,"onUpdate:modelValue":l[13]||(l[13]=e=>i.principalIdCard=e),placeholder:"请输入经办人身份证",clearable:"",maxlength:"18","show-word-limit":"",class:"input-width"},null,8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("签名来源")},{default:a(()=>[t(B,{modelValue:i.signSource,"onUpdate:modelValue":l[14]||(l[14]=e=>i.signSource=e)},{default:a(()=>[(f(!0),U(F,null,M(h.signSourceList,e=>(f(),S(k,{key:e.type,label:e.type},{default:a(()=>[u(p(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("签名类型")},{default:a(()=>[t(B,{modelValue:i.signType,"onUpdate:modelValue":l[15]||(l[15]=e=>i.signType=e)},{default:a(()=>[(f(!0),U(F,null,M(h.signTypeList,e=>(f(),S(k,{key:e.type,label:e.type},{default:a(()=>[u(p(e.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(g,{label:o(n)("上传图片"),prop:"imgUrl"},{default:a(()=>[t(de,{modelValue:i.imgUrl,"onUpdate:modelValue":l[16]||(l[16]=e=>i.imgUrl=e),limit:1},null,8,["modelValue"])]),_:1},8,["label"]),Ke,el,t(g,{label:o(n)("是否默认")},{default:a(()=>[t(B,{modelValue:i.defaultSign,"onUpdate:modelValue":l[17]||(l[17]=e=>i.defaultSign=e)},{default:a(()=>[t(k,{label:1},{default:a(()=>[u("是")]),_:1}),t(k,{label:0},{default:a(()=>[u("否")]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}});export{Nl as _};
