import{d as X,l as D,r as w,n as I,h as y,c as C,a as s,b as Z,e as r,w as i,i as g,t as d,u as l,q as o,Z as k,_ as S,D as G,X as H,s as J,B as Q,a6 as W,b4 as Y,$ as N,E as ee,L as te,M as ae,N as le,ag as oe,ah as se,a2 as re,V as ne,a3 as me}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  */import{_ as ie}from"./member_head-d9fd7b2c.js";import{p as de}from"./member-a1692c0d.js";import{_ as pe}from"./_plugin-vue_export-helper-c27b6911.js";const ce={class:"inline-block ml-[10px] text-[14px]"},ue={class:"text-primary mx-[2px]"},_e={class:"flex items-center"},fe={class:"mr-[10px] rounded-full w-[50px] h-[50px] flex items-center justify-center"},be=["src"],ge={key:1,class:"max-w-[50px] max-h-[50px]",src:ie,alt:""},he={class:"flex flex-col"},ve={class:"mt-[16px] flex"},xe={class:"flex items-center flex-1"},ye={class:"mr-[10px]"},ke={class:"text-primary mx-[2px]"},we={class:"dialog-footer"},Ce=X({__name:"select-member",props:{modelValue:{type:Array,default:()=>[]},max:{type:Number,default:0},min:{type:Number,default:0}},emits:["update:modelValue","memberSelect"],setup(P,{expose:T,emit:V}){const _=P,c=D({get(){return _.modelValue},set(a){V("update:modelValue",a)}}),b=w(!1),n=I({}),f=D(()=>Object.keys(n).length),t=I({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{keyword:"",verify_member_ids:""}}),E=w(),x=w(),z=(a,e)=>{let p=!1;for(let u=0;u<a.length;u++)if(a[u].member_id==e.member_id){p=!0;break}p?n["member_"+e.member_id]=e:delete n["member_"+e.member_id]},B=a=>{a.length?a.forEach(e=>{n["member_"+e.member_id]=e}):t.data.forEach(e=>{delete n["member_"+e.member_id]})},h=(a=1,e=null)=>{t.loading=!0,t.page=a;const p=W(t.searchParam);de({page:t.page,limit:t.limit,...p}).then(u=>{t.loading=!1,t.data=u.data.data,t.total=u.data.total,e&&e(u.data.verify_member_ids),$()}).catch(()=>{t.loading=!1})},$=()=>{Y(()=>{if(x.value)for(let a=0;a<t.data.length;a++)x.value.toggleRowSelection(t.data[a],!1),n["member_"+t.data[a].member_id]&&x.value.toggleRowSelection(t.data[a],!0)})},F=a=>{a&&(a.resetFields(),h())},L=()=>{t.searchParam.verify_member_ids=c.value,h(1,a=>{if(c.value){for(let e in n){const p=e.replace("member_","");c.value.includes(Number(p))||delete n[e]}c.value.forEach(e=>{n["member_"+e]||(n["member_"+e]={})});for(let e=0;e<t.data.length;e++)c.value.indexOf(t.data[e].member_id)!=-1&&(n["member_"+t.data[e].member_id]=t.data[e])}}),b.value=!0},R=()=>{for(let a in n)delete n[a];$()},U=()=>{if(_.min&&f.value<_.min){N({type:"warning",message:`${o("所选会员数量不能少于")}${_.min}${o("个")}`});return}if(_.max&&_.max>0&&f.value&&f.value>_.max){N({type:"warning",message:`${o("所选会员数量不能超过")}${_.max}${o("个")}`});return}let a=[];for(let e in n)a.push(parseInt(e.replace("member_","")));c.value.splice(0,c.value.length,...a),V("memberSelect",n),b.value=!1};return T({showDialog:b,selectMember:n,selectMemberNum:f}),(a,e)=>{const p=ee,u=te,M=ae,j=le,v=oe,A=se,K=re,O=ne,q=me;return y(),C("div",null,[s("div",{onClick:L},[Z(a.$slots,"default",{},()=>[r(p,null,{default:i(()=>[g(d(l(o)("选择会员")),1)]),_:1}),k(s("div",ce,[s("span",null,d(l(o)("已选")),1),s("span",ue,d(l(c).length),1),s("span",null,d(l(o)("个")),1)],512),[[S,l(c).length]])],!0)]),r(O,{modelValue:b.value,"onUpdate:modelValue":e[8]||(e[8]=m=>b.value=m),title:l(o)("会员选择"),width:"1000px","destroy-on-close":!0,"close-on-click-modal":!1},{footer:i(()=>[s("span",we,[r(p,{onClick:e[7]||(e[7]=m=>b.value=!1)},{default:i(()=>[g(d(l(o)("cancel")),1)]),_:1}),r(p,{type:"primary",onClick:U},{default:i(()=>[g(d(l(o)("confirm")),1)]),_:1})])]),default:i(()=>[r(j,{inline:!0,model:t.searchParam,ref_key:"searchFormRef",ref:E},{default:i(()=>[r(M,{label:l(o)("会员名称"),prop:"keyword",class:"form-item-wrap"},{default:i(()=>[r(u,{modelValue:t.searchParam.keyword,"onUpdate:modelValue":e[0]||(e[0]=m=>t.searchParam.keyword=m),modelModifiers:{trim:!0},placeholder:l(o)("memberInfoPlaceholder"),maxlength:"60",onKeydown:e[1]||(e[1]=G(H(()=>{},["prevent"]),["enter","native"]))},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),r(M,{class:"form-item-wrap"},{default:i(()=>[r(p,{type:"primary",onClick:e[2]||(e[2]=m=>h())},{default:i(()=>[g(d(l(o)("search")),1)]),_:1}),r(p,{onClick:e[3]||(e[3]=m=>F(E.value))},{default:i(()=>[g(d(l(o)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),k((y(),J(A,{data:t.data,size:"large",ref_key:"memberListTableRef",ref:x,"max-height":"400",onSelect:z,onSelectAll:B},{empty:i(()=>[s("span",null,d(t.loading?"":l(o)("emptyData")),1)]),default:i(()=>[r(v,{type:"selection",width:"55"}),r(v,{prop:"nickname","show-overflow-tooltip":!0,label:l(o)("memberInfo"),"min-width":"150"},{default:i(({row:m})=>[s("div",_e,[s("div",fe,[m.headimg?(y(),C("img",{key:0,class:"max-w-[50px] max-h-[50px]",src:l(Q)(m.headimg),alt:""},null,8,be)):(y(),C("img",ge))]),s("div",he,[s("span",null,d(m.nickname||""),1)])])]),_:1},8,["label"]),r(v,{prop:"mobile",label:l(o)("mobile"),width:"180"},null,8,["label"]),r(v,{prop:"point",label:l(o)("point"),"min-width":"80"},null,8,["label"]),r(v,{prop:"balance",label:l(o)("balance"),"min-width":"80"},null,8,["label"])]),_:1},8,["data"])),[[q,t.loading]]),s("div",ve,[s("div",xe,[k(s("div",ye,[s("span",null,d(l(o)("已选择")),1),s("span",ke,d(l(f)),1),s("span",null,d(l(o)("个会员")),1)],512),[[S,l(f)]]),k(r(p,{type:"primary",link:"",onClick:R},{default:i(()=>[g(d(l(o)("取消选择")),1)]),_:1},512),[[S,l(f)]])]),r(K,{"current-page":t.page,"onUpdate:current-page":e[4]||(e[4]=m=>t.page=m),"page-size":t.limit,"onUpdate:page-size":e[5]||(e[5]=m=>t.limit=m),layout:"total, sizes, prev, pager, next, jumper",total:t.total,onSizeChange:e[6]||(e[6]=m=>h()),onCurrentChange:h},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"])])}}});const Xe=pe(Ce,[["__scopeId","data-v-8c15842e"]]);export{Xe as default};
