function g(e,t){e=e.replace(/=/g,"");var o=[];switch(t.constructor){case String:case Number:case Boolean:o.push(encodeURIComponent(e)+"="+encodeURIComponent(t));break;case Array:t.forEach(function(r){o=o.concat(g(e+"[]=",r))});break;case Object:Object.keys(t).forEach(function(r){var n=t[r];o=o.concat(g(e+"["+r+"]",n))})}return o}function f(e){var t=[];return e.forEach(function(o){typeof o=="string"?t.push(o):t=t.concat(f(o))}),t}function w(e,t,o){if(t===void 0&&(t={}),typeof e!="string")throw new Error('[Vue-jsonp] Type of param "url" is not string.');if(typeof t!="object"||!t)throw new Error("[Vue-jsonp] Invalid params, should be an object.");return o=typeof o=="number"?o:5e3,new Promise(function(r,n){var c=typeof t.callbackQuery=="string"?t.callbackQuery:"callback",l=typeof t.callbackName=="string"?t.callbackName:"jsonp_"+(Math.floor(1e5*Math.random())*Date.now()).toString(16);t[c]=l,delete t.callbackQuery,delete t.callbackName;var a=[];Object.keys(t).forEach(function(y){a=a.concat(g(y,t[y]))});var p=f(a).join("&"),s=function(){d(),clearTimeout(h),n({status:400,statusText:"Bad Request"})},d=function(){u.removeEventListener("error",s)},b=function(){document.body.removeChild(u),delete window[l]},h=null;o>-1&&(h=setTimeout(function(){d(),b(),n({statusText:"Request Timeout",status:408})},o)),window[l]=function(y){clearTimeout(h),d(),b(),r(y)};var u=document.createElement("script");u.addEventListener("error",s),u.src=e+(/\?/.test(e)?"&":"?")+p,document.body.appendChild(u)})}const i={},M=(e,t)=>{const o=window.TMap,r=o.LatLng;t.radius=t.radius??1e3,t.center=t.center??{lat:e.getCenter().lat,lng:e.getCenter().lng};const n=[Math.floor(Math.random()*255),Math.floor(Math.random()*255),Math.floor(Math.random()*255)],c=new o.MultiCircle({map:e,styles:{circle:new o.CircleStyle({color:`rgba(${n.toString()}, .4)`,showBorder:!0,borderColor:`rgb(${n.toString()})`,borderWidth:2})},geometries:[{styleId:"circle",center:new r(t.center.lat,t.center.lng),radius:parseInt(t.radius),id:t.key}]});i[t.key]={graphical:c};const l=new o.tools.GeometryEditor({map:e,overlayList:[{overlay:c,id:t.key}],actionMode:o.tools.constants.EDITOR_ACTION.INTERACT,activeOverlayId:t.key,selectable:!0});l.on("adjust_complete",a=>{t.center={lat:a.center.lat,lng:a.center.lng},t.radius=parseInt(a.radius)}),i[t.key]={graphical:c,editor:l}},T=(e,t)=>{const o=window.TMap,r=o.LatLng,{lat:n,lng:c}=e.getCenter();t.paths=t.paths??[{lat:n+.01,lng:c+.01},{lat:n-.01,lng:c+.01},{lat:n-.01,lng:c-.01},{lat:n+.01,lng:c-.01}];const l=[Math.floor(Math.random()*255),Math.floor(Math.random()*255),Math.floor(Math.random()*255)],a=new o.MultiPolygon({map:e,styles:{polygon:new o.PolygonStyle({color:`rgba(${l.toString()}, .4)`,showBorder:!0,borderColor:`rgb(${l.toString()})`,borderWidth:2})},geometries:[{id:t.key,styleId:"polygon",paths:t.paths.map(s=>new r(s.lat,s.lng))}]}),p=new o.tools.GeometryEditor({map:e,overlayList:[{overlay:a,id:t.key}],actionMode:o.tools.constants.EDITOR_ACTION.INTERACT,activeOverlayId:t.key,selectable:!0});p.on("adjust_complete",s=>{t.paths=s.paths.map(d=>({lat:d.lat,lng:d.lng}))}),i[t.key]={graphical:a,editor:p}},k=e=>{i[e].graphical.remove(e),i[e].editor.delete()},v=e=>{i[e].editor.select([e])},L=e=>{const t=window.TMap;return t.LatLng,new t.MultiMarker({map:e,geometries:[{id:"center",position:e.getCenter()}]})},m=e=>w(`https://apis.map.qq.com/ws/geocoder/v1/?key=${e.mapKey}&location=${e.lat},${e.lng}&output=jsonp&callback=latLngToAddress`,{callbackName:"latLngToAddress"}),C=e=>w(`https://apis.map.qq.com/ws/geocoder/v1/?key=${e.mapKey}&address=${e.address}&output=jsonp&callback=addressToLatLng`,{callbackName:"addressToLatLng"});export{C as a,M as b,L as c,T as d,k as e,m as l,v as s};
