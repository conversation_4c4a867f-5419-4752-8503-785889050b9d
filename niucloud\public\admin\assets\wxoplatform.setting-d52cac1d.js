const e="开放平台设置",o="请输入微信第三方平台AppId",p="请输入微信第三方平台AppSecret",s="请输入消息校验Token",a="请输入消息加解密Key",t="开放平台通信",i="授权发起页域名",n="授权事件接收URL",l="消息校验Token",c="消息加解密Key",r="消息与事件接收URL",d="公众号开发域名",m="小程序服务器域名",y="小程序业务域名",g="开发者设置",u="开发者邮箱",v="开发者手机号",D="开发者QQ",P="开发者微信",S="在代替公众号或小程序收发消息过程中使用。必须是长度为43位的字符串，只能是字母和数字。",h="重新生成",K="消息与事件接收",T="域名配置",w="开发小程序配置",b="开发小程序appid",A="代码上传密钥",f="",R="如果小程序代码上传开启了ip白名单设置，在ip白名单中添加ip：",U="开发小程序APPID",W="请输入开发小程序APPID",k="站点小程序同步",Q="是否要给该套餐下已授权小程序的站点提交代码？",I={oplatformSetting:e,appidPlaceholder:o,appSecretPlaceholder:p,tokenPlaceholder:s,aesKeyPlaceholder:a,oplatformComm:t,empowerStartDomain:i,empowerReceiveUrl:n,messageValidationToken:l,messageDecryptKey:c,messageReceiveUrl:r,wechatDomain:d,weappDomain:m,weappBusinessDomain:y,oplatformBuilder:g,builderEmail:u,builderMobile:v,builderQQ:D,builderWx:P,messageDecryptKeyTips:S,regenerate:h,messagesReceiving:K,domainSetting:T,developerWeappUpload:w,developerAppid:b,uploadKey:A,uploadKeyTips:f,uploadIpTips:R,developAppid:U,developAppidPlaceholder:W,siteWeappSync:k,syncSiteWeappTips:Q};export{a as aesKeyPlaceholder,p as appSecretPlaceholder,o as appidPlaceholder,u as builderEmail,v as builderMobile,D as builderQQ,P as builderWx,I as default,U as developAppid,W as developAppidPlaceholder,b as developerAppid,w as developerWeappUpload,T as domainSetting,n as empowerReceiveUrl,i as empowerStartDomain,c as messageDecryptKey,S as messageDecryptKeyTips,r as messageReceiveUrl,l as messageValidationToken,K as messagesReceiving,g as oplatformBuilder,t as oplatformComm,e as oplatformSetting,h as regenerate,k as siteWeappSync,Q as syncSiteWeappTips,s as tokenPlaceholder,R as uploadIpTips,A as uploadKey,f as uploadKeyTips,y as weappBusinessDomain,m as weappDomain,d as wechatDomain};
