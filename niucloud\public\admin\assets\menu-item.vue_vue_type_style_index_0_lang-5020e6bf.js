import{d as B,f as R,y as M,j as b,G as N,l as k,r as F,R as V,u as e,h as o,c,s as u,w as p,C as d,a as _,t as f,F as y,v as C,W as $,d3 as q,cY as A}from"./index-30109030.js";import"./el-menu-item-4ed993c7.js";import"./el-sub-menu-4ed993c7.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";const G={key:0,class:"flex h-full flex-col items-center justify-center"},K={class:"leading-none mt-[5px]"},L={key:0,class:"flex h-full flex-col items-center justify-center"},O={class:"leading-none mt-[5px]"},U=B({__name:"menu-item",props:{routes:{type:Object,required:!0},level:{type:Number,default:1}},setup(i){const n=i,z=R(),m=M(),x=b(),I=b().routers,h=N(),t=k(()=>n.routes.meta);k(()=>{var l,a;const s={};return(l=x.siteInfo)==null||l.apps.forEach(r=>{s[r.key]=r}),(a=x.siteInfo)==null||a.site_addons.forEach(r=>{s[r.key]=r}),s});const E=k(()=>{var s;return(s=x.siteInfo)==null?void 0:s.site_addons.map(l=>l.key)}),v={};I.forEach(s=>{s.original_name=s.name,s.meta.addon&&(v[s.meta.addon]=s)});const g=F(null);return V(m,()=>{n.routes.name=="addon_list"&&(E.value.includes(m.meta.addon)&&v[m.meta.addon]?g.value=v[m.meta.addon]:g.value=null)},{immediate:!0}),(s,l)=>{const a=D,r=q,j=A;return e(t).show?(o(),c(y,{key:0},[e(t).type==0&&i.routes.children?(o(),u(r,{key:0,index:String(i.routes.name)},{title:p(()=>[e(h).menuIsCollapse&&n.level==1?(o(),c("div",G,[e(t).icon?(o(),u(a,{key:0,name:e(t).icon,size:"20px",class:"leading-none"},null,8,["name"])):d("",!0),_("div",K,f(e(t).short_title||e(t).title),1)])):(o(),c(y,{key:1},[e(t).icon&&n.level!=1?(o(),u(a,{key:0,name:e(t).icon,size:n.level==1?"20px":"16px",class:"absolute"},null,8,["name","size"])):d("",!0)],64)),_("span",{class:C(["text-[14px]",{"font-bold":n.level===1," ml-[20px]":e(t).icon&&n.level!=1}])},f(e(t).title),3)]),default:p(()=>[(o(!0),c(y,null,$(i.routes.children,(S,w)=>(o(),u(U,{routes:S,key:w,level:n.level+1},null,8,["routes","level"]))),128))]),_:1},8,["index"])):(o(),u(j,{key:1,index:String(i.routes.name),onClick:l[0]||(l[0]=S=>e(z).push({name:i.routes.name}))},{title:p(()=>[_("span",{class:C(["text-[14px]",{"font-bold":n.level===1," ml-[20px]":e(t).icon&&n.level!=1}])},f(e(t).title),3)]),default:p(()=>[e(h).menuIsCollapse&&n.level==1?(o(),c("div",L,[e(t).icon?(o(),u(a,{key:0,name:e(t).icon,size:"20px",class:"leading-none"},null,8,["name"])):d("",!0),_("div",O,f(e(t).short_title||e(t).title),1)])):(o(),c(y,{key:1},[e(t).icon&&n.level!=1?(o(),u(a,{key:0,name:e(t).icon,size:n.level==1?"20px":"16px",class:"absolute"},null,8,["name","size"])):d("",!0)],64))]),_:1},8,["index"]))],64)):d("",!0)}}});export{U as _};
