import{d as C,r as v,n as S,l as U,h as m,s as h,w as t,a as _,e as s,i as f,t as d,u as l,q as n,Z as j,c as D,F as L,W as O,au as T,av as $,M as q,N as G,E as I,V as K,a3 as M}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                       *//* empty css                 */import{e as W}from"./notice-9865bf12.js";const Z={class:"input-width"},z={class:"input-width"},A={class:"input-width"},H={class:"dialog-footer"},ie=C({__name:"notice-weapp",emits:["complete"],setup(J,{expose:y,emit:E}){const p=v(!1),i=v(!0),g={is_weapp:0,key:"",name:"",title:"",type:"",content:[],first:"",remark:"",tid:""},a=S({...g}),b=v(),F=U(()=>({})),x=async o=>{i.value||!o||await o.validate(async e=>{if(e){i.value=!0;const u=a;u.status=u.is_weapp,W(u).then(w=>{i.value=!1,p.value=!1,E("complete")}).catch(()=>{i.value=!1})}})};return y({showDialog:p,setFormData:async(o=null)=>{i.value=!0,Object.assign(a,g),o&&Object.keys(a).forEach(e=>{o[e]!=null&&(a[e]=o[e]),o.weapp&&o.weapp[e]!=null&&(a[e]=o.weapp[e])}),i.value=!1}}),(o,e)=>{const u=T,w=$,c=q,k=G,V=I,B=K,N=M;return m(),h(B,{modelValue:p.value,"onUpdate:modelValue":e[3]||(e[3]=r=>p.value=r),title:l(n)("noticeSetting"),width:"550px","destroy-on-close":!0},{footer:t(()=>[_("span",H,[s(V,{onClick:e[1]||(e[1]=r=>p.value=!1)},{default:t(()=>[f(d(l(n)("cancel")),1)]),_:1}),s(V,{type:"primary",loading:i.value,onClick:e[2]||(e[2]=r=>x(b.value))},{default:t(()=>[f(d(l(n)("confirm")),1)]),_:1},8,["loading"])])]),default:t(()=>[j((m(),h(k,{model:a,"label-width":"110px",ref_key:"formRef",ref:b,rules:l(F),class:"page-form"},{default:t(()=>[s(c,{label:l(n)("status")},{default:t(()=>[s(w,{modelValue:a.is_weapp,"onUpdate:modelValue":e[0]||(e[0]=r=>a.is_weapp=r)},{default:t(()=>[s(u,{label:1},{default:t(()=>[f(d(l(n)("startUsing")),1)]),_:1}),s(u,{label:0},{default:t(()=>[f(d(l(n)("statusDeactivate")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),s(c,{label:l(n)("name")},{default:t(()=>[_("div",Z,d(a.name),1)]),_:1},8,["label"]),s(c,{label:l(n)("weappTempKey")},{default:t(()=>[_("div",z,d(a.tid),1)]),_:1},8,["label"]),s(c,{label:l(n)("content")},{default:t(()=>[_("div",A,[(m(!0),D(L,null,O(a.content,(r,R)=>(m(),D("div",{key:R},d(r[0])+"："+d(r[1]),1))),128))])]),_:1},8,["label"])]),_:1},8,["model","rules"])),[[N,i.value]])]),_:1},8,["modelValue","title"])}}});export{ie as _};
