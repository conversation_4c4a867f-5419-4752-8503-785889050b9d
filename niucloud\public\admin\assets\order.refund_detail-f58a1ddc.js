const e="订单编号",o="订单信息",r="交易流水号",s="订单类型",n="订单来源",t="收货人",d="收货人手机号",c="收货地址",a="商品信息",l="商品名称",i="价格",y="数量",m="商品总额",u="优惠金额",p="配送金额",f="订单日志",T="请输入订单编号",h="订单状态",v="请选择订单状态",g="订单类型",N="请选择订单类型",P="支付时间",R="商品",M="单价（元）/数量",D="实付金额（元）",I="买家/收货人",b="配送方式",k="开始时间",A="结束时间",F="件",S="支付方式",x="备注",G="修改地址",C="提醒",E="如果未发货，请点击同意退款给买家。",L="如果实际已发货，请主动与买家联系。",V="如果订单整体退款后，优惠券和余额会退还给买家。",_="关闭订单",j="确认收货",q="订单发货",w="请选择配送方式",z="物流公司",B="请选择物流公司",H="物流单号",J="请输入物流单号",K="请选择订单项",O="虚拟发货",Q="关闭订单后该订单将无法支付，是否确认关闭？",U="是否确认用户已经收货？",W="请选择要发货的商品",X="买家留言",Y="订单详情",Z="物流信息",$="发货时间",ee="物流公司",oe="物流单号",re="物流包裹信息",se="发货信息",ne="商品信息",te="物流信息",de="自提点名称",ce="自提点地址",ae="自提点电话",le="营业时间",ie="发货状态",ye="退款编号",me="申请时间",ue="退款金额",pe="退款原因",fe="售后信息",Te="退款状态",he="退款拒绝",ve="同意退款",ge="同意",Ne="申请金额",Pe="拒绝",Re="转账",Me="拒绝原因",De="请输入拒绝原因",Ie="确认收货",be="确定商品收到了吗？",ke="同意买家收货",Ae="退货地址",Fe="申请凭证",Se="退款描述",xe="退款金额",Ge="退款方式",Ce="请输入退款金额",Ee="请输入退货地址",Le="物流公司",Ve="物流说明",_e="暂无数据",je={orderNo:e,orderInfo:o,outTradeNo:r,orderType:s,orderForm:n,takerName:t,takerMobile:d,takerFullAddress:c,goodsDetail:a,goodsName:l,price:i,num:y,goodsMoney:m,preferentialMoney:u,deliveryMoney:p,operateLog:f,orderNoPlaceholder:T,orderStatus:h,orderStatusPlaceholder:v,orderFrom:g,orderFromPlaceholder:N,payTime:P,orderGoods:R,goodsPriceNumber:M,orderMoney:D,buyInfo:I,deliveryType:b,startDate:k,endDate:A,piece:F,payType:S,notes:x,editAddress:G,remind:C,remindTips1:E,remindTips2:L,remindTips3:V,close:_,finish:j,delivery:q,deliveryTypePlaceholder:w,company:z,companyPlaceholder:B,expressNumber:H,expressNumberPlaceholder:J,orderGoodsIdsPlaceholder:K,virtualDelivery:O,orderCloseTips:Q,orderFinishTips:U,orderGoodsPlaceholder:W,memberRemark:X,discountMoney:Y,orderDelivery:Z,devliveryTime:$,companyName:ee,logisticNo:oe,packageInfo:re,deliveryInfo:se,goodsInfo:ne,logisticInfo:te,storeName:de,storeAddress:ce,storeMobile:ae,tradeTime:le,deliveryStatusName:ie,orderRefundNo:ye,createTime:me,refundMoney:ue,refundReason:pe,afterSales:fe,refundStatus:Te,orderRefundRefuse:he,orderRefundAgree:ve,agree:ge,applyMoney:Ne,refuse:Pe,transferAccounts:Re,refuseReason:Me,shopReasonPlaceholder:De,confirmDelivery:Ie,orderDeliveryTips:be,agreeRefundDelivery:ke,refundDeliveryAddress:Ae,refundVoucher:Fe,refundRemark:Se,agreeMoney:xe,refundType:Ge,moneyPlaceholder:Ce,refundAddressPlaceholder:Ee,expressCompany:Le,expressRemark:Ve,orderInfoEmpty:_e};export{fe as afterSales,ge as agree,xe as agreeMoney,ke as agreeRefundDelivery,Ne as applyMoney,I as buyInfo,_ as close,z as company,ee as companyName,B as companyPlaceholder,Ie as confirmDelivery,me as createTime,je as default,q as delivery,se as deliveryInfo,p as deliveryMoney,ie as deliveryStatusName,b as deliveryType,w as deliveryTypePlaceholder,$ as devliveryTime,Y as discountMoney,G as editAddress,A as endDate,Le as expressCompany,H as expressNumber,J as expressNumberPlaceholder,Ve as expressRemark,j as finish,a as goodsDetail,ne as goodsInfo,m as goodsMoney,l as goodsName,M as goodsPriceNumber,te as logisticInfo,oe as logisticNo,X as memberRemark,Ce as moneyPlaceholder,x as notes,y as num,f as operateLog,Q as orderCloseTips,Z as orderDelivery,be as orderDeliveryTips,U as orderFinishTips,n as orderForm,g as orderFrom,N as orderFromPlaceholder,R as orderGoods,K as orderGoodsIdsPlaceholder,W as orderGoodsPlaceholder,o as orderInfo,_e as orderInfoEmpty,D as orderMoney,e as orderNo,T as orderNoPlaceholder,ve as orderRefundAgree,ye as orderRefundNo,he as orderRefundRefuse,h as orderStatus,v as orderStatusPlaceholder,s as orderType,r as outTradeNo,re as packageInfo,P as payTime,S as payType,F as piece,u as preferentialMoney,i as price,Ee as refundAddressPlaceholder,Ae as refundDeliveryAddress,ue as refundMoney,pe as refundReason,Se as refundRemark,Te as refundStatus,Ge as refundType,Fe as refundVoucher,Pe as refuse,Me as refuseReason,C as remind,E as remindTips1,L as remindTips2,V as remindTips3,De as shopReasonPlaceholder,k as startDate,ce as storeAddress,ae as storeMobile,de as storeName,c as takerFullAddress,d as takerMobile,t as takerName,le as tradeTime,Re as transferAccounts,O as virtualDelivery};
