import{d as F,y as N,n as z,r as h,bI as V,h as b,c as $,e as t,w as r,a as d,t as m,u as i,i as u,q as o,Z as L,s as j,bJ as I,af as M,E as R,L as U,M as q,N as J,a9 as Z,ag as A,ah as G,a2 as H,a3 as K}from"./index-30109030.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                */import"./el-form-item-4ed993c7.js";/* empty css                  */import{aq as O,ar as Q}from"./goods-e07707eb.js";import{_ as W}from"./service-edit.vue_vue_type_style_index_0_lang-6796dd31.js";/* empty css                  *//* empty css                   */import"./index-e3ceb692.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";const X={class:"main-container"},Y={class:"flex justify-between items-center"},ee={class:"text-page-title"},te={class:"mt-[10px]"},ae={class:"mt-[16px] flex justify-end"},Ue=F({__name:"service",setup(oe){const y=N().meta.title,e=z({page:1,limit:10,total:0,loading:!0,data:[],searchParam:{service_name:""}}),g=h(),s=(n=1)=>{e.loading=!0,e.page=n,O({page:e.page,limit:e.limit,...e.searchParam}).then(a=>{e.loading=!1,e.data=a.data.data,e.total=a.data.total,I(e.page,e.limit,e.searchParam)}).catch(()=>{e.loading=!1})};s(V(e.searchParam).page);const p=h(null),x=()=>{p.value.setFormData(),p.value.showDialog=!0},C=n=>{p.value.setFormData(n),p.value.showDialog=!0},k=n=>{M.confirm(o("serveDeleteTips"),o("warning"),{confirmButtonText:o("confirm"),cancelButtonText:o("cancel"),type:"warning"}).then(()=>{Q(n).then(()=>{s()}).catch(()=>{})})},w=n=>{n&&(n.resetFields(),s())};return(n,a)=>{const c=R,E=U,v=q,P=J,f=Z,_=A,D=G,S=H,T=K;return b(),$("div",X,[t(f,{class:"box-card !border-none",shadow:"never"},{default:r(()=>[d("div",Y,[d("span",ee,m(i(y)),1),t(c,{type:"primary",onClick:x},{default:r(()=>[u(m(i(o)("addServe")),1)]),_:1})]),t(f,{class:"box-card !border-none my-[10px] table-search-wrap",shadow:"never"},{default:r(()=>[t(P,{inline:!0,model:e.searchParam,ref_key:"searchFormRef",ref:g},{default:r(()=>[t(v,{label:i(o)("serviceName"),prop:"service_name"},{default:r(()=>[t(E,{modelValue:e.searchParam.service_name,"onUpdate:modelValue":a[0]||(a[0]=l=>e.searchParam.service_name=l),modelModifiers:{trim:!0},placeholder:i(o)("serviceNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),t(v,null,{default:r(()=>[t(c,{type:"primary",onClick:a[1]||(a[1]=l=>s())},{default:r(()=>[u(m(i(o)("search")),1)]),_:1}),t(c,{onClick:a[2]||(a[2]=l=>w(g.value))},{default:r(()=>[u(m(i(o)("reset")),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),d("div",te,[L((b(),j(D,{data:e.data,size:"large"},{empty:r(()=>[d("span",null,m(e.loading?"":i(o)("emptyData")),1)]),default:r(()=>[t(_,{prop:"service_name",label:i(o)("serviceName"),"min-width":"120"},null,8,["label"]),t(_,{prop:"desc",label:i(o)("desc"),"min-width":"120"},null,8,["label"]),t(_,{label:i(o)("operation"),fixed:"right",align:"right","min-width":"120"},{default:r(({row:l})=>[t(c,{type:"primary",link:"",onClick:B=>C(l)},{default:r(()=>[u(m(i(o)("edit")),1)]),_:2},1032,["onClick"]),t(c,{type:"primary",link:"",onClick:B=>k(l.service_id)},{default:r(()=>[u(m(i(o)("delete")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])),[[T,e.loading]]),d("div",ae,[t(S,{"current-page":e.page,"onUpdate:current-page":a[3]||(a[3]=l=>e.page=l),"page-size":e.limit,"onUpdate:page-size":a[4]||(a[4]=l=>e.limit=l),layout:"total, sizes, prev, pager, next, jumper",total:e.total,onSizeChange:a[5]||(a[5]=l=>s()),onCurrentChange:s},null,8,["current-page","page-size","total"])])]),t(W,{ref_key:"editServeDialog",ref:p,onComplete:s},null,512)]),_:1})])}}});export{Ue as default};
