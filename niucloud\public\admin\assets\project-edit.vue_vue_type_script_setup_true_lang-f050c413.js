import{d as F,r as m,n as S,q as e,aN as h,h as f,s as w,w as r,a as I,e as l,i as u,t as p,u as s,c as A,F as M,W as $,$ as v,L as O,M as G,a4 as T,a1 as W,ax as z,au as H,av as J,N as K,E as Q,V as X}from"./index-30109030.js";/* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  */import"./el-form-item-4ed993c7.js";import{i as Y,j as Z,a as ee}from"./project-8517d3f3.js";import te from"./index-e3ceb692.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-880b20ce.js";const ae={class:"dialog-footer"},ve=F({__name:"project-edit",emits:["complete"],setup(oe,{expose:q,emit:D}){const i=m(!1),c=m(!1),_=m(),y=m([]),t=S({project_id:"",name:"",category_id:"",target_amount:0,days:30,image:"",description:"",content:"",reward_description:"",status:0}),R={name:[{required:!0,message:e("projectNameRequired"),trigger:"blur"}],category_id:[{required:!0,message:e("categoryRequired"),trigger:"change"}],target_amount:[{required:!0,message:e("targetAmountRequired"),trigger:"blur"}],days:[{required:!0,message:e("projectDaysRequired"),trigger:"blur"}],description:[{required:!0,message:e("projectDescriptionRequired"),trigger:"blur"}],reward_description:[{required:!0,message:e("rewardDescriptionRequired"),trigger:"blur"}]},U=(n={})=>{Object.assign(t,{project_id:"",name:"",category_id:"",target_amount:0,days:30,image:"",description:"",content:"",reward_description:"",status:0},n)},x=async()=>{_.value&&await _.value.validate(async n=>{if(n){c.value=!0;try{t.project_id?(await Y(t.project_id,t),v.success(e("editSuccess"))):(await Z(t),v.success(e("addSuccess"))),i.value=!1,D("complete")}catch(a){console.error("Save project failed:",a)}finally{c.value=!1}}})},N=async()=>{try{const n=await ee({});y.value=n.data}catch(n){console.error("Load category list failed:",n)}};return h(()=>{N()}),q({showEdit:i,setFormData:U}),(n,a)=>{const V=O,d=G,k=T,C=W,b=z,g=H,P=J,L=K,j=Q,B=X;return f(),w(B,{modelValue:i.value,"onUpdate:modelValue":a[10]||(a[10]=o=>i.value=o),title:t.project_id?s(e)("editProject"):s(e)("addProject"),width:"800px","destroy-on-close":!0},{footer:r(()=>[I("span",ae,[l(j,{onClick:a[9]||(a[9]=o=>i.value=!1)},{default:r(()=>[u(p(s(e)("cancel")),1)]),_:1}),l(j,{type:"primary",loading:c.value,onClick:x},{default:r(()=>[u(p(s(e)("confirm")),1)]),_:1},8,["loading"])])]),default:r(()=>[l(L,{ref_key:"formRef",ref:_,model:t,rules:R,"label-width":"120px"},{default:r(()=>[l(d,{label:s(e)("projectName"),prop:"name"},{default:r(()=>[l(V,{modelValue:t.name,"onUpdate:modelValue":a[0]||(a[0]=o=>t.name=o),placeholder:s(e)("projectNamePlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(d,{label:s(e)("category"),prop:"category_id"},{default:r(()=>[l(C,{modelValue:t.category_id,"onUpdate:modelValue":a[1]||(a[1]=o=>t.category_id=o),placeholder:s(e)("categoryPlaceholder"),style:{width:"100%"}},{default:r(()=>[(f(!0),A(M,null,$(y.value,o=>(f(),w(k,{key:o.category_id,label:o.category_name,value:o.category_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),l(d,{label:s(e)("targetAmount"),prop:"target_amount"},{default:r(()=>[l(b,{modelValue:t.target_amount,"onUpdate:modelValue":a[2]||(a[2]=o=>t.target_amount=o),min:1,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1},8,["label"]),l(d,{label:s(e)("projectDays"),prop:"days"},{default:r(()=>[l(b,{modelValue:t.days,"onUpdate:modelValue":a[3]||(a[3]=o=>t.days=o),min:1,max:365,style:{width:"100%"}},null,8,["modelValue"])]),_:1},8,["label"]),l(d,{label:s(e)("projectImage"),prop:"image"},{default:r(()=>[l(te,{modelValue:t.image,"onUpdate:modelValue":a[4]||(a[4]=o=>t.image=o)},null,8,["modelValue"])]),_:1},8,["label"]),l(d,{label:s(e)("projectDescription"),prop:"description"},{default:r(()=>[l(V,{modelValue:t.description,"onUpdate:modelValue":a[5]||(a[5]=o=>t.description=o),type:"textarea",rows:4,placeholder:s(e)("projectDescriptionPlaceholder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(d,{label:s(e)("projectContent"),prop:"content"},{default:r(()=>[l(E,{modelValue:t.content,"onUpdate:modelValue":a[6]||(a[6]=o=>t.content=o),height:300},null,8,["modelValue"])]),_:1},8,["label"]),l(d,{label:s(e)("rewardDescription"),prop:"reward_description"},{default:r(()=>[l(E,{modelValue:t.reward_description,"onUpdate:modelValue":a[7]||(a[7]=o=>t.reward_description=o),height:200},null,8,["modelValue"])]),_:1},8,["label"]),l(d,{label:s(e)("status"),prop:"status"},{default:r(()=>[l(P,{modelValue:t.status,"onUpdate:modelValue":a[8]||(a[8]=o=>t.status=o)},{default:r(()=>[l(g,{label:0},{default:r(()=>[u(p(s(e)("statusDraft")),1)]),_:1}),l(g,{label:1},{default:r(()=>[u(p(s(e)("statusReview")),1)]),_:1}),l(g,{label:2},{default:r(()=>[u(p(s(e)("statusActive")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}});export{ve as _};
