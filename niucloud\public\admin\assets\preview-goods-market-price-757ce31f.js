import{d as r,l,h as f,c as u,x as c,u as p}from"./index-30109030.js";const s=r({__name:"preview-goods-market-price",props:{value:{type:Object,default:{}}},setup(i,{expose:a}){const e=i;l(()=>e.value);const n=l(()=>{var t="";t+=`font-size: ${e.value.fontSize}px;color: ${e.value.fontColor};line-height: ${e.value.lineHeight+e.value.fontSize}px;`,(e.value.x=="left"||e.value.x=="center"||e.value.x=="right")&&(t+=`text-align: ${e.value.x};`),e.value.weight&&(t+="font-weight: bold;"),(!e.value.fontFamily||e.value.fontFamily=="static/font/price.ttf")&&(t+="font-family: poster_price_font;");let o=document.getElementById(e.value.id);return o?t+=`width:${o.offsetWidth}px;height:${o.offsetHeight}px;`:t+=`width:${e.value.width}px;height:${e.value.height}px;`,t});return a({}),(t,o)=>(f(),u("div",{class:"overflow-hidden",style:c(p(n))},"￥1999.00",4))}}),h=Object.freeze(Object.defineProperty({__proto__:null,default:s},Symbol.toStringTag,{value:"Module"}));export{h as _};
