import{P as t}from"./index-30109030.js";function n(){return t.get("weapp/config")}function a(e){return t.put("weapp/config",e,{showSuccessMessage:!0})}function p(){return t.get("weapp/template")}function r(e){return t.put("weapp/template/sync",e,{showSuccessMessage:!0})}function i(e){return t.post("weapp/version",e,{showSuccessMessage:!0})}function u(){return t.get("weapp/preview")}function o(e){return t.get("weapp/version",{params:e})}function c(e){return t.get(`weapp/upload/${e}`)}function g(e){return t.post("applet/version",e,{showSuccessMessage:!0})}function f(e){return t.get("applet/version",{params:e})}function w(e){return t.put(`applet/version/${e.id}`,e,{showSuccessMessage:!0})}function d(e){return t.delete(`applet/version/${e}`)}function l(){return t.get("weapp/delivery/getIsTradeManaged")}function v(e){return t.put("weapp/domain",e,{showSuccessMessage:!0})}function h(e){return t.put("weapp/privacysetting",e,{showSuccessMessage:!0})}function M(){return t.get("weapp/privacysetting")}export{u as a,o as b,c,a as d,v as e,h as f,n as g,M as h,p as i,r as j,f as k,w as l,g as m,d as n,l as o,i as s};
