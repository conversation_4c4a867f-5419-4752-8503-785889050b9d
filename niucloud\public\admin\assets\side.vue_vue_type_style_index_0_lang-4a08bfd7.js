import{d as b,G as R,j as w,r as _,l as B,aN as C,bZ as p,h as t,s as f,w as l,e as u,u as h,c as F,W as M,F as N,a as V,y as O,cZ as U,U as Z,O as j,A as q}from"./index-30109030.js";/* empty css                     *//* empty css                */import"./el-tooltip-4ed993c7.js";/* empty css                  */import{_ as A}from"./menu-item.vue_vue_type_style_index_0_lang-5020e6bf.js";const D=V("div",{class:"h-[48px]"},null,-1),K=b({__name:"side",setup(G){const c=R(),r=w(),i=O(),a=r.siteInfo,m=r.routers,v=r.addonIndexRoute,s=_([]),o={};B(()=>c.website);const d=_([]);if(C(()=>{}),m.forEach(e=>{e.original_name=e.name,e.meta.addon==""?(e.children&&e.children.length&&(e.name=p(e.children)),s.value.push(e)):e.meta.addon!=""&&(a==null?void 0:a.apps.length)==1&&(a==null?void 0:a.apps[0].key)==e.meta.addon?e.children?(e.children.forEach(n=>{n.original_name=n.name,n.children&&n.children.length&&(n.name=p(n.children))}),s.value.unshift(...e.children)):s.value.unshift(e):o[e.meta.addon]=e}),(a==null?void 0:a.apps.length)>1){const e=[];a==null||a.apps.forEach(n=>{o[n.key]&&(o[n.key].name=v[n.key],e.push(o[n.key]))}),s.value.unshift(...e)}return d.value=s.value.map(e=>e.name),(e,n)=>{const y=U,x=Z,k=j,g=q;return t(),f(g,{class:"flex flex-col"},{default:l(()=>[u(k,{class:"menu-wrap"},{default:l(()=>[u(x,{class:"menu-scrollbar flex-1 h-0"},{default:l(()=>[u(y,{"default-active":h(i).name,"default-openeds":d.value,router:!0,"unique-opened":!1,collapse:h(c).menuIsCollapse},{default:l(()=>[(t(!0),F(N,null,M(s.value,(E,S)=>(t(),f(A,{routes:E,key:S},null,8,["routes"]))),128))]),_:1},8,["default-active","default-openeds","collapse"]),D]),_:1})]),_:1})]),_:1})}}});export{K as _};
