const t="名称",o="任务详情",n="任务类型",e="最后执行时间",s="开始时间",c="定时执行",r="结束时间",a="请选择任务模版",i="任务类型",p="任务周期",T="已执行次数",d="下次执行时间",m="任务命令",l="附加参数",u="最后执行结果",y="key",h="时间类型",k="执行时间",D="编辑任务",C="任务模版",x="任务周期",w="任务状态",O="是否启用",f="日",g="时",I="分",L="你确定要删除任务吗",N="添加任务",P="任务周期时间不能为空并且大于0",S="启动计划任务方式：",_="1、使用命令启动：php think workerman 如果更改了任务周期、状态、删除任务等操作后,需要重新启动下 php think workerman 确保生效",b="执行一次",j="执行日志",q={title:t,cronInfo:o,type:n,lastTime:e,startDate:s,cron:c,endDate:r,titlePlaceholder:a,typeName:i,crondType:p,count:T,nextTime:d,task:m,data:l,statusDesc:u,key:y,timeClass:h,executeTime:k,editCron:D,cronTemplate:C,cronTime:x,openStatus:w,isopen:O,day:f,hour:g,min:I,cronDeleteTips:L,addCron:N,cronTimeTips:P,cronTipsOne:S,cronTipsTwo:_,doOne:b,cronLog:j};export{N as addCron,T as count,c as cron,L as cronDeleteTips,o as cronInfo,j as cronLog,C as cronTemplate,x as cronTime,P as cronTimeTips,S as cronTipsOne,_ as cronTipsTwo,p as crondType,l as data,f as day,q as default,b as doOne,D as editCron,r as endDate,k as executeTime,g as hour,O as isopen,y as key,e as lastTime,I as min,d as nextTime,w as openStatus,s as startDate,u as statusDesc,m as task,h as timeClass,t as title,a as titlePlaceholder,n as type,i as typeName};
