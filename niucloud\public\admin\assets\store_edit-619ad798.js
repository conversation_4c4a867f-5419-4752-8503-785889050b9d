import{d as ue,y as _e,r as h,n as B,cT as L,aN as pe,cn as fe,cU as ve,l as be,q as l,R as T,aH as ge,h as _,c as y,e as d,w as n,u as r,aT as ye,Z as Q,s as E,bO as he,a as f,t as u,i as b,F as P,W as M,C as X,a6 as we,aU as Ve,a9 as xe,L as ke,M as Te,aL as Le,aZ as Ee,bN as Pe,au as Me,av as Ce,a4 as Ue,a1 as je,N as Re,E as qe,a3 as De}from"./index-30109030.js";/* empty css                   *//* empty css                  *//* empty css                *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                          *//* empty css                    */import Se from"./index-e3ceb692.js";import"./el-form-item-4ed993c7.js";/* empty css                *//* empty css                       */import{u as Ie,v as Ne,w as Be,x as Fe}from"./delivery-ef62b210.js";import{c as He,l as Ae,a as Ge}from"./qqmap-011f3cfb.js";/* empty css                        */import"./index.vue_vue_type_style_index_0_lang-28d0201e.js";/* empty css                  *//* empty css                   */import"./attachment-bca8f41b.js";import"./index.vue_vue_type_script_setup_true_lang-a160f88b.js";/* empty css                      */import"./el-tooltip-4ed993c7.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                    *//* empty css                         */import"./index.vue_vue_type_script_setup_true_lang-f3436425.js";/* empty css                   */import"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./sortable.esm-be94e56d.js";const Oe={class:"main-container"},$e={class:"text-[12px] text-[#999]"},ze=f("br",null,null,-1),Ke=f("span",{class:"mx-2"},"-",-1),Ze=["onClick"],We={class:"text-[12px] text-[#999]"},Je={class:"text-[12px] text-[#999]"},Qe={id:"container",class:"w-[800px] h-[520px] relative"},Xe={class:"fixed-footer-wrap"},Ye={class:"fixed-footer !z-[1000]"},Ht=ue({__name:"store_edit",setup(et){const Y=_e(),C=parseInt(Y.query.id),w=h(!1),g=B({province:[],city:[],district:[]}),U=h(),j=h(),R=h();L(0).then(i=>{g.province=i.data});let S="";pe(()=>{const i=document.createElement("script");fe().then(e=>{S=e.data.key,i.type="text/javascript",i.src="https://map.qq.com/api/gljs?libraries=tools,service&v=1.exp&key="+e.data.key,document.body.appendChild(i)}),i.onload=()=>{setTimeout(()=>{ae()},500)},te()});const ee=h({}),F=h({}),te=()=>{Ie().then(i=>{ee.value=i.data.week_list,F.value=i.data.time_interval_list})};let x,I;const H=h(!0),ae=()=>{const i=window.TMap,e=i.LatLng,a=new e(t.latitude,t.longitude);x=new i.Map("container",{center:a,zoom:14}),x.on("tilesloaded",()=>{H.value=!1}),I=He(x),x.on("click",s=>{x.setCenter(s.latLng),I.updateGeometries({id:"center",position:s.latLng}),A(s.latLng.lat,s.latLng.lng)}),A(a.lat,a.lng)},p=B({province_id:0,city_id:0,district_id:0}),A=(i,e)=>{Ae({mapKey:S,lat:i,lng:e}).then(({message:a,result:s})=>{a=="query ok"||a=="Success"?(t.latitude=s.location.lat,t.longitude=s.location.lng,t.address=s.formatted_addresses.recommend,ve(s.ad_info.adcode).then(({data:c})=>{p.province_id=c.province?c.province.id:0,p.city_id=c.city?c.city.id:0,p.district_id=c.district?c.district.id:0})):console.error(a,s)}).catch(a=>{console.log(a)})},G={store_id:0,store_name:"",store_desc:"",store_logo:"",store_mobile:"",province_id:0,province_name:"",city_id:0,city_name:"",district_id:0,district_name:"",address:"",full_address:"",longitude:116.39719,latitude:39.908626,trade_time:"",time_week:["1","2","3","4","5","6","0"],trade_time_json:[{start_time:"",end_time:""}],time_interval:30},t=B({...G}),le=()=>{t.trade_time_json.push({start_time:"",end_time:""})},oe=i=>{t.trade_time_json.splice(i,1)},O=i=>{const e=i.split(":");return e[0]*60*60+e[1]*60},$=i=>{let e=Math.floor(i/3600),a=Math.floor(i/60)-e*60;return e=e<10?"0"+e:e,a=a<10?"0"+a:a,e+":"+a};C&&(async(i=0)=>{w.value=!0,Object.assign(t,G);const e=await(await Ne(i)).data;Object.keys(t).forEach(a=>{e[a]!=null&&(t[a]=e[a]),a=="trade_time_json"&&Array.isArray(e[a])&&(t[a]=e[a].map(s=>({start_time:$(s.start_time),end_time:$(s.end_time)})))}),w.value=!1})(C);const z=h(),ie=be(()=>({store_name:[{required:!0,message:l("storeNamePlaceholder"),trigger:"blur"}],store_logo:[{required:!0,message:l("storeLogoPlaceholder"),trigger:"blur"}],store_mobile:[{required:!0,message:l("storeMobilePlaceholder"),trigger:"blur"}],trade_time:[{required:!0,message:l("tradeTimePlaceholder"),trigger:"blur"}],address_area:[{validator:(i,e,a)=>{t.province_id||a(new Error(l("provincePlaceholder"))),t.city_id||a(new Error(l("cityPlaceholder"))),g.district.length&&!t.district_id&&a(new Error(l("districtPlaceholder"))),a()}}],address:[{required:!0,message:l("addressPlaceholder"),trigger:"blur"}],time_week:[{required:!0,message:l("selectBusinessDays"),trigger:"change"}],trade_time_json:[{validator:(i,e,a)=>{if(!e||e.length===0)return a(new Error(l("tradeTimePlaceholderTwo")));for(let s=0;s<e.length;s++){const c=e[s];if(!c.start_time||!c.end_time)return a(new Error(l("tradeTimePlaceholderTwo")));if(c.end_time<=c.start_time)return a(new Error(l("tradeTimePlaceholderFour")));if(s>0&&e[s].start_time<e[s-1].end_time)return a(new Error(l("tradeTimePlaceholderFive")))}a()},trigger:"change",required:!0}],time_interval:[{required:!0,message:l("tradeTimePlaceholderThree"),trigger:"change"}]}));T(()=>t.province_id,i=>{i?L(t.province_id).then(e=>{g.city=e.data;const a=t.city_id;if(a){let s=!1;for(let c=0;c<e.data.length;c++)if(a==e.data[c].id){s=!0;break}if(s){t.city_id=a;return}}t.city_id=0,q()}):t.city_id=0}),T(()=>t.city_id,i=>{i?L(t.city_id).then(e=>{g.district=e.data;const a=t.district_id;if(a){let s=!1;for(let c=0;c<e.data.length;c++)if(a==e.data[c].id){s=!0;break}if(s){t.district_id=a;return}}q(),t.district_id=0}):t.district_id=0}),T(()=>t.district_id,i=>{i&&q()});const q=ge(()=>{setTimeout(()=>{const i=[t.province_id?U.value.states.selectedLabel:"",t.city_id?j.value.states.selectedLabel:"",t.district_id?R.value.states.selectedLabel:"",t.address];Ge({mapKey:S,address:i.join("")}).then(({message:e,result:a})=>{if(e=="Success"||e=="query ok"){const s=new window.TMap.LatLng(a.location.lat,a.location.lng);x.setCenter(s),I.updateGeometries({id:"center",position:s}),t.latitude=a.location.lat,t.longitude=a.location.lng}else console.error(e,a)})},500)},500);T(()=>p.province_id,i=>{i&&L(p.province_id).then(e=>{g.city=e.data,t.province_id=p.province_id,t.city_id=p.city_id})}),T(()=>p.city_id,i=>{i&&L(p.city_id).then(e=>{g.district=e.data,t.city_id=p.city_id,t.district_id=p.district_id})}),T(()=>p.district_id,i=>{i&&(t.district_id=p.district_id)});const re=async i=>{w.value||!i||await i.validate(async e=>{if(e){w.value=!0;const a=we(t);t.province_name=t.province_id?U.value.states.selectedLabel:"",t.city_name=t.city_id?j.value.states.selectedLabel:"",t.district_name=t.district_id?R.value.states.selectedLabel:"";const s=[a.province_id?U.value.states.selectedLabel:"",a.city_id?j.value.states.selectedLabel:"",a.district_id?R.value.states.selectedLabel:"",a.address];a.full_address=s.join(""),a.trade_time_json=t.trade_time_json.map(m=>({start_time:m.start_time?O(m.start_time):null,end_time:m.end_time?O(m.end_time):null})),(C?Be:Fe)(a).then(m=>{w.value=!1,history.back()}).catch(()=>{w.value=!1})}})},K=()=>{history.back()};return(i,e)=>{const a=Ve,s=xe,c=ke,m=Te,de=Se,V=Le,se=Ee,Z=Pe,ne=Me,ce=Ce,k=Ue,N=je,me=Re,W=qe,J=De;return _(),y("div",Oe,[d(s,{class:"card !border-none",shadow:"never"},{default:n(()=>[d(a,{content:r(C)?r(l)("updateStore"):r(l)("addStore"),icon:r(ye),onBack:K},null,8,["content","icon"])]),_:1}),Q((_(),E(s,{class:"box-card mt-[15px] !border-none",shadow:"never"},{default:n(()=>[d(me,{model:t,"label-width":"140px",ref_key:"formRef",ref:z,rules:r(ie),class:"page-form"},{default:n(()=>[d(m,{label:r(l)("storeName"),prop:"store_name"},{default:n(()=>[d(c,{modelValue:t.store_name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.store_name=o),modelModifiers:{trim:!0},clearable:"",placeholder:r(l)("storeNamePlaceholder"),class:"input-width",maxlength:"30"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(m,{label:r(l)("storeDesc")},{default:n(()=>[d(c,{modelValue:t.store_desc,"onUpdate:modelValue":e[1]||(e[1]=o=>t.store_desc=o),modelModifiers:{trim:!0},type:"textarea",rows:"4",clearable:"",placeholder:r(l)("storeDescPlaceholder"),class:"input-width",maxlength:"200"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(m,{label:r(l)("storeLogo")},{default:n(()=>[d(de,{modelValue:t.store_logo,"onUpdate:modelValue":e[2]||(e[2]=o=>t.store_logo=o)},null,8,["modelValue"])]),_:1},8,["label"]),d(m,{label:r(l)("storeMobile"),prop:"store_mobile"},{default:n(()=>[d(c,{modelValue:t.store_mobile,"onUpdate:modelValue":e[3]||(e[3]=o=>t.store_mobile=o),modelModifiers:{trim:!0},clearable:"",placeholder:r(l)("storeMobilePlaceholder"),class:"input-width",onKeyup:e[4]||(e[4]=o=>r(he)(o)),onBlur:e[5]||(e[5]=o=>t.store_mobile=o.target.value),maxlength:"11"},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),d(m,{label:r(l)("tradeTime"),prop:"trade_time"},{default:n(()=>[f("div",null,[d(c,{modelValue:t.trade_time,"onUpdate:modelValue":e[6]||(e[6]=o=>t.trade_time=o),modelModifiers:{trim:!0},clearable:"",placeholder:r(l)("tradeTimePlaceholder"),class:"input-width"},null,8,["modelValue","placeholder"]),f("p",$e,u(r(l)("tradeTimeTips")),1)])]),_:1},8,["label"]),d(m,{label:r(l)("storeDate"),prop:"time_week"},{default:n(()=>[d(se,{modelValue:t.time_week,"onUpdate:modelValue":e[7]||(e[7]=o=>t.time_week=o)},{default:n(()=>[d(V,{label:"1"},{default:n(()=>[b(u(r(l)("monday")),1)]),_:1}),d(V,{label:"2"},{default:n(()=>[b(u(r(l)("tuesday")),1)]),_:1}),d(V,{label:"3"},{default:n(()=>[b(u(r(l)("wednesday")),1)]),_:1}),d(V,{label:"4"},{default:n(()=>[b(u(r(l)("thursday")),1)]),_:1}),d(V,{label:"5"},{default:n(()=>[b(u(r(l)("friday")),1)]),_:1}),d(V,{label:"6"},{default:n(()=>[b(u(r(l)("saturday")),1)]),_:1}),d(V,{label:"0"},{default:n(()=>[b(u(r(l)("sunday")),1)]),_:1}),ze]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(m,{label:r(l)("storeTime"),prop:"trade_time_json"},{default:n(()=>[f("div",null,[f("div",null,[(_(!0),y(P,null,M(t.trade_time_json,(o,v)=>(_(),y("div",{key:v,class:"mb-3"},[d(Z,{modelValue:o.start_time,"onUpdate:modelValue":D=>o.start_time=D,placeholder:r(l)("startTime"),format:"HH:mm","value-format":"HH:mm","picker-options":{selectableRange:"00:00 - 23:59"}},null,8,["modelValue","onUpdate:modelValue","placeholder"]),Ke,d(Z,{modelValue:o.end_time,"onUpdate:modelValue":D=>o.end_time=D,placeholder:r(l)("endTime"),format:"HH:mm","value-format":"HH:mm","picker-options":{selectableRange:"00:00 - 23:59"}},null,8,["modelValue","onUpdate:modelValue","placeholder"]),v>0?(_(),y("span",{key:0,class:"text-primary cursor-pointer ml-[10px]",onClick:D=>oe(v)},u(r(l)("delete")),9,Ze)):X("",!0)]))),128)),t.trade_time_json.length<3?(_(),y("span",{key:0,class:"text-primary cursor-pointer mr-[10px]",onClick:le},u(r(l)("addTimeRange")),1)):X("",!0)]),f("div",We,u(r(l)("storeDateTips")),1)])]),_:1},8,["label"]),d(m,{label:r(l)("storeTimeInterval"),prop:"time_interval"},{default:n(()=>[f("div",null,[d(ce,{modelValue:t.time_interval,"onUpdate:modelValue":e[8]||(e[8]=o=>t.time_interval=o)},{default:n(()=>[(_(!0),y(P,null,M(F.value,(o,v)=>(_(),E(ne,{key:v,label:o.type},{default:n(()=>[b(u(o.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),f("p",Je,u(r(l)("storeTimeIntervalTips")),1)])]),_:1},8,["label"]),d(m,{label:r(l)("storeAddress"),prop:"address_area"},{default:n(()=>[d(N,{modelValue:t.province_id,"onUpdate:modelValue":e[9]||(e[9]=o=>t.province_id=o),"value-key":"id",clearable:"",class:"w-[200px]",ref_key:"provinceRef",ref:U},{default:n(()=>[d(k,{label:r(l)("provincePlaceholder"),value:0},null,8,["label"]),(_(!0),y(P,null,M(g.province,(o,v)=>(_(),E(k,{key:v,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),d(N,{modelValue:t.city_id,"onUpdate:modelValue":e[10]||(e[10]=o=>t.city_id=o),"value-key":"id",clearable:"",class:"w-[200px] ml-3",ref_key:"cityRef",ref:j},{default:n(()=>[d(k,{label:r(l)("cityPlaceholder"),value:0},null,8,["label"]),(_(!0),y(P,null,M(g.city,(o,v)=>(_(),E(k,{key:v,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),d(N,{modelValue:t.district_id,"onUpdate:modelValue":e[11]||(e[11]=o=>t.district_id=o),"value-key":"id",clearable:"",class:"w-[200px] ml-3",ref_key:"districtRef",ref:R},{default:n(()=>[d(k,{label:r(l)("districtPlaceholder"),value:0},null,8,["label"]),(_(!0),y(P,null,M(g.district,(o,v)=>(_(),E(k,{key:v,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),d(m,{prop:"address"},{default:n(()=>[d(c,{modelValue:t.address,"onUpdate:modelValue":e[12]||(e[12]=o=>t.address=o),modelModifiers:{trim:!0},clearable:"",placeholder:r(l)("addressPlaceholder"),onInput:e[13]||(e[13]=o=>r(q)()),class:"input-width"},null,8,["modelValue","placeholder"])]),_:1}),d(m,null,{default:n(()=>[Q(f("div",Qe,null,512),[[J,H.value]])]),_:1})]),_:1},8,["model","rules"])]),_:1})),[[J,w.value]]),f("div",Xe,[f("div",Ye,[d(W,{type:"primary",onClick:e[14]||(e[14]=o=>re(z.value))},{default:n(()=>[b(u(r(l)("save")),1)]),_:1}),d(W,{onClick:e[15]||(e[15]=o=>K())},{default:n(()=>[b(u(r(l)("cancel")),1)]),_:1})])])])}}});export{Ht as default};
