import{d as M,r as _,n as O,l as P,h,s as U,w as p,a as s,e as v,i as $,c as y,F as W,W as X,v as S,t as q,X as D,C as A,x as k,u as G,a6 as m,E as H,V as J,p as K,g as Q}from"./index-30109030.js";/* empty css                  *//* empty css                   *//* empty css                  */import{d as Y,s as Z,c as I}from"./diy-aea57979.js";import{_ as ee}from"./edit-theme.vue_vue_type_script_setup_true_lang-9344cd0b.js";import{_ as te}from"./_plugin-vue_export-helper-c27b6911.js";const f=d=>(K("data-v-eafd2eba"),d=d(),Q(),d),oe={class:"flex flex-col items-baseline"},le={class:"flex items-center flex-wrap max-h-[365px] overflow-auto [&>:nth-child(3n)]:mr-0"},ne=["onClick"],se={class:"flex justify-between pb-[5px]"},ae=["onClick"],ie=["onClick"],de={class:"flex"},re=f(()=>s("span",null,"主色调",-1)),ce=[re],_e={class:"flex flex-col"},he=f(()=>s("span",null,"辅色",-1)),pe=[he],me=f(()=>s("span",null,"配色",-1)),fe=[me],ue=f(()=>s("span",{class:"text-[14px] text-[var(--el-color-primary)]"},"新增配色",-1)),xe=[ue],ve={class:"dialog-footer"},ye=M({__name:"theme-list",emits:["confirm"],setup(d,{expose:F,emit:b}){const w=_(),a=_(!1);let i=!1;const t=O({title:"",id:"",theme:{},default_theme:{},new_theme:[],addon_title:"",key:""}),u=_([]),x=(e,o="")=>{I({addon:e.key}).then(n=>{u.value=n.data||[],o&&o(n.data[n.data.length-1])})},g=_(""),B=e=>{g.value=e.id,x(e),i=!1,t.title=e.title,t.id=e.id,t.theme=e.theme,t.addon_title=e.addon_title,t.key=e.key,a.value=!0},E=P(()=>`选择${t.addon_title}配色`),j=(e={})=>{t.title=e.title,t.id=e.id,t.theme=e.theme,t.default_theme=e.default_theme,t.new_theme=e.new_theme},C=(e="add",o={})=>{const n={default_theme:{},theme:{},title:"",id:"",new_theme:[],key:"",theme_field:""};e=="edit"&&(n.title=o.title,n.theme=m(o.theme)||{},n.id=o.id,n.default_theme=m(o.default_theme)||"",n.new_theme=m(o.new_theme)||[],n.new_theme=m(o.new_theme)||[]),n.key=t.key,u.value.forEach((c,l)=>{c.id==t.id&&(n.theme_field=c.theme_field)}),w.value.open(n)},N=e=>{x(t,o=>{t.new_theme=e.new_theme,t.theme=e.theme,t.title=e.title,t.id=e.id||o.id})};let r=!1;const R=e=>{if(r)return!1;r=!0;const o=e.id;Y(o).then(n=>{x(t),r=!1}).catch(()=>{r=!1})},z=()=>{if(i)return;i=!0;const e={};e.addon=t.key,e.id=t.id,e.title=t.title,e.theme=t.theme,e.new_theme=t.new_theme,Z(e).then(o=>{i=!1,a.value=!1,b("confirm")}).catch(()=>{i=!1})},T=()=>{a.value=!1,b("confirm")};return F({dialogThemeVisible:a,open:B}),(e,o)=>{const n=H,c=J;return h(),U(c,{modelValue:a.value,"onUpdate:modelValue":o[3]||(o[3]=l=>a.value=l),title:G(E),width:"535px","align-center":"",class:"custom-theme-dialog",onClose:T},{footer:p(()=>[s("div",ve,[v(n,{onClick:o[1]||(o[1]=l=>T())},{default:p(()=>[$("取消")]),_:1}),v(n,{type:"primary",onClick:o[2]||(o[2]=l=>z())},{default:p(()=>[$("确定")]),_:1})])]),default:p(()=>[s("div",oe,[s("div",le,[(h(!0),y(W,null,X(u.value,(l,L)=>(h(),y("div",{key:L,class:S(["flex flex-col border-[1px] border-solid border-[#dcdee2] rounded-[4px] px-[10px] pt-[10px] pb-[15px] mr-[10px] cursor-pointer my-[5px]",{"!border-[var(--el-color-primary)]":t.id==l.id}]),onClick:V=>j(l)},[s("div",se,[s("div",{class:S(["text-[14px] text-[#666] max-w-[85px] whitespace-nowrap overflow-hidden text-ellipsis",{"!text-[#333]":t.id==l.id}])},q(l.title),3),s("div",null,[t.id!=l.id&&l.theme_type!="default"&&g.value!=l.id?(h(),y("span",{key:0,class:"iconfont iconshanchu-fanggaiV6xx !text-[14px] text-[#999]",onClick:D(V=>R(l),["stop"])},null,8,ae)):A("",!0),s("span",{class:"nc-iconfont nc-icon-bianjiV6xx1 !text-[14px] text-[#999] ml-[5px]",onClick:D(V=>C("edit",l),["stop"])},null,8,ie)])]),s("div",de,[s("div",{class:"w-[70px] h-[54px] pl-[7px] pt-[9px] flex flex-col mr-[4px] rounded-[4px] text-[10px] leading-[1] text-[#fff]",style:k({backgroundColor:l.theme["--primary-color"]})},ce,4),s("div",_e,[s("div",{class:"secod-color-item mb-[4px]",style:k({backgroundColor:l.theme["--primary-help-color2"]})},pe,4),s("div",{class:"secod-color-item",style:k({backgroundColor:l.theme["--primary-color-dark"]})},fe,4)])])],10,ne))),128))]),s("div",{class:"flex items-center border-[1px] border-solid border-[var(--el-color-primary)] rounded-[2px] h-[32px] px-[15px] cursor-pointer mt-[15px]",onClick:o[0]||(o[0]=l=>C())},xe)]),v(ee,{ref_key:"editThemeRef",ref:w,onConfirm:N},null,512)]),_:1},8,["modelValue","title"])}}});const ke=te(ye,[["__scopeId","data-v-eafd2eba"]]),Se=Object.freeze(Object.defineProperty({__proto__:null,default:ke},Symbol.toStringTag,{value:"Module"}));export{Se as _,ke as t};
