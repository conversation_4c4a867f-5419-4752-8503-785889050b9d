import{by as W,a6 as C,$ as T,q as p,af as w,B as H}from"./index-30109030.js";const X=W("poster",{state:()=>({contentBoxWidth:720,contentBoxHeight:1280,id:0,name:"",type:"",typeName:"",channel:"",status:1,isDefault:0,addon:"",currentIndex:-99,currentComponent:"edit-page",predefineColors:["#F4391c","#ff4500","#ff8c00","#FFD009","#ffd700","#19C650","#90ee90","#00ced1","#1e90ff","#c71585","#FF407E","#CFAF70","#A253FF","rgba(255, 69, 0, 0.68)","rgb(255, 120, 0)","hsl(181, 100%, 37%)","hsla(209, 100%, 56%, 0.73)","#c7158577"],components:[],global:{width:720,height:1280,bgType:"url",bgColor:"#ffffff",bgUrl:""},template:{width:200,height:200,minWidth:60,minHeight:60,x:0,y:0,angle:0,zIndex:0},templateType:{text:{height:60,minWidth:120,minHeight:44,fontFamily:"static/font/SourceHanSansCN-Regular.ttf",fontSize:40,weight:!1,lineHeight:10,fontColor:"#303133"},image:{shape:"normal"},qrcode:{},draw:{draw_type:"Polygon",points:[[0,1210],[720,1210],[720,1280],[0,1280]],bgColor:"#eeeeee"}},value:[]}),getters:{editComponent:e=>e.currentIndex==-99?e.global:e.value[e.currentIndex]},actions:{init(){this.global={width:720,height:1280,bgType:"url",bgColor:"#ffffff",bgUrl:""},this.value=[]},addComponent(e,f){let h=C(f);h.id=this.generateRandom(),h.componentName=e,h.componentTitle=h.title,delete h.title,delete h.icon;let r=C(this.template);Object.assign(h,r);let o=C(this.templateType);if(Object.assign(h,o[h.type]),h.template&&(Object.assign(h,h.template),delete h.template),!this.checkComponentIsAdd(h)){T({type:"warning",message:`${h.componentTitle}${p("componentCanOnlyAdd")}${h.uses}${p("piece")}`});return}h.zIndex=this.value.length+1,this.value.push(h),this.currentIndex=this.value.length-1,this.currentComponent="edit-"+h.path},generateRandom(e=5){return Number(Math.random().toString().substr(3,e)+Date.now()).toString(36)},changeCurrentIndex(e,f=null){this.currentIndex=e,this.currentIndex==-99?this.currentComponent="edit-page":f&&(this.currentComponent="edit-"+f.path)},delComponent(){this.currentIndex!=-99&&w.confirm(p("delComponentTips"),p("warning"),{confirmButtonText:p("confirm"),cancelButtonText:p("cancel"),type:"warning",autofocus:!1}).then(()=>{this.value.splice(this.currentIndex,1),this.value.length===0&&(this.currentIndex=-99),this.currentIndex===this.value.length&&this.currentIndex--;let e=C(this.value[this.currentIndex]);this.changeCurrentIndex(this.currentIndex,e)}).catch(()=>{})},moveUpComponent(){this.currentIndex<-1||(this.value[this.currentIndex].zIndex++,this.value[this.currentIndex].zIndex>=this.value.length&&(this.value[this.currentIndex].zIndex=this.value.length))},moveDownComponent(){this.currentIndex<-1||(this.value[this.currentIndex].zIndex--,this.value[this.currentIndex].zIndex<0&&(this.value[this.currentIndex].zIndex=0))},copyComponent(){if(this.currentIndex<0)return;let e=C(this.value[this.currentIndex]);if(e.id=this.generateRandom(),e.x=0,e.y=0,!this.checkComponentIsAdd(e)){T({type:"warning",message:`${p("notCopy")}，${e.componentTitle}${p("componentCanOnlyAdd")}${e.uses}${p("piece")}`});return}var f=this.currentIndex+1;this.value.splice(f,0,e),this.changeCurrentIndex(f,e)},checkComponentIsAdd(e){if(e.uses===0)return!0;var f=0;for(var h in this.value)this.value[h].componentName===e.componentName&&f++;return!(f>=e.uses)},resetComponent(){this.currentIndex<0||w.confirm(p("resetComponentTips"),p("warning"),{confirmButtonText:p("confirm"),cancelButtonText:p("cancel"),type:"warning",autofocus:!1}).then(()=>{for(let e=0;e<this.components.length;e++)if(this.components[e].componentName==this.editComponent.componentName){Object.assign(this.editComponent,this.components[e]);let f=C(this.templateType);Object.assign(this.editComponent,f[this.editComponent.type]),this.editComponent.angle=0;break}}).catch(()=>{})},verify(){if(this.name==="")return T({message:p("posterNamePlaceholder"),type:"warning"}),this.changeCurrentIndex(-99),!1;if(this.value.length==0)return T({message:p("diyPosterValueEmptyTips"),type:"warning"}),this.changeCurrentIndex(-99),!1;for(var e=0;e<this.value.length;e++)try{if(this.value[e].verify){var f=this.value[e].verify(e);if(!f.code)return this.changeCurrentIndex(e,this.value[e]),T({message:f.message,type:"warning"}),!1}}catch(h){console.log("verify Error:",h,e,this.value[e])}return!0},mouseDown(e,f,h){const r=document.getElementById(f),o=e.clientX*2-r.offsetLeft,b=e.clientY*2-r.offsetTop;document.onmousemove=y=>{let v=y.clientX*2,u=y.clientY*2;this.contentBoxWidth==r.offsetWidth?r.style.left=0:r.style.left=v-o+"px",r.style.top=u-b+"px",v-o<0&&(r.style.left=0),v-o>this.contentBoxWidth-r.offsetWidth&&(r.style.left=this.contentBoxWidth-r.offsetWidth+"px"),u-b<0&&(r.style.top=0),u-b>this.contentBoxHeight-r.offsetHeight&&(r.style.top=this.contentBoxHeight-r.offsetHeight+"px"),this.value[h].x=r.offsetLeft,this.value[h].y=r.offsetTop},document.onmouseup=y=>{document.onmousemove=null}},resizeMouseDown(e,f,h){const r=e;r.stopPropagation();const o=document.getElementById(f.id),b=e.target.className,y=o.offsetWidth,v=o.offsetHeight,u=r.clientX,I=r.clientY,d=o.offsetLeft,c=o.offsetTop;let i=100,s=100;f.type=="text"?(i=60,s=22):f.type=="image"||f.type=="qrcode"?(i=30,s=30):f.type=="draw"&&(i=20,s=20),document.onmousemove=B=>{const a=B;if(b=="box1"){let t=y-(a.clientX-u);const m=this.contentBoxWidth;let n=v-(a.clientY-I);const x=this.contentBoxHeight-c;let l=d+(a.clientX-u),g=c+(a.clientY-I);t<i&&(t=i),t>m&&(t=m),n<s&&(n=s),n>x&&(n=x),d==0&&c==0?t==i&&n==s?(l=i,g=s):t==i&&n>s?l=i:t>i&&n==s&&(g=s):d==0&&c>0?t==i&&n==s||t==i&&n>s?(l=i,g=o.offsetTop):t>i&&n==s&&(g=o.offsetTop):d>0&&c==0?t==i&&n==s?(l=o.offsetLeft,g=o.offsetTop):t==i&&n>s?(l=o.offsetLeft,g=0):t>i&&n==s&&(g=o.offsetTop):d>0&&c>0&&(t==i&&n==s||t==i&&n>s?(l=o.offsetLeft,g=o.offsetTop):t>i&&n==s&&(g=o.offsetTop)),l<0&&(l=0,t=y-(a.clientX-u)+(d+(a.clientX-u))),g<0&&(g=0,n=c+(a.clientY-I)+(v-(a.clientY-I))),o.children[0].style.width=t+"px",(f.type=="text"||f.type=="draw")&&(o.children[0].style.height=n+"px"),o.style.left=l+"px",o.style.top=g+"px"}else if(b=="box2"){let t=y+(a.clientX-u);const m=this.contentBoxWidth-d;let n=v-(a.clientY-I);const x=this.contentBoxHeight-c;let l=c+(a.clientY-I);t<i&&(t=i),t>m&&(t=m),n<s&&(n=s),n>x&&(n=x),d==0&&c==0?t==i&&n==s?l=s:t==i&&n>s||t>i&&n==s&&(l=s):d==0&&c>0?(t==i&&n==s||t==i&&n>s||t>i&&n==s)&&(l=o.offsetTop):d>0&&c==0?t==i&&n==s?l=o.offsetTop:t==i&&n>s?l=0:t>i&&n==s&&(l=o.offsetTop):d>0&&c>0&&(t==i&&n==s||t==i&&n>s||t>i&&n==s)&&(l=o.offsetTop),l<0&&(l=0,n=c+(a.clientY-I)+(v-(a.clientY-I))),o.children[0].style.width=t+"px",(f.type=="text"||f.type=="draw")&&(o.children[0].style.height=n+"px"),o.style.top=l+"px"}else if(b=="box3"){let t=y-(a.clientX-u);const m=this.contentBoxWidth;let n=v+(a.clientY-I);const x=this.contentBoxHeight-c;let l=d+(a.clientX-u);t<i&&(t=i),t>m&&(t=m),n<s&&(n=s),n>x&&(n=x),d==0&&c==0||d==0&&c>0?(t==i&&n==s||t==i&&n>s)&&(l=i):d>0&&c==0?(t==i&&n==s||t==i&&n>s)&&(l=o.offsetLeft):d>0&&c>0&&(t==i&&n==s||t==i&&n>s)&&(l=o.offsetLeft),l<0&&(l=0,t=y-(a.clientX-u)+(d+(a.clientX-u))),o.children[0].style.width=t+"px",(f.type=="text"||f.type=="draw")&&(o.children[0].style.height=n+"px"),o.style.left=l+"px"}else if(b=="box4"){let t=y+(a.clientX-u);const m=this.contentBoxWidth-d;let n=v+(a.clientY-I);const x=this.contentBoxHeight-c;t<i&&(t=i),t>m&&(t=m),n<s&&(n=s),n>x&&(n=x),o.children[0].style.width=t+"px",(f.type=="text"||f.type=="draw")&&(o.children[0].style.height=n+"px")}this.value[h].x=o.offsetLeft,this.value[h].y=o.offsetTop,this.value[h].width=parseInt(o.children[0].style.width.replace("px",""))},document.onmouseup=()=>{document.onmousemove=null,document.onmouseup=null}},getGlobalStyle(){let e="";return this.global.bgType=="color"?e+=`background-color:${this.global.bgColor};`:this.global.bgType=="url"&&this.global.bgUrl&&(e+=`background-image:url("${H(this.global.bgUrl)}")`),e},getMaxX(){const e=document.getElementById(this.editComponent.id);let f=this.contentBoxWidth;return e&&(f-=e.offsetWidth),f},getMaxY(){const e=document.getElementById(this.editComponent.id);let f=this.contentBoxHeight;return e&&(f-=e.offsetHeight),f},getMaxWidth(){let e=this.contentBoxWidth;return e-=this.editComponent.x,e}}}),L=X;export{L as u};
