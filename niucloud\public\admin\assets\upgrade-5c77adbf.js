import{P as r}from"./index-30109030.js";function a(e=""){return r.get(e?`upgrade/${e}`:"upgrade")}function s(){return r.get("upgrade/task")}function c(e="",t={}){return r.post(e?`upgrade/${e}`:"upgrade",t)}function n(){return r.post("upgrade/execute",{},{showErrorMessage:!1})}function p(e=""){return r.get(e?`upgrade/check/${e}`:"upgrade/check")}function o(){return r.post("upgrade/clear")}function g(e){return r.post(`upgrade/operate/${e}`)}function d(e){return r.get("upgrade/records",{params:e})}function i(e){return r.delete("upgrade/records",{params:e})}function k(e){return r.get("backup/records",{params:e})}function f(e){return r.put("backup/remark",e,{showSuccessMessage:!0})}function h(e){return r.post("backup/check_dir",e)}function l(e){return r.post("backup/check_permission",e)}function m(e){return r.post("backup/restore",e)}function U(e){return r.post("backup/delete",e,{showSuccessMessage:!0})}function b(e){return r.post("backup/manual",e)}export{a,g as b,o as c,k as d,n as e,h as f,s as g,l as h,f as i,U as j,d as k,i as l,b as m,p,m as r,c as u};
