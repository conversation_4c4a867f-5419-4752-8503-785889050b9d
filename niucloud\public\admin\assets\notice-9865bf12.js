import{P as e}from"./index-30109030.js";function i(t){return e.get("notice/notice",{params:t})}function u(t){return e.get("notice/log",{params:t})}function c(t){return e.post("notice/notice/editstatus",t,{showSuccessMessage:!0})}function r(t){return e.post("notice/notice/edit",t,{showSuccessMessage:!0})}function a(){return e.get("notice/notice/sms")}function g(t){return e.get(`notice/notice/sms/${t}`)}function m(t){return e.put(`notice/notice/sms/${t.sms_type}`,t,{showSuccessMessage:!0})}function f(t){return e.get("notice/sms/log",{params:t})}function p(){return e.get("notice/niusms/config")}function d(t){return e.post("notice/niusms/account/login",t,{showSuccessMessage:!0})}function l(t){return e.post("notice/niusms/account/register",t,{showSuccessMessage:!0})}function S(t){return e.get(`notice/niusms/account/info/${t}`)}function $(t){return e.get(`notice/niusms/template/list/${t.sms_type}/${t.username}`,{})}function h(t,s){return e.get(`notice/niusms/sign/list/${t}`,{params:s})}function w(t,s){return e.post(`notice/niusms/sign/report/${t}`,s,{showSuccessMessage:!0})}function M(t,s){return e.post(`notice/niusms/sign/delete/${t}`,s,{showSuccessMessage:!0})}function L(t,s){return e.post(`notice/niusms/account/edit/${t}`,s,{showSuccessMessage:!0})}function y(t,s){return e.get(`notice/niusms/account/send_list/${t}`,{params:s})}function I(t,s){return e.get(`notice/niusms/order/list/${t}`,{params:s})}function A(){return e.get("notice/niusms/packages")}function O(){return e.get("notice/niusms/captcha")}function P(t){return e.post("notice/niusms/send",t,{showSuccessMessage:!0})}function C(){return e.get("notice/niusms/sign/report/config")}function N(){return e.get("notice/niusms/template/report/config")}function T(t,s,n){return e.post(`notice/niusms/template/report/${t}/${s}`,n,{showSuccessMessage:!0})}function b(t,s,n){return e.get(`notice/niusms/template/info/${t}/${s}`,{params:n})}function k(t,s){return e.post(`notice/niusms/order/create/${t}`,s)}function _(t,s){return e.get(`notice/niusms/order/pay/${t}`,{params:s})}function q(t,s){return e.get(`notice/niusms/order/info/${t}`,{params:s})}function x(t,s){return e.get(`notice/niusms/order/status/${t}`,{params:s})}function j(t,s){return e.post(`notice/niusms/order/calculate/${t}`,s)}function v(t){return e.put("notice/niusms/enable",t,{showSuccessMessage:!0})}function z(t,s){return e.get(`notice/niusms/template/sync/${t}/${s}`)}function B(t,s){return e.post(`notice/niusms/account/reset/password/${t}`,s,{showSuccessMessage:!0})}function D(t,s){return e.delete(`notice/niusms/template/${t}/${s}`)}export{i as A,u as B,a as C,p as D,S as E,v as F,L as G,x as H,f as I,r as a,m as b,C as c,O as d,c as e,P as f,g,B as h,A as i,j,_ as k,d as l,I as m,q as n,y as o,w as p,h as q,l as r,k as s,M as t,$ as u,z as v,D as w,N as x,b as y,T as z};
