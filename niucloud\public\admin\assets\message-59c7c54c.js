import{d as v,r as d,h as o,c as l,e as a,w as m,a as e,C as g,F as y,a8 as w,p as k,g as F}from"./index-30109030.js";/* empty css                  *//* empty css                   */import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-8d60d0a2.js";import{_ as C,a as I}from"./message_empty-a58cfc74.js";import B from"./index-2c5e161c.js";import{_ as j}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                  *//* empty css                     *//* empty css               */import"./dark-63c0c649.js";const i=s=>(k("data-v-cd540765"),s=s(),F(),s),D={class:"relative"},N={key:0,class:"absolute top-[-3px] right-[-5px] w-[12px] h-[12px] rounded-full bg-[#DA203E] text-white text-[12px] flex justify-center items-center"},S={key:0,class:"flex items-center bg-[#F8FAFF] p-[10px] rounded-[8px]"},A=i(()=>e("div",{class:"w-[36px] h-[36px] rounded-full flex justify-center items-center"},[e("img",{src:C,alt:"",class:"w-[36px] h-[36px]"})],-1)),E={class:"py-[3px] ml-[5px] flex-1"},P=i(()=>e("div",{class:"text-[16px] font-bold text-[#1D1F3A] mb-[5px]"},"云编译",-1)),R={class:"text-[12px] text-[#4F516D] flex justify-between items-center"},V=i(()=>e("span",null,"有正在执行的编译任务",-1)),T={key:1,class:"flex items-center justify-center"},$=i(()=>e("img",{src:I,alt:""},null,-1)),q=[$],z=v({__name:"message",setup(s){const r=d(null),c=d(!1);let t=null;const u=()=>{t||(t=setInterval(()=>{const n=localStorage.getItem("cloud_build_task");c.value=!!n,n||f()},1e3))},f=()=>{t&&(clearInterval(t),t=null)};return u(),(n,p)=>{const x=b,h=w;return o(),l(y,null,[a(h,{class:"box-item",width:322},{reference:m(()=>[e("div",D,[a(x,{name:"iconfont iconFramec-1"}),c.value?(o(),l("span",N,"1")):g("",!0)])]),default:m(()=>[c.value?(o(),l("div",S,[A,e("div",E,[P,e("div",R,[V,e("span",{class:"text-primary cursor-pointer ml-auto",onClick:p[0]||(p[0]=G=>{var _;return(_=r.value)==null?void 0:_.elNotificationClick()})},"点击查看")])])])):(o(),l("div",T,q))]),_:1}),a(B,{ref_key:"cloudBuildRef",ref:r},null,512)],64)}}});const oe=j(z,[["__scopeId","data-v-cd540765"]]);export{oe as default};
